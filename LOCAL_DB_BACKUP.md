# Local Database Backup

Simple ad-hoc backup and restore for your local Supabase database following [Supabase's official best practices](https://supabase.com/docs/guides/platform/migrating-within-supabase/backup-restore#backup-database-using-the-cli).

## Commands

### Backup

```bash
pnpm backup-local-db
```

Creates a complete backup of your local database with separate files for roles, schema, and data.

### Restore

```bash
# List available backups
pnpm restore-local-db

# Restore from specific backup
pnpm restore-local-db <timestamp>

# Or use the auto-generated restore script
./supabase/backups/local_db_<branch>_<timestamp>_restore.sh
```

## Example Usage

```bash
# Take a backup before making changes
pnpm backup-local-db

# Make your changes...

# If something goes wrong, restore
pnpm restore-local-db
# (shows available backups, pick one)
```

## Storage & Files

Backups are stored in organized subfolders within `supabase/backups/` and automatically excluded from git commits.

### New Subfolder Structure (Recommended)

Each backup creates its own subfolder with full descriptive filenames inside:

```
supabase/backups/local_db_security-review_20250724_105138/
├── local_db_security-review_20250724_105138_roles.sql     (Database roles)
├── local_db_security-review_20250724_105138_schema.sql    (Database schema)
├── local_db_security-review_20250724_105138_data.sql      (Database data)
├── local_db_security-review_20250724_105138_restore.sh    (Auto-restore script)
└── local_db_security-review_20250724_105138.info         (Backup metadata)
```

### Benefits of Subfolder Organization:

- **Clean directory**: No more cluttered backup folder
- **Complete isolation**: Each backup is self-contained
- **Clear identification**: Full descriptive names maintained
- **Easy management**: Delete entire backup by removing folder

### Legacy Support

The system still supports older backups stored directly in the main directory (marked as "legacy" in listings).

## Features

✅ **Follows Supabase best practices** - Uses official CLI approach  
✅ **Efficient data backup** - Uses `--use-copy` for faster backups/restores  
✅ **Single transaction restore** - Atomic operations with `--single-transaction`  
✅ **Error handling** - Stops on any error with `--variable ON_ERROR_STOP=1`  
✅ **Replica mode** - Sets `session_replication_role = replica` during restore  
✅ **Smart backup listing** - Shows complete vs incomplete backups  
✅ **Auto-generated scripts** - Each backup includes its own restore script  
✅ **Branch-aware naming** - Files include git branch for easy identification  
✅ **Special character handling** - Branch names sanitized for filesystem safety

## Branch-Aware Backups

Backup files now include your git branch name for better organization:

```bash
# On 'main' branch
pnpm backup-local-db
# Creates subfolder: supabase/backups/local_db_main_20250724_104540/

# On 'feature/new-design' branch
pnpm backup-local-db
# Creates subfolder: supabase/backups/local_db_feature_new-design_20250724_104540/
```

Special characters in branch names (`/`, `\`, spaces, etc.) are automatically converted to underscores for filesystem compatibility.

Simple and robust! 🎯
