# Pages Collection Frontend UI Implementation Plan

## Overview

This plan outlines the implementation of a frontend UI for the Pages collection that follows the same 4-column grid pattern as the Articles single page, maintaining consistency with our established architecture while leveraging existing shared components.

## Architecture Analysis

### Current State
- **Pages Collection**: Dual-language support (English/German), translation controls, SEO fields, featured images, Lexical editor
- **Existing Route**: `src/app/(frontend)/pages/[slug]/page.tsx` - basic single-column layout (needs refactoring)
- **Articles Pattern**: 4-column grid with proper caching, metadata generation, and component structure
- **Shared Components**: `NewsCard`, skeleton loaders, caching utilities, Lexical content rendering

### Design Requirements
- **4-Column Grid Layout**: Consistent with articles and categories
- **Column 1**: Reserved for future use (empty for now)
- **Columns 2-3**: Page title, featured image (if present), and content
- **Column 4**: List of recently published articles (any tier)
- **Responsive Design**: Following existing breakpoint patterns
- **Caching**: Unified caching strategy following established patterns
- **Skeleton Loading**: Consistent loading states

## Implementation Plan

### Phase 1: Caching Infrastructure

#### 1.1 Create Pages Caching Module
**File**: `src/lib/cache/pages.ts`

```typescript
import { unstable_cache } from 'next/cache';
import { getPayload } from 'payload';
import config from '@/payload.config';
import type { Page } from '@/payload-types';
import { CACHE_DURATIONS, CACHE_TAGS, generateCacheKey } from './constants';

// Page fetching function
const fetchPageBySlug = async (slug: string): Promise<Page | null> => {
  const payload = await getPayload({ config });
  
  const result = await payload.find({
    collection: 'pages',
    where: { 
      and: [
        { slug: { equals: slug } }, 
        { _status: { equals: 'published' } }
      ] 
    },
    limit: 1,
    depth: 2,
  });

  return result.docs[0] || null;
};

// Cached page fetching - standardized 5 minute cache
export const getCachedPageBySlug = (slug: string) =>
  unstable_cache(async () => fetchPageBySlug(slug), ['page', slug], {
    revalidate: CACHE_DURATIONS.DEFAULT, // 5 minutes
    tags: ['pages', `page-${slug}`],
  });

// Cached published page slugs for static generation
export const getCachedPublishedPageSlugs = unstable_cache(
  async (): Promise<{ slug: string }[]> => {
    const payload = await getPayload({ config });
    
    const result = await payload.find({
      collection: 'pages',
      where: { _status: { equals: 'published' } },
      limit: 100,
      depth: 0,
    });

    return result.docs
      .filter(page => page.slug)
      .map(page => ({ slug: page.slug! }));
  },
  ['published-page-slugs'],
  {
    revalidate: CACHE_DURATIONS.DEFAULT,
    tags: ['pages', 'published-slugs'],
  }
);
```

#### 1.2 Update Cache Constants
**File**: `src/lib/cache/constants.ts`

Add pages-specific cache tags and keys:
```typescript
export const CACHE_TAGS = {
  // ... existing tags
  PAGES: 'pages',
} as const;

export const generateCacheKey = {
  // ... existing keys
  page: (slug: string) => `page-${slug}`,
  pageExists: (slug: string) => `page-exists-${slug}`,
} as const;
```

#### 1.3 Update Cache Invalidation
**File**: `src/lib/cache/invalidation.ts`

Add pages invalidation hook (already exists but ensure it's working):
```typescript
// The createPageInvalidationHook() already exists in invalidation.ts
// Ensure it's integrated in the Pages collection config
```

### Phase 2: Page Components

#### 2.1 Create Page Header Component
**File**: `src/components/pages/PageHeader.tsx`

```typescript
import type { Page } from '@/payload-types';

interface PageHeaderProps {
  page: Page;
  locale?: 'de' | 'en';
}

export default function PageHeader({ page, locale = 'de' }: PageHeaderProps) {
  // Prioritise German content first, then English, then fallback
  const title = page.germanTab?.germanTitle || 
                page.englishTab?.title || 
                page.title;

  // Format publication date in German style
  const publishDate = new Date(page.publishedAt || page.createdAt);
  const formattedDate = publishDate.toLocaleDateString('de-DE', {
    weekday: 'long',
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });

  return (
    <div className="space-y-4">
      {/* Large serif title */}
      <h1 className="text-xl md:text-2xl lg:text-3xl font-serif font-normal text-gray-900 dark:text-gray-100 leading-tight">
        {title}
      </h1>

      {/* Publication Date */}
      <div className="text-xs/4 text-gray-600 dark:text-gray-400">
        <span className="font-sans">{formattedDate}</span>
      </div>
    </div>
  );
}
```

#### 2.2 Create Page Content Component
**File**: `src/components/pages/PageContent.tsx`

```typescript
import React from 'react';
import Image from 'next/image';
import type { Page, Media } from '@/payload-types';

interface PageContentProps {
  page: Page;
  locale?: 'de' | 'en';
}

// Import existing Lexical renderer from ArticleContent
import { renderLexicalContent } from '@/components/articles/ArticleContent';

export default function PageContent({ page, locale = 'de' }: PageContentProps) {
  // Handle featuredImage which can be number (ID) or Media object
  const featuredImage = typeof page.featuredImage === 'object' 
    ? (page.featuredImage as Media) 
    : null;

  // Prioritise content: German → English → fallback
  const content = page.germanTab?.germanContent || 
                  page.englishTab?.content || 
                  null;

  // Get title for alt text fallback
  const title = page.germanTab?.germanTitle || 
                page.englishTab?.title || 
                page.title;

  return (
    <div className="space-y-6 lg:space-y-8">
      {/* Prominent hero image */}
      {featuredImage?.url && (
        <div className="overflow-hidden rounded-lg">
          <Image
            src={featuredImage.url}
            alt={featuredImage.alt || title}
            width={800}
            height={400}
            className="w-full h-auto object-cover"
            priority={true}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 66vw, 800px"
          />
        </div>
      )}

      {/* Page content with proper typography */}
      <article className="prose prose-base max-w-none dark:prose-invert">
        <div className="font-serif text-gray-800 dark:text-gray-200 text-base/8">
          {content ? (
            renderLexicalContent(content)
          ) : (
            <div className="text-gray-500 dark:text-gray-400 italic font-sans">
              <p>Content is being processed and will be available soon.</p>
            </div>
          )}
        </div>
      </article>
    </div>
  );
}
```

#### 2.3 Create Page Accessibility Navigation
**File**: `src/components/pages/PageAccessibilityNav.tsx`

```typescript
export default function PageAccessibilityNav() {
  return (
    <div className="sr-only">
      <a
        href="#page-content"
        className="skip-link focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-background focus:text-foreground focus:border focus:border-border focus:rounded"
      >
        Skip to page content
      </a>
      <a
        href="#related-articles"
        className="skip-link focus:not-sr-only focus:absolute focus:top-4 focus:left-32 focus:z-50 focus:px-4 focus:py-2 focus:bg-background focus:text-foreground focus:border focus:border-border focus:rounded"
      >
        Skip to related articles
      </a>
    </div>
  );
}
```

#### 2.4 Create Recent Articles Section
**File**: `src/components/pages/RecentArticlesSection.tsx`

```typescript
import { Suspense } from 'react';
import { getCachedTier2Articles } from '@/lib/cache/articles';
import NewsCard from '@/components/NewsCard';
import RelatedArticlesSkeleton from '@/components/articles/RelatedArticlesSkeleton';

interface RecentArticlesSectionProps {
  maxArticles?: number;
  locale?: 'de' | 'en';
}

async function RecentArticles({ maxArticles = 4, locale = 'de' }: RecentArticlesSectionProps) {
  try {
    // Use tier-2 articles as "recent articles" for pages
    const articlesData = await getCachedTier2Articles();
    const articles = articlesData.docs.slice(0, maxArticles);

    if (articles.length === 0) {
      return (
        <div className="space-y-4">
          <h3 className="font-sans font-medium text-gray-900 dark:text-gray-100 text-sm pl-4">
            Aktuelle Artikel
          </h3>
          <div className="text-gray-500 dark:text-gray-400 text-sm italic pl-4">
            Derzeit keine aktuellen Artikel verfügbar
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-2 sm:space-y-2.5 font-sans">
        <h3 className="font-sans font-medium text-gray-900 dark:text-gray-100 text-sm pl-4">
          Aktuelle Artikel
        </h3>

        <div className="space-y-1.5 md:space-y-2">
          {articles.map(article => (
            <NewsCard
              key={article.id}
              article={article}
              variant="title-only"
              showDescription={true}
              locale={locale}
            />
          ))}
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error fetching recent articles:', error);
    return (
      <div className="space-y-4">
        <h3 className="font-sans font-medium text-gray-900 dark:text-gray-100 text-sm pl-4">
          Aktuelle Artikel
        </h3>
        <div className="text-gray-500 dark:text-gray-400 text-sm italic pl-4">
          Fehler beim Laden der aktuellen Artikel
        </div>
      </div>
    );
  }
}

export default function RecentArticlesSection(props: RecentArticlesSectionProps) {
  return (
    <Suspense fallback={<RelatedArticlesSkeleton maxArticles={props.maxArticles} />}>
      <RecentArticles {...props} />
    </Suspense>
  );
}
```

### Phase 3: Refactor Page Route

#### 3.1 Update Page Route Implementation
**File**: `src/app/(frontend)/pages/[slug]/page.tsx`

```typescript
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { Suspense } from 'react';
import { draftMode } from 'next/headers';
import { getPayload } from 'payload';
import config from '@payload-config';
import { getCachedPageBySlug, getCachedPublishedPageSlugs } from '@/lib/cache/pages';
import type { Page, Media } from '@/payload-types';
import PageHeader from '@/components/pages/PageHeader';
import PageContent from '@/components/pages/PageContent';
import RecentArticlesSection from '@/components/pages/RecentArticlesSection';
import PageAccessibilityNav from '@/components/pages/PageAccessibilityNav';
import RelatedArticlesSkeleton from '@/components/articles/RelatedArticlesSkeleton';

interface PageProps {
  params: Promise<{ slug: string }>;
}

// Function to get page with draft mode support
async function getPage(slug: string, isDraftMode: boolean) {
  if (isDraftMode) {
    // Fetch draft version when in draft mode
    const payload = await getPayload({ config });
    const pages = await payload.find({
      collection: 'pages',
      where: { slug: { equals: slug } },
      draft: true,
      limit: 1,
    });
    return pages.docs[0] || null;
  } else {
    // Use cached version for published pages
    const getCachedDoc = getCachedPageBySlug(slug);
    const result = await getCachedDoc();
    return result as unknown as Page | null;
  }
}

// Generate metadata for SEO
export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const { slug } = await params;
  const draft = await draftMode();
  const isDraftMode = draft.isEnabled;

  const getCachedDoc = getCachedPageBySlug(slug);
  const result = await getCachedDoc();
  const page = result as unknown as Page | null;

  if (!page || (!isDraftMode && (page as any)._status !== 'published')) {
    return {
      title: 'Page Not Found | Börsen Blick',
      description: 'The requested page could not be found.',
    };
  }

  // Prioritise German content first, then English, then fallback
  const title = page.germanTab?.germanTitle || 
                page.englishTab?.title || 
                page.title;

  const description = 'Static page content from Börsen Blick.';

  // Handle featuredImage which can be number (ID) or Media object
  const featuredImage = typeof page.featuredImage === 'object' 
    ? (page.featuredImage as Media) 
    : null;

  return {
    title: `${title} | Börsen Blick`,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
      publishedTime: page.publishedAt || page.createdAt,
      authors: ['Börsen Blick Team'],
      images: featuredImage?.url ? [{
        url: featuredImage.url,
        width: 1200,
        height: 630,
        alt: featuredImage.alt || title,
      }] : [],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: featuredImage?.url ? [featuredImage.url] : [],
    },
  };
}

// Generate static params for published pages using cached data
export async function generateStaticParams(): Promise<{ slug: string }[]> {
  try {
    return await getCachedPublishedPageSlugs();
  } catch (error) {
    console.error('Error generating static params for pages:', error);
    return [];
  }
}

// Enable dynamic params for draft routes
export const dynamicParams = true;

// Main page component
export default async function PageDisplay({ params }: PageProps) {
  const { slug } = await params;
  const draft = await draftMode();
  const isDraftMode = draft.isEnabled;

  const page = await getPage(slug, isDraftMode);

  // Check access: draft mode shows any page, normal mode only shows published pages
  if (!page) {
    notFound();
  }

  return (
    <div className="min-h-dvh bg-background">
      {/* Draft Mode Banner */}
      {isDraftMode && (
        <div className="bg-yellow-50 border-b border-border p-4 text-center">
          <p className="text-yellow-800 font-medium">
            Preview Mode - This is a draft version
            <a
              href="/api/disable-draft"
              className="ml-2 underline hover:no-underline"
            >
              Exit Preview
            </a>
          </p>
        </div>
      )}

      {/* Accessibility Navigation */}
      <PageAccessibilityNav />

      {/* Match Articles Grid Layout Exactly */}
      <div className="max-w-[1440px] mx-auto px-4 py-8 lg:py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-0">
          {/* Column 1: Reserved for future use */}
          <aside
            className="sm:border-r sm:border-border sm:pr-2 lg:pr-3 xl:pr-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-3 sm:order-1 hidden sm:block"
            aria-labelledby="page-sidebar-heading"
            tabIndex={-1}
          >
            <h2 id="page-sidebar-heading" className="sr-only">
              Page Sidebar (Reserved)
            </h2>
            <div className="text-gray-500 dark:text-gray-400 text-sm italic">
              {/* Reserved for future content */}
            </div>
          </aside>

          {/* Columns 2-3: Page title, image and main content */}
          <section
            id="page-content"
            className="sm:col-span-1 lg:col-span-2 xl:col-span-2 sm:border-r sm:border-border sm:px-2 lg:px-3 xl:px-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-1 sm:order-2"
            aria-labelledby="page-content-heading"
            tabIndex={-1}
          >
            <h2 id="page-content-heading" className="sr-only">
              Page Content
            </h2>
            <div className="flex flex-col gap-6">
              <PageHeader page={page} locale="de" />
              <PageContent page={page} locale="de" />
            </div>
          </section>

          {/* Column 4: Recent articles sidebar */}
          <aside
            id="related-articles"
            className="xl:pl-4 order-2 sm:order-3"
            aria-labelledby="recent-articles-heading"
            tabIndex={-1}
          >
            <h2 id="recent-articles-heading" className="sr-only">
              Recent Articles
            </h2>
            <Suspense fallback={<RelatedArticlesSkeleton maxArticles={4} />}>
              <RecentArticlesSection maxArticles={4} locale="de" />
            </Suspense>
          </aside>
        </div>
      </div>
    </div>
  );
}
```

### Phase 4: Skeleton Loading

#### 4.1 Create Page-specific Skeleton
**File**: `src/components/pages/PageSkeleton.tsx`

```typescript
import { CardSkeleton } from '@/components/NewsCard';

export default function PageSkeleton() {
  return (
    <div className="min-h-dvh bg-background">
      <div className="max-w-[1440px] mx-auto px-4 py-8 lg:py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-0">
          {/* Column 1: Reserved space skeleton */}
          <aside className="sm:border-r sm:border-border sm:pr-2 lg:pr-3 xl:pr-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-3 sm:order-1 hidden sm:block">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-2/3 mb-2" />
              <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded w-1/2" />
            </div>
          </aside>

          {/* Columns 2-3: Page content skeleton */}
          <section className="sm:col-span-1 lg:col-span-2 xl:col-span-2 sm:border-r sm:border-border sm:px-2 lg:px-3 xl:px-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-1 sm:order-2">
            <div className="animate-pulse space-y-6">
              {/* Title skeleton */}
              <div className="space-y-2">
                <div className="h-8 bg-gray-200 dark:bg-gray-800 rounded w-3/4" />
                <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-1/3" />
              </div>
              
              {/* Image skeleton */}
              <div className="bg-gray-200 dark:bg-gray-800 aspect-[16/9] rounded-lg" />
              
              {/* Content skeleton */}
              <div className="space-y-4">
                <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-full" />
                <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-5/6" />
                <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-4/5" />
                <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-full" />
                <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-3/4" />
              </div>
            </div>
          </section>

          {/* Column 4: Recent articles skeleton */}
          <aside className="xl:pl-4 order-2 sm:order-3">
            <div className="space-y-2 sm:space-y-2.5">
              <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-28" />
              <div className="space-y-1.5 md:space-y-2">
                {Array.from({ length: 4 }).map((_, index) => (
                  <CardSkeleton key={index} variant="title-only" />
                ))}
              </div>
            </div>
          </aside>
        </div>
      </div>
    </div>
  );
}
```

### Phase 5: Integration and Configuration

#### 5.1 Add Pages Cache Invalidation Hook
**File**: `src/collections/Pages/index.ts`

```typescript
import { createPageInvalidationHook } from '@/lib/cache/invalidation';

export const Pages: CollectionConfig = {
  // ... existing config
  hooks: {
    beforeChange: [
      // ... existing hooks
    ],
    afterChange: [
      revalidatePage,
      createPageInvalidationHook(), // Add cache invalidation
    ],
    afterDelete: [
      revalidateDelete,
      createPageInvalidationHook(), // Add cache invalidation for deletes
    ],
  },
  // ... rest of config
};
```

#### 5.2 Add Loading and Error States
**File**: `src/app/(frontend)/pages/[slug]/loading.tsx`

```typescript
import PageSkeleton from '@/components/pages/PageSkeleton';

export default function Loading() {
  return <PageSkeleton />;
}
```

**File**: `src/app/(frontend)/pages/[slug]/error.tsx`

```typescript
'use client';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <div className="min-h-dvh bg-background">
      <div className="max-w-[1440px] mx-auto px-4 py-8 lg:py-12">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Something went wrong!</h2>
          <p className="text-gray-600 mb-4">
            There was an error loading this page.
          </p>
          <button
            onClick={reset}
            className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
          >
            Try again
          </button>
        </div>
      </div>
    </div>
  );
}
```

**File**: `src/app/(frontend)/pages/[slug]/not-found.tsx`

```typescript
import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="min-h-dvh bg-background">
      <div className="max-w-[1440px] mx-auto px-4 py-8 lg:py-12">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Page Not Found</h2>
          <p className="text-gray-600 mb-4">
            The requested page could not be found.
          </p>
          <Link
            href="/"
            className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90 inline-block"
          >
            Return Home
          </Link>
        </div>
      </div>
    </div>
  );
}
```

## Architecture Compliance

### PayloadCMS Best Practices ✅
- Uses native PayloadCMS caching patterns with `unstable_cache`
- Leverages existing `getCachedDocument` patterns  
- Implements proper draft mode support
- Uses PayloadCMS type system consistently
- Integrates with existing invalidation hooks

### Next.js Best Practices ✅
- Implements proper `generateMetadata` for SEO
- Uses `generateStaticParams` for static generation
- Enables `dynamicParams` for draft routes
- Follows App Router patterns with proper async components
- Implements proper error boundaries and loading states

### Shared Component Usage ✅
- Reuses `NewsCard` component for recent articles
- Leverages existing skeleton loading components
- Uses consistent typography and spacing patterns
- Follows established 4-column grid layout
- Integrates with existing Lexical content rendering

### Caching Strategy ✅
- Follows unified caching strategy (5-minute cache duration)
- Uses consistent cache tag naming
- Implements proper invalidation patterns
- Avoids cache size issues with smart field selection

## Testing Strategy

1. **Unit Tests**: Test individual components (PageHeader, PageContent, RecentArticlesSection)
2. **Integration Tests**: Test page route with different data scenarios
3. **Cache Tests**: Verify caching behavior and invalidation
4. **Performance Tests**: Monitor cache size and loading times
5. **Accessibility Tests**: Ensure proper ARIA labels and navigation

## Future Enhancements

1. **Column 1 Reserved Space**: Can be used for:
   - Page-specific navigation
   - Table of contents for long pages
   - Related page links
   - Call-to-action sections

2. **Enhanced Content Features**:
   - Page-specific components
   - Interactive elements
   - Custom layout options

3. **Performance Optimizations**:
   - Image optimization for featured images
   - Progressive loading for long content
   - Advanced caching strategies

This plan ensures that the Pages collection frontend follows the same high-quality patterns as our Articles implementation while providing a solid foundation for future enhancements.
description:
globs:
alwaysApply: false
---
