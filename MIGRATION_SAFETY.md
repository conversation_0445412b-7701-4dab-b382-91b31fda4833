# PayloadCMS Migration Safety Guide

## ⚠️ CRITICAL: How to Prevent Database Destruction

PayloadCMS can generate massive migrations that **DROP YOUR ENTIRE DATABASE**. Follow this guide to prevent data loss.

## What Happened

When you added new image sizes to the Media collection, PayloadCMS:
1. Detected schema changes (new image size columns needed)
2. Generated a migration that recreates the entire database from scratch
3. This migration would have **deleted all your articles and data**

## Prevention Strategies

### 1. Never Run These Commands in Production
```bash
# DANGEROUS - Can destroy your database
pnpm payload generate:migration
payload generate:migration

# DANGEROUS - Can apply destructive migrations
pnpm payload migrate
payload migrate
```

### 2. Safe Schema Change Workflow

**For Collection Changes (like adding image sizes):**

1. **Make changes in development**
2. **Test thoroughly**
3. **Create manual Supabase migration** (not PayloadCMS migration)
4. **Test migration on development database**
5. **Push code + Supabase migration to production**
6. **Let Supabase GitHub integration handle the migration**

**Example: Adding new image sizes**
```sql
-- supabase/migrations/YYYYMMDD_add_image_sizes.sql
ALTER TABLE public.media 
ADD COLUMN IF NOT EXISTS sizes_new_size_url varchar,
ADD COLUMN IF NOT EXISTS sizes_new_size_width numeric,
-- ... etc
```

### 3. Current Safety Settings

Your PayloadCMS config now has these protections:
- `push: false` - Disables automatic schema sync
- `disableMigrations: true` in production
- Development warnings about migration safety

### 4. Red Flags to Watch For

**If you see these, STOP immediately:**
- PayloadCMS asking about "dev mode changes"
- Migration files that DROP tables
- Migration files over 100 lines
- Any migration that mentions "CASCADE"

### 5. Safe Development Practices

1. **Always backup before schema changes**
2. **Use Supabase migrations for database changes**
3. **Test on development database first**
4. **Never run PayloadCMS migrations in production**
5. **Use version control for all changes**

## Emergency Recovery

If you accidentally generate a dangerous migration:
1. **DO NOT COMMIT IT**
2. Delete the migration file immediately
3. Remove it from `src/migrations/index.ts`
4. Create a safe Supabase migration instead

## Questions?

When in doubt:
- Use Supabase migrations for database changes
- Test everything in development first
- Ask for help before running any migration commands
