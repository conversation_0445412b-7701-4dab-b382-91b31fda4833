#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to diagnose and fix data corruption in Articles collection
 * Specifically targets the "Cannot create property 'id' on string" error
 */

import { getPayload } from 'payload';
import config from '../src/payload.config.ts';

async function diagnoseAndFixCorruption() {
  try {
    console.log('🔍 Diagnosing article data corruption...');

    const payload = await getPayload({ config });

    // First, check the specific article causing the error (ID 265)
    console.log('\n📄 Checking article 265 specifically...');

    try {
      const article265 = await payload.findByID({
        collection: 'articles',
        id: 265,
        depth: 0, // Get raw data without relationships
      });

      console.log('✅ Article 265 found in database');
      console.log('📋 Basic info:', {
        id: article265.id,
        title: article265.title,
        workflowStage: article265.workflowStage,
      });

      // Check for corrupted array fields
      console.log('\n🔍 Checking array fields for corruption...');

      // Check keywords in English tab
      if (article265.englishTab?.keywords) {
        console.log('📝 English keywords structure:');
        console.log('   Type:', typeof article265.englishTab.keywords);
        console.log(
          '   Is Array:',
          Array.isArray(article265.englishTab.keywords)
        );
        console.log('   Length:', article265.englishTab.keywords?.length || 0);

        if (Array.isArray(article265.englishTab.keywords)) {
          article265.englishTab.keywords.forEach((item, index) => {
            console.log(`   [${index}]:`, {
              type: typeof item,
              value: item,
              hasKeywordProperty: typeof item === 'object' && 'keyword' in item,
            });
          });
        }
      }

      // Check keywords in German tab
      if (article265.germanTab?.germanKeywords) {
        console.log('\n📝 German keywords structure:');
        console.log('   Type:', typeof article265.germanTab.germanKeywords);
        console.log(
          '   Is Array:',
          Array.isArray(article265.germanTab.germanKeywords)
        );
        console.log(
          '   Length:',
          article265.germanTab.germanKeywords?.length || 0
        );

        if (Array.isArray(article265.germanTab.germanKeywords)) {
          article265.germanTab.germanKeywords.forEach((item, index) => {
            console.log(`   [${index}]:`, {
              type: typeof item,
              value: item,
              hasKeywordProperty: typeof item === 'object' && 'keyword' in item,
            });
          });
        }
      }

      // Check categories relationship
      if (article265.categories) {
        console.log('\n📂 Categories structure:');
        console.log('   Type:', typeof article265.categories);
        console.log('   Is Array:', Array.isArray(article265.categories));
        console.log('   Length:', article265.categories?.length || 0);

        if (Array.isArray(article265.categories)) {
          article265.categories.forEach((item, index) => {
            console.log(`   [${index}]:`, {
              type: typeof item,
              value: item,
              hasIdProperty: typeof item === 'object' && 'id' in item,
            });
          });
        }
      }

      // Check related companies
      if (article265.relatedCompanies) {
        console.log('\n🏢 Related companies structure:');
        console.log('   Type:', typeof article265.relatedCompanies);
        console.log('   Is Array:', Array.isArray(article265.relatedCompanies));
        console.log('   Length:', article265.relatedCompanies?.length || 0);

        if (Array.isArray(article265.relatedCompanies)) {
          article265.relatedCompanies.forEach((item, index) => {
            console.log(`   [${index}]:`, {
              type: typeof item,
              value: typeof item === 'string' ? item : 'object',
              hasRequiredFields: typeof item === 'object' && 'name' in item,
            });
          });
        }
      }

      // Check keywordsMatched relationship
      if (article265.keywordsMatched) {
        console.log('\n🔑 Keywords matched structure:');
        console.log('   Type:', typeof article265.keywordsMatched);
        console.log('   Is Array:', Array.isArray(article265.keywordsMatched));
        console.log('   Length:', article265.keywordsMatched?.length || 0);

        if (Array.isArray(article265.keywordsMatched)) {
          article265.keywordsMatched.forEach((item, index) => {
            console.log(`   [${index}]:`, {
              type: typeof item,
              value: item,
              hasIdProperty: typeof item === 'object' && 'id' in item,
            });
          });
        }
      }
    } catch (error) {
      console.error('❌ Error fetching article 265:', error.message);
    }

    // Now check for similar issues in other articles
    console.log('\n🔍 Scanning other articles for similar corruption...');

    const articles = await payload.find({
      collection: 'articles',
      limit: 50,
      sort: '-createdAt',
      depth: 0,
    });

    const corruptionReport = {
      totalChecked: 0,
      corruptedKeywords: [],
      corruptedCategories: [],
      corruptedCompanies: [],
      corruptedKeywordsMatched: [],
    };

    for (const article of articles.docs) {
      corruptionReport.totalChecked++;

      // Check English keywords
      if (
        article.englishTab?.keywords &&
        Array.isArray(article.englishTab.keywords)
      ) {
        const hasCorruptedKeywords = article.englishTab.keywords.some(
          item => typeof item === 'string'
        );
        if (hasCorruptedKeywords) {
          corruptionReport.corruptedKeywords.push({
            id: article.id,
            title: article.title,
            corruptedItems: article.englishTab.keywords.filter(
              item => typeof item === 'string'
            ),
          });
        }
      }

      // Check German keywords
      if (
        article.germanTab?.germanKeywords &&
        Array.isArray(article.germanTab.germanKeywords)
      ) {
        const hasCorruptedKeywords = article.germanTab.germanKeywords.some(
          item => typeof item === 'string'
        );
        if (hasCorruptedKeywords) {
          corruptionReport.corruptedKeywords.push({
            id: article.id,
            title: article.title,
            corruptedItems: article.germanTab.germanKeywords.filter(
              item => typeof item === 'string'
            ),
          });
        }
      }

      // Check categories
      if (article.categories && Array.isArray(article.categories)) {
        const hasCorruptedCategories = article.categories.some(
          item => typeof item === 'string'
        );
        if (hasCorruptedCategories) {
          corruptionReport.corruptedCategories.push({
            id: article.id,
            title: article.title,
            corruptedItems: article.categories.filter(
              item => typeof item === 'string'
            ),
          });
        }
      }

      // Check keywordsMatched
      if (article.keywordsMatched && Array.isArray(article.keywordsMatched)) {
        const hasCorruptedKeywordsMatched = article.keywordsMatched.some(
          item => typeof item === 'string'
        );
        if (hasCorruptedKeywordsMatched) {
          corruptionReport.corruptedKeywordsMatched.push({
            id: article.id,
            title: article.title,
            corruptedItems: article.keywordsMatched.filter(
              item => typeof item === 'string'
            ),
          });
        }
      }
    }

    console.log('\n📊 Corruption Report:');
    console.log('=====================');
    console.log(`Total articles checked: ${corruptionReport.totalChecked}`);
    console.log(
      `Articles with corrupted keywords: ${corruptionReport.corruptedKeywords.length}`
    );
    console.log(
      `Articles with corrupted categories: ${corruptionReport.corruptedCategories.length}`
    );
    console.log(
      `Articles with corrupted keywordsMatched: ${corruptionReport.corruptedKeywordsMatched.length}`
    );

    if (corruptionReport.corruptedKeywords.length > 0) {
      console.log('\n❌ Corrupted Keywords Found:');
      corruptionReport.corruptedKeywords.forEach(item => {
        console.log(`   Article ${item.id}: "${item.title}"`);
        console.log(
          `   Corrupted items: ${JSON.stringify(item.corruptedItems)}`
        );
      });
    }

    if (corruptionReport.corruptedCategories.length > 0) {
      console.log('\n❌ Corrupted Categories Found:');
      corruptionReport.corruptedCategories.forEach(item => {
        console.log(`   Article ${item.id}: "${item.title}"`);
        console.log(
          `   Corrupted items: ${JSON.stringify(item.corruptedItems)}`
        );
      });
    }

    if (corruptionReport.corruptedKeywordsMatched.length > 0) {
      console.log('\n❌ Corrupted KeywordsMatched Found:');
      corruptionReport.corruptedKeywordsMatched.forEach(item => {
        console.log(`   Article ${item.id}: "${item.title}"`);
        console.log(
          `   Corrupted items: ${JSON.stringify(item.corruptedItems)}`
        );
      });
    }

    process.exit(0);
  } catch (error) {
    console.error('❌ Error during diagnosis:', error);
    process.exit(1);
  }
}

diagnoseAndFixCorruption();
