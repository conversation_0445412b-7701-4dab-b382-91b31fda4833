#!/bin/bash

# Simple Local Database Backup
# Usage: pnpm backup-local-db

set -e

BACKUP_BASE_DIR="supabase/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
# Get current git branch and sanitize for filename
BRANCH=$(git branch --show-current 2>/dev/null || echo "unknown")
# Replace characters that aren't filesystem-safe with underscores
BRANCH_SAFE=$(echo "$BRANCH" | sed 's/[^a-zA-Z0-9._-]/_/g')

# Create backup-specific subfolder with full filename inside
BACKUP_NAME="local_db_${BRANCH_SAFE}_$TIMESTAMP"
BACKUP_DIR="$BACKUP_BASE_DIR/$BACKUP_NAME"
BACKUP_FILE="$BACKUP_DIR/$BACKUP_NAME.sql"

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

echo "🗄️ Backing up local Supabase database..."

# Check if Supabase is running
if ! supabase status > /dev/null 2>&1; then
    echo "❌ Error: Supabase is not running"
    echo "Please start Supabase with: supabase start"
    exit 1
fi

# Create separate backups following Supabase best practices
ROLES_FILE="${BACKUP_FILE%.sql}_roles.sql"
SCHEMA_FILE="${BACKUP_FILE%.sql}_schema.sql"
DATA_FILE="${BACKUP_FILE%.sql}_data.sql"

echo "📋 Creating roles backup..."
supabase db dump --local --role-only -f "$ROLES_FILE"

echo "🏗️ Creating schema backup..."
supabase db dump --local -f "$SCHEMA_FILE"

echo "📊 Creating data backup..."
supabase db dump --local --data-only --use-copy -f "$DATA_FILE"

# For convenience, also create a combined restore script
RESTORE_SCRIPT="${BACKUP_FILE%.sql}_restore.sh"
cat > "$RESTORE_SCRIPT" << EOF
#!/bin/bash
# Auto-generated restore script for backup: ${BRANCH_SAFE}_$TIMESTAMP
# Usage: ./$(basename "$RESTORE_SCRIPT")

set -euo pipefail

echo "🔄 Restoring database from backup: ${BRANCH_SAFE}_$TIMESTAMP"

# Get database URL from Supabase CLI
DB_URL=\$(supabase status --output env | grep "DB_URL=" | cut -d'"' -f2)

if [ -z "\$DB_URL" ]; then
    echo "❌ Error: Could not get database URL. Is Supabase running?"
    exit 1
fi

echo "⚠️ This will replace your current database with the backup."
echo "Are you sure? (y/N)"
read -r confirm

if [ "\$confirm" != "y" ] && [ "\$confirm" != "Y" ]; then
    echo "❌ Restore cancelled"
    exit 0
fi

echo "🗄️ Resetting database..."
supabase db reset --local

echo "📦 Restoring from backup files..."
docker run --rm -i --network host -v "\$(pwd):/workspace" -w /workspace postgres:15 \\
    psql "\$DB_URL" \\
    --single-transaction \\
    --variable ON_ERROR_STOP=1 \\
    --file "$ROLES_FILE" \\
    --file "$SCHEMA_FILE" \\
    --command 'SET session_replication_role = replica' \\
    --file "$DATA_FILE"

echo "✅ Database restored successfully!"
echo "🎯 Restored from backup: ${BRANCH_SAFE}_$TIMESTAMP"
EOF

chmod +x "$RESTORE_SCRIPT"

# Create a simple info file
cat > "$BACKUP_DIR/local_db_${BRANCH_SAFE}_$TIMESTAMP.info" << EOF
Supabase Local Database Backup
==============================
Created: $(date)
Branch: $BRANCH
Timestamp: $TIMESTAMP
Git commit: $(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

Files created:
- $ROLES_FILE (Database roles)
- $SCHEMA_FILE (Database schema)  
- $DATA_FILE (Database data with COPY statements)
- $RESTORE_SCRIPT (Auto-generated restore script)

Restore options:
==============
Option 1: Use the restore script
  ./$RESTORE_SCRIPT

Option 2: Use the restore command
  pnpm restore-local-db ${BRANCH_SAFE}_$TIMESTAMP

File sizes:
- Roles: $(du -h "$ROLES_FILE" | cut -f1)
- Schema: $(du -h "$SCHEMA_FILE" | cut -f1) 
- Data: $(du -h "$DATA_FILE" | cut -f1)
EOF

echo "✅ Database backed up successfully!"
echo "📁 Files created:"
echo "   📋 Roles: $ROLES_FILE"
echo "   🏗️ Schema: $SCHEMA_FILE" 
echo "   📊 Data: $DATA_FILE"
echo "   🔄 Restore script: $RESTORE_SCRIPT"
echo "📋 Info file: $BACKUP_DIR/local_db_${BRANCH_SAFE}_$TIMESTAMP.info"
echo ""
echo "🚀 Quick restore: ./$RESTORE_SCRIPT"
echo "🔄 Or use: pnpm restore-local-db ${BRANCH_SAFE}_$TIMESTAMP" 
