#!/usr/bin/env node

/**
 * Test script for Resend email configuration
 * Run with: npx tsx scripts/test-email-config.ts
 */

import dotenv from 'dotenv';

// Load both .env and .env.local (like Next.js does)
dotenv.config({ path: '.env' });
dotenv.config({ path: '.env.local', override: true });

const testEmailConfig = (): boolean => {
  console.log('🧪 Testing Resend Email Configuration\n');

  // Check if required environment variables are set
  const requiredVars = {
    RESEND_API_KEY: process.env.RESEND_API_KEY,
    RESEND_DEFAULT_FROM_ADDRESS: process.env.RESEND_DEFAULT_FROM_ADDRESS,
    RESEND_DEFAULT_FROM_NAME: process.env.RESEND_DEFAULT_FROM_NAME,
    PIPELINE_NOTIFICATION_EMAIL: process.env.PIPELINE_NOTIFICATION_EMAIL,
  };

  let allGood = true;

  Object.entries(requiredVars).forEach(([key, value]) => {
    if (!value) {
      console.log(`❌ Missing: ${key}`);
      allGood = false;
    } else {
      console.log(`✅ Found: ${key} = ${value.substring(0, 10)}...`);
    }
  });

  if (!allGood) {
    console.log('\n🚨 Missing required environment variables!');
    console.log(
      'Please check your .env file and ensure all Resend variables are set.'
    );
    console.log('See RESEND_SETUP.md for detailed setup instructions.');
    return false;
  }

  // Validate API key format
  if (!requiredVars.RESEND_API_KEY!.startsWith('re_')) {
    console.log('❌ RESEND_API_KEY should start with "re_"');
    allGood = false;
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(requiredVars.RESEND_DEFAULT_FROM_ADDRESS!)) {
    console.log('❌ RESEND_DEFAULT_FROM_ADDRESS is not a valid email format');
    allGood = false;
  }

  // Validate PIPELINE_NOTIFICATION_EMAIL (supports multiple emails)
  if (requiredVars.PIPELINE_NOTIFICATION_EMAIL) {
    const pipelineEmails = requiredVars.PIPELINE_NOTIFICATION_EMAIL.split(
      ','
    ).map((email: string) => email.trim());
    const invalidEmails = pipelineEmails.filter(
      (email: string) => !emailRegex.test(email)
    );

    if (invalidEmails.length > 0) {
      console.log(
        `❌ Invalid email format in PIPELINE_NOTIFICATION_EMAIL: ${invalidEmails.join(', ')}`
      );
      allGood = false;
    } else if (pipelineEmails.length > 1) {
      console.log(
        `✅ Multiple notification emails configured: ${pipelineEmails.length} recipients`
      );
    }
  }

  if (allGood) {
    console.log('\n✅ All environment variables look good!');
    console.log('\n📧 To test email sending:');
    console.log('1. Start your PayloadCMS server');
    console.log('2. Go to the admin login page');
    console.log('3. Click "Forgot Password?"');
    console.log('4. Enter an existing user email');
    console.log('5. Check for the password reset email');
    console.log('\n💡 Make sure your domain is verified in Resend first!');
  }

  return allGood;
};

// Run the test
testEmailConfig();
