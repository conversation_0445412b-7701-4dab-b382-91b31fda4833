#!/usr/bin/env node

/**
 * Fix Generated Articles Enhanced Flag
 *
 * Sets hasBeenEnhanced = true for all generated articles since they are
 * already AI-enhanced during the import process.
 */

const { Pool } = require('pg');
require('dotenv').config();

async function fixGeneratedArticlesEnhancedFlag() {
  const pool = new Pool({
    connectionString:
      process.env.DATABASE_URI ||
      process.env.POSTGRES_URL ||
      process.env.DATABASE_URL,
  });

  try {
    console.log('🔄 Updating hasBeenEnhanced flag for generated articles...');

    const result = await pool.query(`
      UPDATE articles 
      SET "hasBeenEnhanced" = true 
      WHERE "articleType" = 'generated' 
      AND "hasBeenEnhanced" = false;
    `);

    console.log(`✅ Updated ${result.rowCount} generated articles`);
    console.log('🎉 Generated articles now have hasBeenEnhanced = true');
    console.log(
      '📝 The translate button should now be enabled for these articles'
    );
  } catch (error) {
    console.error('❌ Error updating articles:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the fix
fixGeneratedArticlesEnhancedFlag();
