#!/usr/bin/env node

/**
 * Clear Processed URLs Script
 *
 * This script clears all processed URLs from the database to allow
 * re-testing of the content pipelines with the same URLs.
 *
 * Usage: node scripts/clear-processed-urls.js
 */

import { Client } from 'pg';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env') });

async function clearProcessedUrls() {
  const client = new Client({
    connectionString: process.env.DATABASE_URI || process.env.POSTGRES_URL,
  });

  try {
    await client.connect();
    console.log('🔧 Connected to database...\n');

    // Get current count
    const countResult = await client.query(
      'SELECT COUNT(*) FROM processed_urls'
    );
    const currentCount = parseInt(countResult.rows[0].count);

    console.log(`📊 Current processed URLs: ${currentCount}`);

    if (currentCount === 0) {
      console.log('✅ No processed URLs to clear');
      return;
    }

    // Clear all processed URLs
    console.log('🧹 Clearing all processed URLs...');
    const deleteResult = await client.query('DELETE FROM processed_urls');

    console.log(`✅ Cleared ${deleteResult.rowCount} processed URLs`);
    console.log('🎯 All URLs can now be processed again by the pipelines');
  } catch (error) {
    console.error('❌ Error clearing processed URLs:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Handle command line confirmation
const args = process.argv.slice(2);
if (args.includes('--confirm') || args.includes('-y')) {
  clearProcessedUrls().catch(console.error);
} else {
  console.log('🚨 This will clear ALL processed URLs from the database');
  console.log('   This allows re-testing pipelines with the same URLs');
  console.log('');
  console.log('   To proceed, run:');
  console.log('   node scripts/clear-processed-urls.js --confirm');
  console.log('');
  console.log('   Or use the shorthand:');
  console.log('   node scripts/clear-processed-urls.js -y');
}
