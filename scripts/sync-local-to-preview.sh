#!/bin/bash

# Sync Local Data to Preview (CLI-Linked Project)
# Usage: pnpm sync-data

set -e

TIMESTAMP=$(date +%Y%m%d_%H%M%S)
TEMP_DIR="tmp"
DATA_FILE="$TEMP_DIR/local_to_preview_sync_$TIMESTAMP.sql"

# Create temp directory
mkdir -p "$TEMP_DIR"

echo "🔄 Syncing local data to preview environment..."

# Get linked project info
LINKED_PROJECT=$(supabase projects list 2>/dev/null | grep "●" | awk -F'|' '{print $4}' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//' || echo "Unknown")
echo "📋 Target: $LINKED_PROJECT"

# Verify user wants to proceed
echo ""
echo "⚠️  WARNING: This will overwrite data in your preview environment!"
echo "⚠️  Target project: $LINKED_PROJECT"
echo ""
echo "✅ Make sure you've run 'pnpm backup:remote' first!"
echo ""
read -p "Continue with data sync? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Data sync cancelled"
    rm -rf "$TEMP_DIR"
    exit 1
fi

echo "📊 Exporting data from local database..."

# Export all data from public schema (where PayloadCMS stores data)
supabase db dump --local --data-only --use-copy \
  --schema public \
  -f "$DATA_FILE"

if [ ! -f "$DATA_FILE" ]; then
    echo "❌ Error: Failed to create data export"
    rm -rf "$TEMP_DIR"
    exit 1
fi

echo "📤 Importing data to preview environment..."

# Import to CLI-linked project (preview) using direct PostgreSQL connection
# We'll use the DATABASE_URI from .env which points to the preview database
if [ -f ".env" ]; then
    source .env
    echo "📋 Importing to: $(echo $DATABASE_URI | sed 's/:[^@]*@/@***@/')"
    psql "$DATABASE_URI" -f "$DATA_FILE"
else
    echo "❌ Error: .env file not found. Make sure you're in preview environment."
    rm -rf "$TEMP_DIR"
    exit 1
fi

echo "✅ Data sync completed successfully!"
echo "🎯 Synced to: $LINKED_PROJECT"
echo "📁 Temp file: $DATA_FILE"

# Ask if user wants to keep temp file
echo ""
read -p "Keep temporary export file for backup? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  Cleaning up temporary files..."
    rm -rf "$TEMP_DIR"
    echo "✅ Cleanup complete"
else
    echo "📁 Temporary export saved: $DATA_FILE"
fi

echo ""
echo "🚀 Next steps:"
echo "   1. Test preview environment: pnpm env:preview && pnpm dev"
echo "   2. Deploy to Vercel: git push origin preview" 
