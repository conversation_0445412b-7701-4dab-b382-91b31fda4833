#!/bin/bash

# Remote Database Backup (CLI-Linked Project)
# Usage: pnpm backup:remote

set -e

BACKUP_BASE_DIR="supabase/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
# Get current git branch and sanitize for filename
BRANCH=$(git branch --show-current 2>/dev/null || echo "unknown")
# Replace characters that aren't filesystem-safe with underscores
BRANCH_SAFE=$(echo "$BRANCH" | sed 's/[^a-zA-Z0-9._-]/_/g')

# Get linked project info first
LINKED_PROJECT=$(supabase projects list 2>/dev/null | grep "●" | awk -F'|' '{print $4}' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//' || echo "Unknown")

# Create project-specific naming
PROJECT_SAFE=$(echo "$LINKED_PROJECT" | \
    sed 's/ö/o/g; s/ä/a/g; s/ü/u/g; s/ß/ss/g; s/Ö/O/g; s/Ä/A/g; s/Ü/U/g' | \
    sed 's/[^a-zA-Z0-9._-]/_/g' | \
    sed 's/__*/_/g' | \
    sed 's/^_//;s/_$//' | \
    tr '[:upper:]' '[:lower:]')
if [ "$PROJECT_SAFE" = "unknown" ] || [ -z "$PROJECT_SAFE" ]; then
    PROJECT_SAFE="unknown"
fi

# Create backup-specific subfolder with full filename inside
BACKUP_NAME="remote_${PROJECT_SAFE}_${BRANCH_SAFE}_$TIMESTAMP"
BACKUP_DIR="$BACKUP_BASE_DIR/$BACKUP_NAME"
BACKUP_FILE="$BACKUP_DIR/$BACKUP_NAME.sql"

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

echo "🗄️ Backing up REMOTE Supabase database ($LINKED_PROJECT)..."
echo "📋 CLI linked to project: $LINKED_PROJECT"

# Create separate backups following Supabase best practices
ROLES_FILE="${BACKUP_FILE%.sql}_roles.sql"
SCHEMA_FILE="${BACKUP_FILE%.sql}_schema.sql"
DATA_FILE="${BACKUP_FILE%.sql}_data.sql"

echo "📋 Creating roles backup..."
supabase db dump --role-only -f "$ROLES_FILE"

echo "🏗️ Creating schema backup..."
supabase db dump -f "$SCHEMA_FILE"

echo "📊 Creating data backup..."
supabase db dump --data-only --use-copy -f "$DATA_FILE"

# For convenience, also create a combined restore script
RESTORE_SCRIPT="${BACKUP_FILE%.sql}_restore.sh"
cat > "$RESTORE_SCRIPT" << EOF
#!/bin/bash
# Auto-generated restore script for REMOTE backup: ${PROJECT_SAFE}_${BRANCH_SAFE}_$TIMESTAMP
# Original project: $LINKED_PROJECT
# Usage: ./$(basename "$RESTORE_SCRIPT")

set -euo pipefail

echo "🔄 Restoring REMOTE database from backup: ${PROJECT_SAFE}_${BRANCH_SAFE}_$TIMESTAMP"

# Show which project we're restoring to
LINKED_PROJECT=\$(supabase projects list 2>/dev/null | grep "●" | awk -F'|' '{print \$4}' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//' || echo "Unknown")
echo "📋 CLI linked to project: \$LINKED_PROJECT"
echo ""

echo "⚠️ ⚠️ ⚠️  DANGER: This will replace your REMOTE database with the backup."
echo "⚠️ This affects your live remote environment: \$LINKED_PROJECT"
echo "Are you absolutely sure? (y/N)"
read -r confirm

if [ "\$confirm" != "y" ] && [ "\$confirm" != "Y" ]; then
    echo "❌ Restore cancelled"
    exit 0
fi

echo "❌ REMOTE DATABASE RESTORE NOT IMPLEMENTED"
echo ""
echo "⚠️  SAFETY NOTICE: Direct remote database restore is extremely dangerous"
echo "⚠️  and could cause data loss in production environments."
echo ""
echo "🔄 SAFER ALTERNATIVES:"
echo "   1. Use 'supabase db push' to apply schema changes"
echo "   2. Use data migration scripts for data changes"
echo "   3. Restore to local first, then selectively sync data"
echo ""
echo "📁 Backup files are available at:"
echo "   📋 Roles: $ROLES_FILE"
echo "   🏗️ Schema: $SCHEMA_FILE"
echo "   📊 Data: $DATA_FILE"
echo ""
echo "💡 For manual restore, you can use these files with psql directly"
echo "   but ONLY if you're absolutely certain about the consequences."

echo ""
echo "ℹ️  Remote database restore was not performed for safety reasons."
echo "📋 Use the backup files above for manual operations if needed."
EOF

chmod +x "$RESTORE_SCRIPT"

# Create a simple info file
cat > "$BACKUP_DIR/remote_${PROJECT_SAFE}_${BRANCH_SAFE}_$TIMESTAMP.info" << EOF
Supabase REMOTE Database Backup ($LINKED_PROJECT)
===============================
Created: $(date)
Branch: $BRANCH
Timestamp: $TIMESTAMP
Git commit: $(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

⚠️ WARNING: This is a REMOTE environment backup
⚠️ Restoring affects your live remote database

Files created:
- $ROLES_FILE (Database roles)
- $SCHEMA_FILE (Database schema)  
- $DATA_FILE (Database data with COPY statements)
- $RESTORE_SCRIPT (Auto-generated restore script)

Restore options:
==============
Option 1: Use the restore script (CAREFUL!)
  ./$RESTORE_SCRIPT

Option 2: Manual restore (CAREFUL!)
  supabase db restore [file]

File sizes:
- Roles: $(du -h "$ROLES_FILE" | cut -f1)
- Schema: $(du -h "$SCHEMA_FILE" | cut -f1) 
- Data: $(du -h "$DATA_FILE" | cut -f1)

SAFETY REMINDER:
===============
- Always backup before making changes
- Remote affects your live remote environment
- Test restores carefully
- Consider data sync from local instead of restore
EOF

echo "✅ REMOTE database ($LINKED_PROJECT) backed up successfully!"
echo "📁 Files created:"
echo "   📋 Roles: $ROLES_FILE"
echo "   🏗️ Schema: $SCHEMA_FILE" 
echo "   📊 Data: $DATA_FILE"
echo "   🔄 Restore script: $RESTORE_SCRIPT"
echo "📋 Info file: $BACKUP_DIR/remote_${PROJECT_SAFE}_${BRANCH_SAFE}_$TIMESTAMP.info"
echo ""
echo "⚠️  SAFETY REMINDER: This backup is from your REMOTE environment ($LINKED_PROJECT)"
echo "🚀 Quick restore: ./$RESTORE_SCRIPT (USE WITH CAUTION!)"
echo "💡 Consider using data sync instead of restore for safety"
