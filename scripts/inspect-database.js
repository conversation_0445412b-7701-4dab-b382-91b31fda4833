#!/usr/bin/env node

/**
 * Database Inspection Script
 *
 * This script connects to your database and provides a report on:
 * - Table sizes
 * - Potentially unused columns
 * - Database optimization opportunities
 *
 * Usage: node scripts/inspect-database.js
 */

const { Client } = require('pg');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

async function inspectDatabase() {
  const client = new Client({
    connectionString: process.env.DATABASE_URI,
  });

  try {
    await client.connect();
    console.log('🔍 Connected to database for inspection...\n');

    // 1. Get table sizes
    console.log('📊 TABLE SIZES:');
    console.log('================');
    const tableSizes = await client.query(`
      SELECT 
        schemaname,
        tablename,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
        pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
      FROM pg_tables 
      WHERE schemaname = 'public'
      ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
    `);

    tableSizes.rows.forEach(row => {
      console.log(`${row.tablename.padEnd(30)} ${row.size}`);
    });

    // 2. Check for potentially unused columns
    console.log('\n🔍 COLUMN ANALYSIS:');
    console.log('===================');

    // Articles table analysis
    const articlesColumns = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'articles'
      ORDER BY ordinal_position
    `);

    console.log('\n📄 Articles table columns:');
    articlesColumns.rows.forEach(col => {
      console.log(`  ${col.column_name} (${col.data_type})`);
    });

    // ProcessedUrls table analysis
    const processedUrlsColumns = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'processed_urls'
      ORDER BY ordinal_position
    `);

    console.log('\n🔗 Processed URLs table columns:');
    processedUrlsColumns.rows.forEach(col => {
      console.log(`  ${col.column_name} (${col.data_type})`);
    });

    // RSS Feeds table analysis
    const rssFeedsColumns = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'rss_feeds'
      ORDER BY ordinal_position
    `);

    console.log('\n📡 RSS Feeds table columns:');
    rssFeedsColumns.rows.forEach(col => {
      console.log(`  ${col.column_name} (${col.data_type})`);
    });

    // 3. Check for empty tables
    console.log('\n📈 ROW COUNTS:');
    console.log('==============');

    const mainTables = [
      'articles',
      'processed_urls',
      'rss_feeds',
      'keywords',
      'categories',
      'users',
    ];

    for (const table of mainTables) {
      try {
        const count = await client.query(`SELECT COUNT(*) FROM ${table}`);
        console.log(`${table.padEnd(20)} ${count.rows[0].count} rows`);
      } catch (err) {
        console.log(`${table.padEnd(20)} Table not found or error`);
      }
    }

    // 4. Check for integration test table
    console.log('\n🧪 DEVELOPMENT TABLES:');
    console.log('=====================');

    const devTables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name LIKE '%test%' OR table_name LIKE '%integration%'
      AND table_schema = 'public'
    `);

    if (devTables.rows.length > 0) {
      console.log('Found potential development tables:');
      devTables.rows.forEach(row => {
        console.log(
          `  ⚠️  ${row.table_name} (consider removing in production)`
        );
      });
    } else {
      console.log('No development tables found.');
    }

    // 5. Check for unused indexes
    console.log('\n📊 INDEX ANALYSIS:');
    console.log('==================');

    const unusedIndexes = await client.query(`
      SELECT 
        schemaname,
        tablename,
        indexname,
        idx_scan as scans,
        pg_size_pretty(pg_relation_size(indexrelid)) as size
      FROM pg_stat_user_indexes 
      WHERE idx_scan = 0 
      AND schemaname = 'public'
      ORDER BY pg_relation_size(indexrelid) DESC
    `);

    if (unusedIndexes.rows.length > 0) {
      console.log('Potentially unused indexes:');
      unusedIndexes.rows.forEach(row => {
        console.log(
          `  ${row.tablename}.${row.indexname} (${row.size}) - ${row.scans} scans`
        );
      });
    } else {
      console.log('No obviously unused indexes found.');
    }

    // 6. Database size summary
    const dbSize = await client.query(`
      SELECT pg_size_pretty(pg_database_size(current_database())) as total_size
    `);

    console.log('\n💾 DATABASE SUMMARY:');
    console.log('===================');
    console.log(`Total database size: ${dbSize.rows[0].total_size}`);

    console.log('\n✅ Database inspection completed!');
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('===================');
    console.log(
      '1. Run the database cleanup migration to remove unused columns'
    );
    console.log(
      '2. Consider removing the IntegrationTest collection in production'
    );
    console.log(
      '3. Monitor table growth and clean up old processed URLs periodically'
    );
    console.log('4. Check if any unused indexes can be dropped');
  } catch (error) {
    console.error('❌ Error inspecting database:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Only run if called directly
if (require.main === module) {
  inspectDatabase().catch(console.error);
}

module.exports = { inspectDatabase };
