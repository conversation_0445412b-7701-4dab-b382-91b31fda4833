#!/usr/bin/env node

/**
 * Test Translation API
 * Simple script to test the German translation API endpoint
 */

async function testTranslationAPI() {
  console.log('🧪 Testing German Translation API...');

  // You'll need to replace this with an actual article ID from your database
  const testArticleId = process.argv[2];

  if (!testArticleId) {
    console.error('❌ Please provide an article ID as an argument');
    console.log('Usage: npx tsx scripts/test-translation-api.ts <article-id>');
    process.exit(1);
  }

  try {
    const response = await fetch(
      'http://localhost:3000/api/articles/translate',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ articleId: testArticleId }),
      }
    );

    const result = await response.json();

    console.log('📊 API Response Status:', response.status);
    console.log('📊 API Response:', JSON.stringify(result, null, 2));

    if (result.success) {
      console.log('✅ Translation API working correctly!');
      console.log('📄 German Title:', result.translatedContent?.germanTitle);
      console.log(
        '📄 German Content Length:',
        result.translatedContent?.germanContent ? 'Present' : 'Missing'
      );
    } else {
      console.log('❌ Translation API failed:', result.error);
    }
  } catch (error: any) {
    console.error('❌ Error testing API:', error.message);
  }
}

testTranslationAPI();
