#!/usr/bin/env node

/**
 * Database Maintenance Script
 *
 * This script performs routine database maintenance tasks:
 * - Clean up old processed URLs
 * - Remove expired document locks
 * - Update table statistics
 * - Vacuum analyze tables
 *
 * Usage: node scripts/database-maintenance.js
 */

const { Client } = require('pg');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

async function performMaintenance() {
  const client = new Client({
    connectionString: process.env.DATABASE_URI || process.env.POSTGRES_URL,
  });

  try {
    await client.connect();
    console.log('🔧 Starting database maintenance...\n');

    // 1. Clean up old processed URLs (older than 90 days)
    console.log('🧹 Cleaning up old processed URLs...');
    const oldUrlsResult = await client.query(`
      DELETE FROM processed_urls 
      WHERE created_at < NOW() - INTERVAL '90 days'
      AND status IN ('rejected', 'error')
    `);
    console.log(`✅ Deleted ${oldUrlsResult.rowCount} old processed URLs`);

    // 2. Clean up old document locks (older than 24 hours)
    console.log('🔒 Cleaning up expired document locks...');
    const oldLocksResult = await client.query(`
      DELETE FROM payload_locked_documents 
      WHERE created_at < NOW() - INTERVAL '24 hours'
    `);
    console.log(`✅ Deleted ${oldLocksResult.rowCount} expired document locks`);

    // 3. Clean up old migration logs (keep last 50)
    console.log('📝 Cleaning up old migration logs...');
    const migrationCleanup = await client.query(`
      DELETE FROM payload_migrations 
      WHERE id NOT IN (
        SELECT id FROM payload_migrations 
        ORDER BY created_at DESC 
        LIMIT 50
      )
    `);
    console.log(
      `✅ Cleaned up ${migrationCleanup.rowCount} old migration records`
    );

    // 4. Update table statistics for better query performance
    console.log('📊 Updating table statistics...');
    const mainTables = [
      'articles',
      'processed_urls',
      'rss_feeds',
      'keywords',
      'categories',
      'users',
    ];

    for (const table of mainTables) {
      try {
        await client.query(`ANALYZE ${table}`);
        console.log(`  ✅ Analyzed ${table}`);
      } catch (err) {
        console.log(`  ⚠️  Could not analyze ${table}: ${err.message}`);
      }
    }

    // 5. Get database size info
    const sizeResult = await client.query(`
      SELECT 
        pg_size_pretty(pg_database_size(current_database())) as total_size,
        (SELECT COUNT(*) FROM articles) as articles_count,
        (SELECT COUNT(*) FROM processed_urls) as urls_count,
        (SELECT COUNT(*) FROM rss_feeds) as feeds_count
    `);

    const stats = sizeResult.rows[0];
    console.log('\n📊 Database Statistics:');
    console.log(`Total size: ${stats.total_size}`);
    console.log(`Articles: ${stats.articles_count}`);
    console.log(`Processed URLs: ${stats.urls_count}`);
    console.log(`RSS Feeds: ${stats.feeds_count}`);

    // 6. Check for potential issues
    console.log('\n🔍 Health Check:');

    // Check for articles without source URLs
    const orphanedArticles = await client.query(`
      SELECT COUNT(*) as count 
      FROM articles 
      WHERE source_url IS NULL OR source_url = ''
    `);

    if (parseInt(orphanedArticles.rows[0].count) > 0) {
      console.log(
        `⚠️  Found ${orphanedArticles.rows[0].count} articles without source URLs`
      );
    } else {
      console.log('✅ All articles have source URLs');
    }

    // Check for processed URLs without corresponding articles
    const orphanedUrls = await client.query(`
      SELECT COUNT(*) as count 
      FROM processed_urls pu
      LEFT JOIN articles a ON pu.article_id_id = a.id
      WHERE pu.status = 'accepted' AND a.id IS NULL
    `);

    if (parseInt(orphanedUrls.rows[0].count) > 0) {
      console.log(
        `⚠️  Found ${orphanedUrls.rows[0].count} processed URLs marked as accepted but missing articles`
      );
    } else {
      console.log('✅ All accepted processed URLs have corresponding articles');
    }

    console.log('\n🎉 Database maintenance completed successfully!');
  } catch (error) {
    console.error('❌ Error during database maintenance:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Helper function to run only specific maintenance tasks
async function runSpecificMaintenance(tasks = []) {
  const client = new Client({
    connectionString: process.env.DATABASE_URI || process.env.POSTGRES_URL,
  });

  try {
    await client.connect();

    if (tasks.includes('cleanup-urls')) {
      console.log('🧹 Cleaning up old processed URLs...');
      const result = await client.query(`
        DELETE FROM processed_urls 
        WHERE created_at < NOW() - INTERVAL '90 days'
        AND status IN ('rejected', 'error')
      `);
      console.log(`✅ Deleted ${result.rowCount} old processed URLs`);
    }

    if (tasks.includes('cleanup-locks')) {
      console.log('🔒 Cleaning up expired document locks...');
      const result = await client.query(`
        DELETE FROM payload_locked_documents 
        WHERE created_at < NOW() - INTERVAL '24 hours'
      `);
      console.log(`✅ Deleted ${result.rowCount} expired document locks`);
    }

    if (tasks.includes('analyze')) {
      console.log('📊 Updating table statistics...');
      const mainTables = [
        'articles',
        'processed_urls',
        'rss_feeds',
        'keywords',
        'categories',
        'users',
      ];

      for (const table of mainTables) {
        try {
          await client.query(`ANALYZE ${table}`);
          console.log(`  ✅ Analyzed ${table}`);
        } catch (err) {
          console.log(`  ⚠️  Could not analyze ${table}: ${err.message}`);
        }
      }
    }
  } catch (error) {
    console.error('❌ Error during specific maintenance:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.length > 0) {
  runSpecificMaintenance(args).catch(console.error);
} else {
  performMaintenance().catch(console.error);
}

module.exports = { performMaintenance, runSpecificMaintenance };
