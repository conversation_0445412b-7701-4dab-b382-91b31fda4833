#!/bin/bash

# Simple Local Database Restore
# Usage: pnpm restore-local-db [timestamp]

set -e

BACKUP_BASE_DIR="supabase/backups"
TIMESTAMP=$1

# If no timestamp provided, show available backups
if [ -z "$TIMESTAMP" ]; then
    echo "📋 Available backups:"
    echo ""
    if [ -d "$BACKUP_BASE_DIR" ]; then
        # Look for backup subdirectories and info files
        for backup_dir in $(find "$BACKUP_BASE_DIR" -maxdepth 1 -type d -name "local_db_*" 2>/dev/null | sort -r); do
            if [ "$backup_dir" = "$BACKUP_BASE_DIR" ]; then
                continue  # Skip the base directory itself
            fi
            
            backup_name=$(basename "$backup_dir")
            timestamp=$(echo "$backup_name" | sed 's/local_db_//')
            info_file="$backup_dir/${backup_name}.info"
            
            echo "🗄️ $timestamp"
            if [ -f "$info_file" ]; then
                echo "   $(grep "Created:" "$info_file" | cut -d: -f2-)"
                echo "   $(grep "Branch:" "$info_file" | cut -d: -f2-)"
                
                # Check if all files exist
                roles_file="$backup_dir/${backup_name}_roles.sql"
                schema_file="$backup_dir/${backup_name}_schema.sql"  
                data_file="$backup_dir/${backup_name}_data.sql"
                
                missing=()
                [ ! -f "$roles_file" ] && missing+=("roles")
                [ ! -f "$schema_file" ] && missing+=("schema") 
                [ ! -f "$data_file" ] && missing+=("data")
                
                if [ ${#missing[@]} -gt 0 ]; then
                    echo "   ⚠️ Missing files: ${missing[*]}"
                else
                    echo "   ✅ Complete backup"
                fi
            else
                echo "   ⚠️ No info file found"
            fi
            echo ""
        done
        
        # Also check for legacy backups (files directly in backup directory)
        for info_file in $(ls -t "$BACKUP_BASE_DIR"/local_db_*.info 2>/dev/null); do
            if [ -f "$info_file" ]; then
                filename=$(basename "$info_file" .info)
                timestamp=$(echo "$filename" | sed 's/local_db_//')
                
                echo "🗄️ $timestamp (legacy)"
                echo "   $(grep "Created:" "$info_file" | cut -d: -f2-)"
                echo "   $(grep "Branch:" "$info_file" | cut -d: -f2-)"
                
                # Check if all files exist
                roles_file="$BACKUP_BASE_DIR/local_db_${timestamp}_roles.sql"
                schema_file="$BACKUP_BASE_DIR/local_db_${timestamp}_schema.sql"  
                data_file="$BACKUP_BASE_DIR/local_db_${timestamp}_data.sql"
                
                missing=()
                [ ! -f "$roles_file" ] && missing+=("roles")
                [ ! -f "$schema_file" ] && missing+=("schema") 
                [ ! -f "$data_file" ] && missing+=("data")
                
                if [ ${#missing[@]} -gt 0 ]; then
                    echo "   ⚠️ Missing files: ${missing[*]}"
                else
                    echo "   ✅ Complete backup"
                fi
                echo ""
            fi
        done
        echo "Usage: pnpm restore-local-db <timestamp>"
        # Try to find the latest backup (either in subfolder or legacy)
        latest_backup=""
        if [ -n "$(find "$BACKUP_BASE_DIR" -maxdepth 1 -type d -name "local_db_*" 2>/dev/null | head -1)" ]; then
            latest_backup=$(find "$BACKUP_BASE_DIR" -maxdepth 1 -type d -name "local_db_*" | sort -r | head -1 | xargs basename | sed 's/local_db_//')
        elif [ -n "$(ls -t "$BACKUP_BASE_DIR"/local_db_*.info 2>/dev/null | head -1)" ]; then
            latest_backup=$(ls -t "$BACKUP_BASE_DIR"/local_db_*.info 2>/dev/null | head -1 | xargs basename | sed 's/local_db_//' | sed 's/.info//')
        fi
        
        if [ -n "$latest_backup" ]; then
            echo "Example: pnpm restore-local-db $latest_backup"
        else
            echo "Example: pnpm restore-local-db BRANCH_YYYYMMDD_HHMMSS"
        fi
    else
        echo "❌ No backups found in $BACKUP_BASE_DIR"
        echo "Create a backup first with: pnpm backup-local-db"
    fi
    exit 0
fi

# Try to find backup in new subfolder structure first
BACKUP_DIR="$BACKUP_BASE_DIR/local_db_$TIMESTAMP"
INFO_FILE="$BACKUP_DIR/local_db_$TIMESTAMP.info"
ROLES_FILE="$BACKUP_DIR/local_db_${TIMESTAMP}_roles.sql"
SCHEMA_FILE="$BACKUP_DIR/local_db_${TIMESTAMP}_schema.sql"  
DATA_FILE="$BACKUP_DIR/local_db_${TIMESTAMP}_data.sql"
RESTORE_SCRIPT="$BACKUP_DIR/local_db_${TIMESTAMP}_restore.sh"

# If not found in subfolder, try legacy location (files directly in backup directory)  
if [ ! -f "$INFO_FILE" ]; then
    BACKUP_DIR="$BACKUP_BASE_DIR"
    INFO_FILE="$BACKUP_DIR/local_db_$TIMESTAMP.info"
    ROLES_FILE="$BACKUP_DIR/local_db_${TIMESTAMP}_roles.sql"
    SCHEMA_FILE="$BACKUP_DIR/local_db_${TIMESTAMP}_schema.sql"  
    DATA_FILE="$BACKUP_DIR/local_db_${TIMESTAMP}_data.sql"
    RESTORE_SCRIPT="$BACKUP_DIR/local_db_${TIMESTAMP}_restore.sh"
fi

# Check if backup exists (check for info file and core files)
if [ ! -f "$INFO_FILE" ]; then
    echo "❌ Error: Backup not found: $TIMESTAMP"
    echo ""
    echo "Available backups:"
    # Show available backups from both locations
    find "$BACKUP_BASE_DIR" -maxdepth 1 -type d -name "local_db_*" 2>/dev/null | sed 's/.*local_db_/  /' || true
    ls -la "$BACKUP_BASE_DIR"/local_db_*.info 2>/dev/null | sed 's/.*local_db_/  (legacy) /' | sed 's/\.info$//' || true
    [ -z "$(find "$BACKUP_BASE_DIR" -maxdepth 1 -type d -name "local_db_*" 2>/dev/null)$(ls "$BACKUP_BASE_DIR"/local_db_*.info 2>/dev/null)" ] && echo "  No backups found"
    exit 1
fi

# Check if all required files exist
missing_files=()
[ ! -f "$ROLES_FILE" ] && missing_files+=("roles")
[ ! -f "$SCHEMA_FILE" ] && missing_files+=("schema") 
[ ! -f "$DATA_FILE" ] && missing_files+=("data")

if [ ${#missing_files[@]} -gt 0 ]; then
    echo "❌ Error: Missing backup files for $TIMESTAMP: ${missing_files[*]}"
    echo "Required files:"
    echo "  📋 $ROLES_FILE"
    echo "  🏗️ $SCHEMA_FILE"
    echo "  📊 $DATA_FILE"
    exit 1
fi

echo "🔄 Restoring database from backup: $TIMESTAMP"

# Show backup info if available
if [ -f "$INFO_FILE" ]; then
    echo ""
    echo "📋 Backup Information:"
    cat "$INFO_FILE"
    echo ""
fi

# Check if Supabase is running
if ! supabase status > /dev/null 2>&1; then
    echo "❌ Error: Supabase is not running"
    echo "Please start Supabase with: supabase start"
    exit 1
fi

# Confirm restore
echo "⚠️ This will replace your current database with the backup."
echo "Are you sure? (y/N)"
read -r confirm

if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
    echo "❌ Restore cancelled"
    exit 0
fi

echo "🗄️ Resetting database and applying backup..."

# Reset and restore
supabase db reset --local

# Get database URL from Supabase CLI
DB_URL=$(supabase status --output env | grep "DB_URL=" | cut -d'"' -f2)

if [ -z "$DB_URL" ]; then
    echo "❌ Error: Could not get database URL. Is Supabase running?"
    exit 1
fi

echo "📦 Restoring from backup files using Supabase best practices..."
echo "   📋 Roles: $(basename "$ROLES_FILE")"
echo "   🏗️ Schema: $(basename "$SCHEMA_FILE")"  
echo "   📊 Data: $(basename "$DATA_FILE")"

# Use official Supabase restore approach with single transaction
docker run --rm -i --network host -v "$(pwd):/workspace" -w /workspace postgres:15 \
    psql "$DB_URL" \
    --single-transaction \
    --variable ON_ERROR_STOP=1 \
    --file "$ROLES_FILE" \
    --file "$SCHEMA_FILE" \
    --command 'SET session_replication_role = replica' \
    --file "$DATA_FILE"

echo "✅ Database restored successfully!"
echo "🎯 Restored from backup: $TIMESTAMP"
echo "📁 Files used:"
echo "   📋 $(basename "$ROLES_FILE")"
echo "   🏗️ $(basename "$SCHEMA_FILE")"
echo "   📊 $(basename "$DATA_FILE")" 
