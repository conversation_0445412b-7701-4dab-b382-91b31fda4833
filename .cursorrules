# PayloadCMS + Supabase Safety Rules

## CRITICAL DATABASE SAFETY RULES

### 🚨 NEVER GENERATE PAYLOADCMS MIGRATIONS
- NEVER suggest or run `payload generate:migration` or `pnpm payload generate:migration`
- NEVER suggest or run `payload migrate` or `pnpm payload migrate`
- PayloadCMS migrations can DROP ENTIRE DATABASES and destroy all data
- Always use Supabase migrations instead for database schema changes

### ✅ SAFE DATABASE CHANGE WORKFLOW
When making PayloadCMS collection changes that require database schema updates:

1. **Make collection changes** in PayloadCMS config files
2. **Create Supabase migration** manually in `supabase/migrations/`
3. **Use incremental ALTER TABLE statements** - never DROP/CREATE
4. **Test on development database first**
5. **Push code + migration together**
6. **Let Supabase GitHub integration handle production migration**

### 📝 SUPABASE MIGRATION TEMPLATE
For PayloadCMS collection changes, use this pattern:
```sql
-- supabase/migrations/YYYYMMDD_HHMMSS_description.sql
-- Add new columns for PayloadCMS collection changes

ALTER TABLE public.table_name 
ADD COLUMN IF NOT EXISTS new_column_name varchar,
ADD COLUMN IF NOT EXISTS new_column_width numeric;

-- Add indexes if needed
CREATE INDEX IF NOT EXISTS table_new_column_idx 
ON public.table_name USING btree (new_column_name);
```

### 🔒 PAYLOADCMS CONFIG SAFETY
Always ensure these settings in `payload.config.ts`:
```typescript
db: postgresAdapter({
  push: false, // CRITICAL: Disable automatic schema sync
  disableMigrations: process.env.NODE_ENV === 'production', // Disable in prod
  prodMigrations: process.env.NODE_ENV === 'production' ? migrations : undefined,
})
```

### ⚠️ RED FLAGS - STOP IMMEDIATELY IF YOU SEE:
- Migration files that contain `DROP TABLE` or `DROP TYPE`
- Migration files over 100 lines
- PayloadCMS asking about "dev mode changes" during build
- Any migration mentioning `CASCADE`
- Migration files that recreate entire database schema

### 🛡️ BUILD-TIME SAFETY
For any service initialization (OpenAI, health checks, database connections):
```typescript
function isBuildTime(): boolean {
  return (
    (process.env.NODE_ENV === 'production' && 
     process.env.NEXT_PHASE === 'phase-production-build') ||
    process.argv.includes('build') ||
    process.env.BUILDING === 'true'
  );
}

// Only initialize services during runtime, not build time
if (!isBuildTime()) {
  // Initialize services here
}
```

### 📦 PACKAGE MANAGEMENT
- Always use package managers (npm, pnpm, yarn) for dependency changes
- Never manually edit package.json, requirements.txt, etc.
- Use `pnpm add/remove` instead of manual edits

### 🔍 BEFORE SUGGESTING ANY DATABASE CHANGES:
1. Ask: "Does this require a database schema change?"
2. If yes: "Use Supabase migration, NOT PayloadCMS migration"
3. Always suggest testing in development first
4. Always suggest backing up production data first

### 💾 DATA SAFETY PRIORITIES
- User has hundreds of articles - data loss is catastrophic
- Always prioritize data safety over convenience
- When in doubt, suggest the safer approach
- Never suggest commands that could delete data without explicit confirmation

### 🚫 FORBIDDEN COMMANDS
Never suggest these commands:
- `payload generate:migration`
- `payload migrate`
- `payload migrate:fresh`
- `payload migrate:reset`
- Any command that drops/recreates database tables

### ✅ SAFE COMMANDS
These are safe to suggest:
- `pnpm build`
- `pnpm dev`
- `payload generate:types`
- Supabase CLI commands for migrations
- Standard Next.js commands

## REMEMBER: Data safety is paramount. When suggesting any changes that might affect the database, always use the safe Supabase migration approach and test in development first.
