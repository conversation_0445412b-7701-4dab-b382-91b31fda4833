# Article Publication Validation Checks - Implementation Plan

**Date**: 2025-07-15  
**Status**: Planning Phase  
**Priority**: High  
**Estimated Time**: 2-3 weeks

## Overview

Implement validation checks to ensure articles meet quality standards before publication. This includes required featured image and SEO plugin validation for title, description, and meta image.

## Requirements

### Core Validation Requirements

1. **Featured Image** - Must be present before publishing
2. **SEO Title** - Must be present and meet quality standards
3. **SEO Description** - Must be present and meet quality standards
4. **SEO Meta Image** - Must be present and properly configured
5. **Integration** - Leverage existing SEO plugin's built-in validation checks

### User Experience Requirements

- Clear error messages with actionable guidance
- Visual validation status indicators in admin interface
- Prominent auto-generation buttons for SEO fields
- Non-disruptive workflow for compliant articles

### Technical Requirements

- No data loss or schema changes
- Backward compatibility with existing published articles
- Grandfather clause for already published content
- Feature flag for gradual rollout

## Current State Analysis

### Existing Infrastructure ✅

- Articles collection with `featuredImage` field (upload type)
- SEO plugin already integrated with auto-generation functionality
- SEO fields: `meta.title`, `meta.description`, `meta.image`
- Workflow stages: `candidate-article` → `translated` → `ready-for-review` → `published`

### Validation Gaps ❌

- No validation for featured image before publication
- No validation for SEO fields before publication
- No integration with SEO plugin's validation checks
- Users can publish without meeting quality standards

## Implementation Strategy

### Phase 1: Core Validation Implementation (Week 1)

#### 1.1 Create Validation Framework

**Files to Create:**

- `src/collections/Articles/validation.ts` - Core validation logic
- `src/collections/Articles/errors.ts` - Enhanced error handling
- `src/collections/Articles/config.ts` - Feature flags and configuration

**Key Functions:**

- `validateFeaturedImage()` - Check featured image presence
- `validateSEOFields()` - Check SEO field completeness
- `validateSEOQuality()` - Check SEO field quality standards
- `validatePublication()` - Main validation orchestrator
- `isAlreadyPublished()` - Grandfather clause logic

#### 1.2 Update Articles Collection

**File to Modify:**

- `src/collections/Articles.ts` - Add validation hooks

**Changes:**

- Add `beforeChange` hook for publication validation
- Add `beforeValidate` hook for published metadata
- Implement error handling with user-friendly messages
- Add grandfather clause for existing published articles

### Phase 2: Admin Interface Enhancements (Week 2)

#### 2.1 Create Publication Status Component

**Files to Create:**

- `src/components/admin/publication-status/PublicationStatus.tsx`
- `src/components/admin/publication-status/index.ts`
- `src/styles/admin/publication-status.css`

**Features:**

- Real-time validation status display
- Visual indicators (✅ ❌) for each requirement
- Helpful messages for missing requirements
- Integration with existing admin interface

#### 2.2 Enhance User Guidance

**Updates:**

- Add publication status indicator to sidebar
- Improve SEO tab visibility and guidance
- Add contextual help text for validation requirements
- Enhance error messages with specific suggestions

### Phase 3: Testing and Deployment (Week 3)

#### 3.1 Safety Testing

**Test Cases:**

- Existing published articles remain accessible
- New articles properly validate before publication
- Error messages are clear and actionable
- Auto-generation buttons work correctly
- Grandfather clause functions properly

#### 3.2 Deployment Strategy

**Rollout Plan:**

1. Deploy with validation disabled (`PUBLICATION_VALIDATION_ENABLED=false`)
2. Run safety check on existing published articles
3. Enable validation for new articles only
4. Monitor validation success rates
5. Full validation rollout

## Implementation Details

### Validation Logic Flow

```typescript
// Publication attempt
if (workflowStage === 'published') {
  // Check grandfather clause
  if (isAlreadyPublished && originalDoc.workflowStage === 'published') {
    return; // Skip validation
  }

  // Run validation checks
  const validation = await validatePublication(data, req, operation);

  // Block if validation fails
  if (!validation.isValid) {
    throw new Error(formatValidationErrors(validation.errors));
  }
}
```

### Error Message Format

```
Article cannot be published due to the following issues:
• Featured image is required before publishing
• SEO title is required before publishing. Use the auto-generate button to create one.
• SEO description is required before publishing. Use the auto-generate button to create one.
• SEO meta image is required before publishing. Use the auto-generate button to create one.

Please complete all required fields before publishing.
```

### Admin Interface Integration

- **Sidebar Position**: Below workflow stage selector
- **Visual Design**: Green/red status indicators with clear messaging
- **Conditional Display**: Only shown when workflow stage is "published"
- **Interactive Elements**: Links to relevant sections for quick fixes

## Safety Measures

### Data Protection

- **No Schema Changes**: Work with existing fields only
- **Grandfather Clause**: Existing published articles bypass validation
- **Feature Flags**: Gradual rollout with ability to disable
- **Database Backup**: Full backup before implementation

### Rollback Plan

- **Immediate**: Disable validation via feature flag
- **Emergency**: Revert to previous Articles collection version
- **Recovery**: Restore from database backup if needed

### Monitoring

- **Validation Success Rate**: Track percentage of successful validations
- **User Feedback**: Monitor for usability issues
- **Performance**: Ensure validation doesn't impact save performance
- **Error Tracking**: Log validation failures for analysis

## Success Metrics

### Functional Metrics

- [ ] 100% of published articles have featured images
- [ ] 100% of published articles have complete SEO fields
- [ ] 0% data loss during implementation
- [ ] 100% backward compatibility maintained

### User Experience Metrics

- [ ] Clear error messages for validation failures
- [ ] Visual validation status indicators working
- [ ] Auto-generation buttons easily accessible
- [ ] No disruption to existing workflow

### Technical Metrics

- [ ] Validation performance < 100ms
- [ ] Feature flag system operational
- [ ] Grandfather clause functioning correctly
- [ ] Error handling robust and user-friendly

## Risk Assessment

### High Risk Items

1. **Data Safety**: Existing published articles must remain accessible
   - **Mitigation**: Grandfather clause + comprehensive testing
2. **User Disruption**: New validation might block legitimate publishing
   - **Mitigation**: Clear error messages + prominent auto-generation

### Medium Risk Items

1. **Performance Impact**: Validation might slow down save operations
   - **Mitigation**: Optimize validation logic + async where possible
2. **SEO Plugin Integration**: Validation might conflict with plugin
   - **Mitigation**: Leverage plugin's existing validation + thorough testing

### Low Risk Items

1. **Feature Flag Complexity**: Managing gradual rollout
   - **Mitigation**: Simple boolean flag + clear documentation

## Timeline

### Week 1: Foundation

- **Days 1-2**: Create validation framework and core logic
- **Days 3-4**: Update Articles collection with hooks
- **Day 5**: Initial testing and refinement

### Week 2: User Experience

- **Days 1-2**: Create publication status component
- **Days 3-4**: Integrate with admin interface
- **Day 5**: User experience testing and refinement

### Week 3: Deployment

- **Days 1-2**: Comprehensive testing and bug fixes
- **Days 3-4**: Gradual deployment with monitoring
- **Day 5**: Full rollout and documentation

## Next Actions

### Immediate (Today)

1. **Review Plan**: Team review and approval of implementation approach
2. **Environment Setup**: Ensure development environment is ready
3. **Database Backup**: Create full backup before starting development

### This Week

1. **Start Phase 1**: Begin core validation implementation
2. **Create Validation Framework**: Build validation utility functions
3. **Update Articles Collection**: Add validation hooks

### Next Week

1. **Admin Interface**: Create publication status component
2. **User Experience**: Enhance guidance and error messages
3. **Testing**: Comprehensive validation testing

## Resources

### Documentation References

- PayloadCMS Hooks Documentation
- SEO Plugin API Reference
- React Component Development Guide
- TypeScript Best Practices

### Related Files

- `src/collections/Articles.ts` - Main collection configuration
- `src/components/admin/` - Admin interface components
- `payload-types.ts` - TypeScript definitions
- `planning.mdc` - Strategic planning document
- `payload.mdc` - Technical implementation guide

## Notes

- This implementation prioritizes data safety and user experience
- The grandfather clause ensures no existing content is affected
- Feature flags allow for safe gradual rollout
- Clear error messages guide users to complete requirements
- Auto-generation buttons make compliance easy for users

---

**Last Updated**: 2025-07-15  
**Next Review**: After Phase 1 completion  
**Status**: Ready for implementation
