# Biome to ESLint + Prettier Migration Plan

**Date:** 2025-01-08
**Project:** <PERSON><PERSON><PERSON> Blick
**Objective:** Safely migrate from Biome to ESLint + Prettier for code formatting and linting
**Estimated Duration:** 3-4 hours (8 tasks × 20-30 minutes each)

## 📋 Migration Overview

### Current State Analysis
Based on comprehensive codebase analysis, Biome is configured in the following locations:

**Configuration Files:**
- `biome.json` - Main configuration file
- `package.json` - Dependencies and scripts
- `.vscode/settings.json` - VS Code formatter settings
- `.vscode/extensions.json` - Recommended extension
- `borsenblick.code-workspace` - Workspace settings

**Key Configuration Details:**
- **Version**: @biomejs/biome ^2.0.0
- **Scope**: `./src` directory only
- **Formatter**: Tabs (width 2), single quotes, ES5 trailing commas, always semicolons
- **Linter**: Recommended rules enabled
- **Scripts**: `lint` and `format` commands

**Current Status**: ~350 minor style warnings (no errors)

### Migration Strategy
**Approach**: Complete replacement with compatibility-first configuration to maintain existing code style and prevent formatting conflicts.

---

## 🛠️ Task Breakdown

### Task 1: Safety Analysis & Backup Creation
  **Duration:** 20 minutes
  **Risk Level:** Low
  **Dependencies:** None

#### Objectives
  - Document current Biome configuration
  - Create rollback procedures
  - Backup current codebase state

#### Implementation Steps
  1. **Document Current Configuration**
```bash
# Export current Biome config
cat biome.json > .ai/04-Doing/biome-backup.json

# Document current package.json scripts
jq .scripts package.json > .ai/04-Doing/package-scripts-backup.json

# Document VS Code settings
cp .vscode/settings.json .ai/04-Doing/vscode-settings-backup.json
```

  2. **Create Git Commit Point**
```bash
git add .
git commit -m "Pre-migration backup: Biome configuration snapshot

- Current Biome config: v2.0.0
- Scope: ./src directory
- ~350 minor style warnings (no errors)
- Safe rollback point before ESLint + Prettier migration"
```

  3. **Test Current Biome Status**
```bash
# Verify current state
pnpm lint 2>&1 | tee .ai/04-Doing/current-biome-lint-output.txt
pnpm format --check 2>&1 | tee .ai/04-Doing/current-biome-format-output.txt
```

#### Validation Criteria
  - [ ] All current configurations documented
  - [ ] Git commit created successfully
  - [ ] Current lint/format status captured
  - [ ] Rollback procedure documented

#### Rollback Procedure
```bash
# If migration fails, restore Biome
git checkout HEAD~1 -- biome.json package.json .vscode/
pnpm install
```

---

### Task 2: Complete Biome Removal
**Duration:** 25 minutes
**Risk Level:** Medium
**Dependencies:** Task 1

#### Objectives
- Remove all Biome-related files and configurations
- Clean up package.json dependencies and scripts
- Remove VS Code Biome integration

#### Implementation Steps
1. **Remove Configuration Files**
```bash
# Remove main config
rm biome.json

# Verify removal
ls -la | grep biome
```

2. **Update package.json**
```bash
# Remove Biome dependency
pnpm remove @biomejs/biome

# Remove Biome scripts (manual edit required)
# Remove: "lint": "biome lint ./src"
# Remove: "format": "biome format --write ./src"
```

3. **Update VS Code Configuration**

**File:** `.vscode/settings.json`
```json
{
  "npm.packageManager": "pnpm",
  "editor.defaultFormatter": null,
  "prettier.enable": true,
  "editor.formatOnSave": true,
  "editor.formatOnSaveMode": "file",
  "editor.insertSpaces": true,
  "editor.tabSize": 2,
  "typescript.tsdk": "node_modules/typescript/lib",
  "cSpell.words": ["payloadcms", "borsenblick"],
  "workbench.colorCustomizations": {
    "activityBar.activeBackground": "#1A1A1A",
    "activityBar.background": "#121212",
    "activityBar.foreground": "#CAA870",
    "activityBar.inactiveForeground": "#CAA87099",
    "activityBarBadge.background": "#B08D57",
    "activityBarBadge.foreground": "#121212",
    "commandCenter.border": "#CAA87080",
    "sash.hoverBorder": "#B08D57",
    "statusBar.background": "#121212",
    "statusBar.foreground": "#CAA870",
    "statusBarItem.hoverBackground": "#1A1A1A",
    "statusBarItem.remoteBackground": "#1A1A1A",
    "statusBarItem.remoteForeground": "#CAA870",
    "titleBar.activeBackground": "#121212",
    "titleBar.activeForeground": "#CAA870",
    "titleBar.inactiveBackground": "#12121299",
    "titleBar.inactiveForeground": "#CAA87099"
  }
}
```

4. **Update Extensions Configuration**

**File:** `.vscode/extensions.json`
```json
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "streetsidesoftware.code-spell-checker",
    "ms-vsliveshare.vsliveshare",
    "johnpapa.vscode-peacock"
  ]
}
```

5. **Update Workspace Configuration**

**File:** `borsenblick.code-workspace`
```json
{
  "folders": [
    {
      "path": "."
    }
  ],
  "settings": {
    "npm.packageManager": "pnpm",
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "prettier.enable": true,
    "editor.formatOnSave": true,
    "editor.formatOnSaveMode": "file",
    "editor.insertSpaces": true,
    "editor.tabSize": 2,
    "[typescript]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode",
      "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit"
      }
    },
    "[typescriptreact]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode",
      "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit"
      }
    },
    "[javascript]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode",
      "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit"
      }
    },
    "[json]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "typescript.tsdk": "node_modules/typescript/lib",
    "cSpell.words": [
      "payloadcms",
      "borsenblick",
      "supabase",
      "tailwindcss"
    ]
  },
  "extensions": {
    "recommendations": [
      "esbenp.prettier-vscode",
      "dbaeumer.vscode-eslint",
      "bradlc.vscode-tailwindcss",
      "ms-vscode.vscode-typescript-next",
      "streetsidesoftware.code-spell-checker",
      "ms-vsliveshare.vsliveshare",
      "johnpapa.vscode-peacock"
    ]
  }
}
```

#### Validation Criteria
- [ ] biome.json removed
- [ ] @biomejs/biome removed from package.json
- [ ] Biome scripts removed from package.json
- [ ] VS Code settings updated
- [ ] Extensions configuration updated
- [ ] Workspace configuration updated
- [ ] No references to "biome" in codebase

---

### Task 3: ESLint Installation & Configuration
  **Duration:** 30 minutes
  **Risk Level:** Medium
  **Dependencies:** Task 2

#### Objectives
  - Install ESLint with Next.js/TypeScript support
  - Configure rules appropriate for PayloadCMS project
  - Set up TypeScript-specific linting

#### Implementation Steps
  1. **Install ESLint Dependencies**
```bash
# Core ESLint packages
pnpm add -D eslint @typescript-eslint/eslint-plugin @typescript-eslint/parser

# Next.js specific
pnpm add -D eslint-config-next

# Prettier integration (prevents conflicts)
pnpm add -D eslint-config-prettier

# Additional useful plugins
pnpm add -D eslint-plugin-import eslint-plugin-jsx-a11y eslint-plugin-react-hooks
```

  2. **Create ESLint Configuration**

  **File:** `.eslintrc.json`
```json
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "root": true,
  "env": {
    "node": true,
    "browser": true,
    "es2022": true
  },
  "parserOptions": {
    "ecmaVersion": 2022,
    "sourceType": "module",
    "project": "./tsconfig.json"
  },
  "rules": {
    "@typescript-eslint/no-unused-vars": "warn",
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/prefer-const": "error",
    "@typescript-eslint/no-inferrable-types": "off",
    "import/order": [
      "error",
      {
        "groups": [
          "builtin",
          "external",
          "internal",
          "parent",
          "sibling",
          "index"
        ],
        "newlines-between": "always"
      }
    ]
  },
  "overrides": [
    {
      "files": ["*.ts", "*.tsx"],
      "rules": {
        "@typescript-eslint/explicit-function-return-type": "off"
      }
    }
  ],
  "ignorePatterns": [
    "node_modules/",
    ".next/",
    "dist/",
    "build/",
    "*.js",
    "*.mjs"
  ]
}
```

  3. **Create ESLint Ignore File**

  **File:** `.eslintignore`
```
# Build outputs
.next/
dist/
build/

# Dependencies
node_modules/

# Generated files
payload-types.ts
src/payload-types.ts

# Config files
*.config.js
*.config.mjs
tailwind.config.js
postcss.config.mjs

# Test files
__tests__/
*.test.ts
*.test.tsx
*.spec.ts
*.spec.tsx

# Logs
logs/
*.log

# Environment files
.env*
```

#### Validation Criteria
  - [ ] ESLint installed successfully
  - [ ] Configuration file created
  - [ ] Ignore file created
  - [ ] Next.js integration configured
  - [ ] TypeScript support configured
  - [ ] Prettier integration configured

---

### Task 4: Prettier Installation & Configuration
**Duration:** 25 minutes
**Risk Level:** Low
**Dependencies:** Task 3

#### Objectives
- Install Prettier with ESLint integration
- Configure formatting rules to match current Biome style
- Ensure compatibility with existing code

#### Implementation Steps
1. **Install Prettier Dependencies**
```bash
# Core Prettier
pnpm add -D prettier

# ESLint integration (already installed in Task 3)
# eslint-config-prettier
```

2. **Create Prettier Configuration**

**File:** `.prettierrc.json`
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "endOfLine": "lf",
  "bracketSpacing": true,
  "bracketSameLine": false,
  "arrowParens": "avoid",
  "overrides": [
    {
      "files": "*.json",
      "options": {
        "tabWidth": 2
      }
    }
  ]
}
```

3. **Create Prettier Ignore File**

**File:** `.prettierignore`
```
# Build outputs
.next/
dist/
build/

# Dependencies
node_modules/

# Generated files
payload-types.ts
src/payload-types.ts

# Logs
logs/
*.log

# Lock files
pnpm-lock.yaml
package-lock.json
yarn.lock

# Environment files
.env*

# Config files that should maintain their formatting
*.config.js
*.config.mjs
```

#### Validation Criteria
- [ ] Prettier installed successfully
- [ ] Configuration file created
- [ ] Ignore file created
- [ ] Settings match current Biome style
- [ ] ESLint integration configured

---

### Task 5: Scripts & Commands Update
  **Duration:** 20 minutes
  **Risk Level:** Low
  **Dependencies:** Task 4

#### Objectives
  - Update package.json scripts
  - Create comprehensive linting and formatting commands
  - Ensure compatibility with existing workflow

#### Implementation Steps
  1. **Update package.json Scripts**
```json
{
  "scripts": {
    "build": "cross-env NODE_OPTIONS=--no-deprecation next build",
    "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev",
    "devsafe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev",
    "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap",
    "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types",
    "lint": "eslint src --ext .ts,.tsx,.js,.jsx",
    "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix",
    "format": "prettier --write src",
    "format:check": "prettier --check src",
    "lint:all": "pnpm lint && pnpm format:check",
    "fix:all": "pnpm lint:fix && pnpm format",
    "lint:next": "cross-env NODE_OPTIONS=--no-deprecation next lint",
    "payload": "cross-env NODE_OPTIONS=--no-deprecation payload",
    "start": "cross-env NODE_OPTIONS=--no-deprecation next start",
    "check:openai": "node scripts/check-openai-status.js",
    "migrate:create": "cross-env NODE_OPTIONS=--no-deprecation payload migrate:create",
    "migrate": "cross-env NODE_OPTIONS=--no-deprecation payload migrate",
    "migrate:status": "cross-env NODE_OPTIONS=--no-deprecation payload migrate:status",
    "migrate:fresh": "cross-env NODE_OPTIONS=--no-deprecation payload migrate:fresh",
    "migrate:reset": "cross-env NODE_OPTIONS=--no-deprecation payload migrate:reset",
    "ci": "cross-env NODE_OPTIONS=--no-deprecation payload migrate && pnpm build"
  }
}
```

  2. **Create Development Helper Scripts**

  **File:** `scripts/lint-staged.js`
```javascript
#!/usr/bin/env node

/**
 * Pre-commit linting and formatting
 * Run: node scripts/lint-staged.js
 */

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🔍 Running pre-commit checks...');

try {
  console.log('📝 Checking TypeScript...');
  execSync('pnpm tsc --noEmit', { stdio: 'inherit' });

  console.log('🔧 Running ESLint...');
  execSync('pnpm lint:fix', { stdio: 'inherit' });

  console.log('💄 Running Prettier...');
  execSync('pnpm format', { stdio: 'inherit' });

  console.log('✅ All checks passed!');
} catch (error) {
  console.error('❌ Pre-commit checks failed');
  process.exit(1);
}
```

#### Validation Criteria
  - [ ] package.json scripts updated
  - [ ] Individual lint and format commands work
  - [ ] Combined commands work
  - [ ] Helper scripts created
  - [ ] Commands match expected output

---

### Task 6: IDE Integration & Developer Experience
**Duration:** 20 minutes
**Risk Level:** Low
**Dependencies:** Task 5

#### Objectives
- Finalize VS Code integration
- Create development documentation
- Ensure smooth developer experience

#### Implementation Steps
1. **Verify VS Code Integration**
  - Test that ESLint extension works
  - Test that Prettier extension works
  - Verify format-on-save functionality
  - Test code actions on save

2. **Create Developer Documentation**

**File:** `docs/development-setup.md`
```markdown
# Development Setup Guide

## Code Quality Tools

This project uses ESLint + Prettier for code quality and formatting.

### Commands

```bash
# Lint code
pnpm lint

# Fix lint issues
pnpm lint:fix

# Format code
pnpm format

# Check formatting
pnpm format:check

# Run all checks
pnpm lint:all

# Fix all issues
pnpm fix:all
```

### VS Code Setup

Install recommended extensions:
- ESLint
- Prettier

Settings are configured automatically via workspace configuration.

### Pre-commit Workflow

```bash
# Run before committing
pnpm fix:all
```
```

3. **Update Main README**

**File:** `README.md` (update sections)
```markdown
- **Linting/Formatting**: ESLint + Prettier

## Development Workflow

- **Code Style**: This project uses ESLint + Prettier for linting and formatting
- **Type Safety**: Run `pnpm type-check` to verify TypeScript types
- **Code Quality**: Run `pnpm lint:all` to check all code quality rules
- **Testing**: Run `pnpm test` to run all tests
```

#### Validation Criteria
- [ ] VS Code integration working
- [ ] Developer documentation created
- [ ] README updated
- [ ] Format-on-save working
- [ ] Code actions working

---

### Task 7: Documentation Update
  **Duration:** 15 minutes
  **Risk Level:** Very Low
  **Dependencies:** Task 6

#### Objectives
  - Update all project documentation
  - Remove Biome references
  - Add ESLint + Prettier information

#### Implementation Steps
  1. **Update Project Overview Documentation**

  **File:** `.ai/01-Project/overview.md`
```markdown
# Update line 330 and 713-715
- **Code Quality**: ESLint + Prettier for linting and formatting

# Update line 713-715
- ESLint configuration in `.eslintrc.json`
- Prettier configuration in `.prettierrc.json`
- Use ESLint + Prettier for linting and formatting
```

  2. **Update Handover Documentation**

  **File:** `HANDOVER_ENGLISH_ONLY_ARCHITECTURE.md`
```markdown
# Update development commands section
### **Code Quality**
```bash
   # Lint check
  pnpm lint

   # Fix lint issues
  pnpm lint:fix

   # Format code
  pnpm format

   # Check all code quality
  pnpm lint:all
```
```

  3. **Update Sprint Documentation**
      - Search for Biome references in `.ai/05-Done/` directory
      - Update historical documentation to reflect current state
      - Add migration notes

#### Validation Criteria
  - [ ] Main project documentation updated
  - [ ] Handover documentation updated
  - [ ] Historical documentation noted
  - [ ] All Biome references removed/updated

---

### Task 8: Testing & Validation
**Duration:** 30 minutes
**Risk Level:** Medium
**Dependencies:** Task 7

#### Objectives
- Run comprehensive testing of new setup
- Validate formatting consistency
- Ensure no regressions in code quality
- Document final state

#### Implementation Steps
1. **Run Full Code Quality Check**
```bash
# Clean install to ensure dependencies are correct
rm -rf node_modules pnpm-lock.yaml
pnpm install

# Run type checking
pnpm tsc --noEmit

# Run ESLint
pnpm lint

# Run Prettier check
pnpm format:check

# Run Next.js build to ensure no issues
pnpm build
```

2. **Test VS Code Integration**
  - Open VS Code
  - Create a test file with intentional formatting issues
  - Verify format-on-save works
  - Verify ESLint warnings appear
  - Test code actions on save

3. **Format Entire Codebase**
```bash
# Format all files to ensure consistency
pnpm format

# Check for any remaining issues
pnpm lint:all
```

4. **Document Final State**
```bash
# Capture final state
pnpm lint 2>&1 | tee .ai/04-Doing/final-eslint-output.txt
pnpm format:check 2>&1 | tee .ai/04-Doing/final-prettier-output.txt

# Create final commit
git add .
git commit -m "✅ Complete migration: Biome → ESLint + Prettier

Migration Summary:
- Removed: Biome v2.0.0
- Added: ESLint + Prettier with Next.js/TypeScript support
- Updated: VS Code settings, scripts, documentation
- Maintained: Existing code style (tabs → spaces, single quotes)
- Status: All code quality checks passing"
```

#### Validation Criteria
- [ ] All dependencies installed correctly
- [ ] TypeScript compilation successful
- [ ] ESLint passes with no errors
- [ ] Prettier formatting consistent
- [ ] Next.js build successful
- [ ] VS Code integration working
- [ ] All files formatted consistently
- [ ] Final documentation complete

---

## 🔄 Rollback Procedures

### If Migration Fails at Any Point

  1. **Immediate Rollback**
```bash
# Reset to backup commit
git reset --hard HEAD~1

# Reinstall Biome
pnpm install

# Verify rollback
pnpm lint
pnpm format --check
```

  2. **Partial Rollback (specific files)**
```bash
# Restore specific files
git checkout HEAD~1 -- package.json .vscode/ biome.json

# Reinstall dependencies
pnpm install
```

  3. **Manual Rollback (if needed)**
```bash
# Remove ESLint/Prettier files
rm .eslintrc.json .prettierrc.json .eslintignore .prettierignore

# Restore Biome config
cp .ai/04-Doing/biome-backup.json biome.json

# Restore package.json
cp .ai/04-Doing/package-scripts-backup.json package.json

# Restore VS Code settings
cp .ai/04-Doing/vscode-settings-backup.json .vscode/settings.json

# Reinstall
pnpm install
```

---

## 📊 Success Metrics

### Pre-Migration State
- **Tool**: Biome v2.0.0
- **Lint Issues**: ~350 minor style warnings
- **Scope**: `./src` directory
- **Style**: Tabs, single quotes, ES5 trailing commas

### Post-Migration Target
- **Tools**: ESLint + Prettier
- **Lint Issues**: < 10 errors (warnings acceptable)
- **Scope**: `./src` directory
- **Style**: Spaces, single quotes, ES5 trailing commas (maintained)
- **Performance**: Similar or better than Biome
- **Developer Experience**: Improved with better IDE integration

### Key Performance Indicators
- [ ] TypeScript compilation: ✅ Success
- [ ] ESLint checks: ✅ No errors
- [ ] Prettier formatting: ✅ Consistent
- [ ] Next.js build: ✅ Success
- [ ] VS Code integration: ✅ Working
- [ ] Developer workflow: ✅ Improved

---

## 🏁 Completion Checklist

### Configuration Files
  - [ ] `biome.json` removed
  - [ ] `.eslintrc.json` created and configured
  - [ ] `.prettierrc.json` created and configured
  - [ ] `.eslintignore` created
  - [ ] `.prettierignore` created

### Package Management
  - [ ] `@biomejs/biome` removed
  - [ ] ESLint packages installed
  - [ ] Prettier packages installed
  - [ ] Scripts updated in package.json

### IDE Integration
  - [ ] VS Code settings updated
  - [ ] Extensions configuration updated
  - [ ] Workspace configuration updated
  - [ ] Format-on-save working

### Documentation
  - [ ] Project README updated
  - [ ] Developer documentation created
  - [ ] Handover documentation updated
  - [ ] Migration notes documented

### Testing & Validation
  - [ ] All code quality checks passing
  - [ ] Codebase formatted consistently
  - [ ] No regressions in functionality
  - [ ] Final commit created

---

## 🚀 Next Steps After Migration

1. **Team Communication**
  - Inform team members about the migration
  - Share new development workflow
  - Update any team documentation

2. **CI/CD Integration** (if applicable)
  - Update any GitHub Actions workflows
  - Modify deployment scripts if needed
  - Test CI/CD pipeline

3. **Monitoring**
  - Monitor for any issues in the first week
  - Collect feedback from team members
  - Make adjustments as needed

4. **Optimization**
  - Fine-tune ESLint rules based on usage
  - Optimize Prettier settings if needed
  - Consider additional plugins for enhanced DX

---

  **Document Version:** 1.0
  **Last Updated:** 2025-01-08
  **Migration Lead:** Assistant
  **Review Required:** Yes (before execution)
