# Sprint 6: Field Renaming Refactor - "Enhanced" to Generic Names

**Duration**: 4-6 hours  
**Priority**: High (Technical Debt Elimination)  
**Dependencies**: Sprint 1, 2, 3, 4, 5 (All core features completed)  
**Risk Level**: High (System-wide changes)

## 🎯 Sprint Goals

1. **Rename "Enhanced" fields** to generic, clear names
2. **Update all code references** throughout the entire codebase
3. **Migrate database schema** safely with backwards compatibility
4. **Update all tests** to use new field names
5. **Update AI prompts** and pipeline references
6. **Ensure zero data loss** during migration

## 🔍 **COMPREHENSIVE IMPACT ANALYSIS**

### **📊 Fields to Rename:**

| Current Field Name               | New Field Name           | Database Path            | Impact Level |
| -------------------------------- | ------------------------ | ------------------------ | ------------ |
| `englishTab.enhancedTitle`       | `englishTab.title`       | `englishTab.title`       | **HIGH**     |
| `englishTab.enhancedSummary`     | `englishTab.summary`     | `englishTab.summary`     | **HIGH**     |
| `englishTab.enhancedContent`     | `englishTab.content`     | `englishTab.content`     | **CRITICAL** |
| `englishTab.enhancedKeywords`    | `englishTab.keywords`    | `englishTab.keywords`    | **MEDIUM**   |
| `englishTab.enhancedKeyInsights` | `englishTab.keyInsights` | `englishTab.keyInsights` | **MEDIUM**   |

### **🎯 Areas of Impact:**

#### **1. Database & Schema (CRITICAL)**

- PayloadCMS collection definition
- Supabase database columns
- Existing article data migration
- Index updates
- Backup/restore procedures

#### **2. Backend Code (HIGH)**

- API endpoints (`/api/articles/*`)
- Validation services
- Content processors
- Integration services
- Type definitions

#### **3. Frontend Components (HIGH)**

- Form field references
- Display components
- Admin UI components
- Validation logic
- State management

#### **4. AI & Processing (CRITICAL)**

- OpenAI prompts referencing field names
- Content enhancement pipeline
- Translation pipeline
- Processing validation
- Response parsing

#### **5. Testing (HIGH)**

- Unit tests
- Integration tests
- Test fixtures
- Mock data
- Validation tests

#### **6. Configuration (MEDIUM)**

- Environment-specific configs
- Pipeline configurations
- Monitoring and logging
- Error tracking

## 📋 **DETAILED TASKS**

### **Task 6.1: Pre-Migration Analysis & Backup (60 minutes)**

#### **6.1.1: Complete Codebase Audit**

**Files to scan for "enhanced" references:**

```bash
# Search for all field references
grep -r "enhancedTitle" src/
grep -r "enhancedSummary" src/
grep -r "enhancedContent" src/
grep -r "enhancedKeywords" src/
grep -r "enhancedKeyInsights" src/

# Search for API references
grep -r "enhanced" src/app/api/
grep -r "enhanced" src/lib/

# Search for test references
grep -r "enhanced" src/**/*.test.ts
grep -r "enhanced" src/**/*.test.tsx
```

#### **6.1.2: Database Backup Strategy**

```sql
-- Create complete backup before migration
pg_dump borsenblick > pre_field_rename_backup_$(date +%Y%m%d_%H%M%S).sql

-- Create specific table backup
COPY (SELECT * FROM articles) TO 'articles_backup_pre_rename.csv' CSV HEADER;
```

#### **6.1.3: Impact Documentation**

Create comprehensive mapping of every file that needs updating:

```typescript
interface FileImpactMap {
  filePath: string;
  impactLevel: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  fieldReferences: string[];
  estimatedChanges: number;
  testCoverage: boolean;
  rollbackRequired: boolean;
}
```

### **Task 6.2: Database Schema Migration (90 minutes)**

#### **6.2.1: PayloadCMS Collection Update**

**File**: `src/collections/Articles.ts`

**BEFORE (Current Schema):**

```typescript
{
  label: 'English Content',
  fields: [
    {
      name: 'enhancedTitle',
      type: 'text',
      label: 'Enhanced English Title',
      admin: {
        description: 'AI-enhanced English title optimised for international markets'
      }
    },
    {
      name: 'enhancedSummary',
      type: 'textarea',
      label: 'Enhanced English Summary',
      admin: {
        description: 'AI-enhanced English summary (100-150 characters)'
      }
    },
    {
      name: 'enhancedContent',
      type: 'richText',
      label: 'Enhanced English Content',
      admin: {
        description: 'AI-enhanced English content optimised for international markets (600-750 words)'
      }
    },
    {
      name: 'enhancedKeywords',
      type: 'array',
      label: 'Enhanced Keywords',
      fields: [/* ... */]
    },
    {
      name: 'enhancedKeyInsights',
      type: 'array',
      label: 'Enhanced Key Insights',
      fields: [/* ... */]
    }
  ]
}
```

**AFTER (New Schema with Migration Support):**

```typescript
{
  label: 'English Content',
  fields: [
    {
      name: 'title',
      type: 'text',
      label: 'English Title',
      admin: {
        description: 'Primary English title for international markets'
      }
    },
    {
      name: 'summary',
      type: 'textarea',
      label: 'English Summary',
      admin: {
        description: 'English summary (100-150 characters)'
      }
    },
    {
      name: 'content',
      type: 'richText',
      label: 'English Content',
      admin: {
        description: 'Primary English content (600-750 words)'
      }
    },
    {
      name: 'keywords',
      type: 'array',
      label: 'Keywords',
      fields: [/* ... */]
    },
    {
      name: 'keyInsights',
      type: 'array',
      label: 'Key Insights',
      fields: [/* ... */]
    }
  ]
}
```

#### **6.2.2: Database Migration Script**

**File**: `src/migrations/20250122_rename_enhanced_fields.ts`

```typescript
import { MigrateUpArgs, MigrateDownArgs } from '@payloadcms/db-postgres';

export async function up({ payload }: MigrateUpArgs): Promise<void> {
  // Step 1: Add new columns
  await payload.db.drizzle.execute(`
    ALTER TABLE articles 
    ADD COLUMN IF NOT EXISTS "englishTab.title" TEXT,
    ADD COLUMN IF NOT EXISTS "englishTab.summary" TEXT,
    ADD COLUMN IF NOT EXISTS "englishTab.content" JSONB,
    ADD COLUMN IF NOT EXISTS "englishTab.keywords" JSONB,
    ADD COLUMN IF NOT EXISTS "englishTab.keyInsights" JSONB;
  `);

  // Step 2: Copy data from old columns to new columns
  await payload.db.drizzle.execute(`
    UPDATE articles SET 
      "englishTab.title" = "englishTab.enhancedTitle",
      "englishTab.summary" = "englishTab.enhancedSummary", 
      "englishTab.content" = "englishTab.enhancedContent",
      "englishTab.keywords" = "englishTab.enhancedKeywords",
      "englishTab.keyInsights" = "englishTab.enhancedKeyInsights"
    WHERE 
      "englishTab.enhancedTitle" IS NOT NULL OR
      "englishTab.enhancedSummary" IS NOT NULL OR
      "englishTab.enhancedContent" IS NOT NULL;
  `);

  // Step 3: Verify data integrity
  const verificationResult = await payload.db.drizzle.execute(`
    SELECT 
      COUNT(*) as total_articles,
      COUNT("englishTab.title") as migrated_titles,
      COUNT("englishTab.summary") as migrated_summaries,
      COUNT("englishTab.content") as migrated_content
    FROM articles;
  `);

  console.log('📊 Migration Verification:', verificationResult);

  // Step 4: Keep old columns for now (rollback safety)
  // Will drop in separate migration after testing
}

export async function down({ payload }: MigrateDownArgs): Promise<void> {
  // Rollback: Copy data back to original columns
  await payload.db.drizzle.execute(`
    UPDATE articles SET 
      "englishTab.enhancedTitle" = "englishTab.title",
      "englishTab.enhancedSummary" = "englishTab.summary",
      "englishTab.enhancedContent" = "englishTab.content", 
      "englishTab.enhancedKeywords" = "englishTab.keywords",
      "englishTab.enhancedKeyInsights" = "englishTab.keyInsights"
    WHERE 
      "englishTab.title" IS NOT NULL OR
      "englishTab.summary" IS NOT NULL OR
      "englishTab.content" IS NOT NULL;
  `);

  // Drop new columns
  await payload.db.drizzle.execute(`
    ALTER TABLE articles 
    DROP COLUMN IF EXISTS "englishTab.title",
    DROP COLUMN IF EXISTS "englishTab.summary", 
    DROP COLUMN IF EXISTS "englishTab.content",
    DROP COLUMN IF EXISTS "englishTab.keywords",
    DROP COLUMN IF EXISTS "englishTab.keyInsights";
  `);
}
```

### **Task 6.3: Backend Code Updates (120 minutes)**

#### **6.3.1: Validation Service Updates**

**File**: `src/lib/services/article-validation.ts`

**CRITICAL CHANGES:**

```typescript
// BEFORE
export interface ArticleValidationContext {
  fields: {
    title: string;
    enhancedTitle: string; // ❌ OLD
    enhancedSummary: string; // ❌ OLD
    enhancedContent: any; // ❌ OLD
    germanTitle: string;
    germanContent: any;
  };
}

// AFTER
export interface ArticleValidationContext {
  fields: {
    title: string;
    englishTitle: string; // ✅ NEW
    englishSummary: string; // ✅ NEW
    englishContent: any; // ✅ NEW
    germanTitle: string;
    germanContent: any;
  };
}

// Update all validation functions
export function validateEnhancementRequirements(
  context: ArticleValidationContext
): ValidationResult {
  const { fields } = context;
  const errors: string[] = [];

  // BEFORE
  // if (!fields.enhancedTitle || fields.enhancedTitle.length < 20) {
  //   errors.push('Enhanced title must be at least 20 characters');
  // }

  // AFTER
  if (!fields.englishTitle || fields.englishTitle.length < 20) {
    errors.push('English title must be at least 20 characters');
  }

  if (!fields.englishSummary || fields.englishSummary.length < 20) {
    errors.push('English summary must be at least 20 characters');
  }

  if (isLexicalContentEmpty(fields.englishContent)) {
    errors.push('English content is required for enhancement');
  }

  return {
    isValid: errors.length === 0,
    errors,
    firstError: errors[0] || null,
  };
}
```

#### **6.3.2: API Endpoints Updates**

**Files**: `src/app/api/articles/enhance/route.ts`, `src/app/api/articles/translate/route.ts`

**Enhancement API:**

```typescript
// BEFORE
const { enhancedTitle, enhancedSummary, enhancedContent } =
  article.englishTab || {};

// AFTER
const {
  title: englishTitle,
  summary: englishSummary,
  content: englishContent,
} = article.englishTab || {};

// Update response handling
const updateData = {
  'englishTab.title': enhancedResult.title, // ✅ NEW PATH
  'englishTab.summary': enhancedResult.summary, // ✅ NEW PATH
  'englishTab.content': enhancedResult.content, // ✅ NEW PATH
  'englishTab.keywords': enhancedResult.keywords, // ✅ NEW PATH
  'englishTab.keyInsights': enhancedResult.keyInsights, // ✅ NEW PATH
  hasBeenEnhanced: true,
  workflowStage: 'enhanced',
};
```

#### **6.3.3: Content Processing Updates**

**Files**: `src/lib/server/enhanced-content-processor.ts`, `src/lib/integrations/openai/*`

**OpenAI Integration:**

```typescript
// BEFORE
interface EnhancementRequest {
  originalTitle: string;
  enhancedTitle?: string; // ❌ OLD
  enhancedSummary?: string; // ❌ OLD
  enhancedContent?: any; // ❌ OLD
}

// AFTER
interface EnhancementRequest {
  originalTitle: string;
  englishTitle?: string; // ✅ NEW
  englishSummary?: string; // ✅ NEW
  englishContent?: any; // ✅ NEW
}
```

### **Task 6.4: Frontend Component Updates (90 minutes)**

#### **6.4.1: DocumentControls Component**

**File**: `src/components/admin/article-actions/DocumentControls.tsx`

**CRITICAL FIELD REFERENCE UPDATES:**

```typescript
// BEFORE - Old field paths
const enhancedTitle = useFormFields(
  ([fields]) => fields['englishTab.enhancedTitle']?.value as string
);
const enhancedSummary = useFormFields(
  ([fields]) => fields['englishTab.enhancedSummary']?.value as string
);
const enhancedContent = useFormFields(
  ([fields]) => fields['englishTab.enhancedContent']?.value
);

// AFTER - New field paths
const englishTitle = useFormFields(
  ([fields]) => fields['englishTab.title']?.value as string
);
const englishSummary = useFormFields(
  ([fields]) => fields['englishTab.summary']?.value as string
);
const englishContent = useFormFields(
  ([fields]) => fields['englishTab.content']?.value
);

// Update article context
const articleContext = useMemo(
  () => ({
    articleType,
    workflowStage,
    hasBeenEnhanced,
    hasGermanTranslation: !!germanTitle || !!germanContent,
    hasOriginalSource,
    fields: {
      title,
      englishTitle, // ✅ NEW
      englishSummary, // ✅ NEW
      englishContent, // ✅ NEW
      germanTitle,
      germanContent,
    },
  }),
  [
    articleType,
    workflowStage,
    hasBeenEnhanced,
    title,
    englishTitle,
    englishSummary,
    englishContent,
    germanTitle,
    germanContent,
    hasOriginalSource,
  ]
);
```

#### **6.4.2: Display Components**

**Files**: All components that display article content

```typescript
// Update all display components to use new field paths
// Example: Article preview components, content renderers, etc.
```

### **Task 6.5: AI Prompts & Processing Updates (75 minutes)**

#### **6.5.1: OpenAI Prompts**

**File**: `src/lib/integrations/openai/prompts-unified.ts`

**CRITICAL PROMPT UPDATES:**

```typescript
// BEFORE - References to "enhanced" fields
const ENHANCEMENT_PROMPT = `
Enhance the following article content:
- Enhanced Title: {enhancedTitle}
- Enhanced Summary: {enhancedSummary}  
- Enhanced Content: {enhancedContent}
`;

// AFTER - References to generic field names
const ENHANCEMENT_PROMPT = `
Enhance the following article content:
- English Title: {englishTitle}
- English Summary: {englishSummary}
- English Content: {englishContent}
`;

// Update all prompt templates
export const UNIFIED_ENHANCEMENT_PROMPT = `
You are enhancing financial article content for international markets.

Input Content:
- Original Title: {originalTitle}
- Current English Title: {englishTitle}           // ✅ UPDATED
- Current English Summary: {englishSummary}       // ✅ UPDATED  
- Current English Content: {englishContent}       // ✅ UPDATED

Output Requirements:
- Enhanced English Title (20+ characters)
- Enhanced English Summary (100-150 characters)
- Enhanced English Content (600-750 words)
- Keywords (5-8 relevant terms)
- Key Insights (3-5 important takeaways)
`;
```

#### **6.5.2: Response Processing**

**File**: `src/lib/integrations/openai/unified-enhancement.ts`

```typescript
// Update response parsing to handle new field structure
export interface EnhancementResponse {
  englishTitle: string; // ✅ NEW
  englishSummary: string; // ✅ NEW
  englishContent: any; // ✅ NEW
  keywords: string[]; // ✅ UPDATED
  keyInsights: string[]; // ✅ UPDATED
}

// Update parsing logic
export function parseEnhancementResponse(
  response: string
): EnhancementResponse {
  // Parse OpenAI response and map to new field structure
  return {
    englishTitle: extractTitle(response),
    englishSummary: extractSummary(response),
    englishContent: extractContent(response),
    keywords: extractKeywords(response),
    keyInsights: extractKeyInsights(response),
  };
}
```

### **Task 6.6: Comprehensive Test Updates (120 minutes)**

#### **6.6.1: Unit Test Updates**

**Files**: All test files referencing old field names

**Validation Service Tests:**

```typescript
// BEFORE
describe('article-validation', () => {
  const mockContext = {
    articleType: 'curated',
    workflowStage: 'draft',
    fields: {
      title: 'Test Title',
      enhancedTitle: 'Enhanced Test Title', // ❌ OLD
      enhancedSummary: 'Enhanced summary...', // ❌ OLD
      enhancedContent: mockLexicalContent, // ❌ OLD
    },
  };
});

// AFTER
describe('article-validation', () => {
  const mockContext = {
    articleType: 'curated',
    workflowStage: 'draft',
    fields: {
      title: 'Test Title',
      englishTitle: 'English Test Title', // ✅ NEW
      englishSummary: 'English summary...', // ✅ NEW
      englishContent: mockLexicalContent, // ✅ NEW
    },
  };
});
```

#### **6.6.2: Integration Test Updates**

**Files**: Component tests, API tests, E2E tests

```typescript
// Update all test fixtures and mock data
const MOCK_ARTICLE_DATA = {
  title: 'Test Article',
  englishTab: {
    title: 'English Test Title', // ✅ NEW
    summary: 'English test summary...', // ✅ NEW
    content: mockLexicalContent, // ✅ NEW
    keywords: ['test', 'article'], // ✅ NEW
    keyInsights: ['Key insight 1'], // ✅ NEW
  },
};
```

#### **6.6.3: Test Fixtures Update**

**Files**: All test fixture files

```typescript
// Update HTML conversion test fixtures
export const COMPLEX_FINANCIAL_DATA_EXPECTED = {
  englishTab: {
    title: 'Complex Financial Article', // ✅ UPDATED
    summary: 'Financial summary...', // ✅ UPDATED
    content: complexLexicalContent, // ✅ UPDATED
  },
};
```

### **Task 6.7: Pipeline & Configuration Updates (45 minutes)**

#### **6.7.1: Content Processing Pipeline**

**Files**: All pipeline scripts and configurations

```typescript
// Update pipeline validation checks
const PIPELINE_VALIDATION_RULES = {
  requiredFields: [
    'englishTab.title', // ✅ UPDATED
    'englishTab.summary', // ✅ UPDATED
    'englishTab.content', // ✅ UPDATED
  ],
  fieldValidation: {
    'englishTab.title': { minLength: 20 }, // ✅ UPDATED
    'englishTab.summary': { minLength: 20 }, // ✅ UPDATED
    'englishTab.content': { required: true }, // ✅ UPDATED
  },
};
```

#### **6.7.2: Monitoring & Logging Updates**

```typescript
// Update all logging references
console.log('📊 Content Processing Stats:', {
  englishTitle: article.englishTab?.title?.length || 0, // ✅ UPDATED
  englishSummary: article.englishTab?.summary?.length || 0, // ✅ UPDATED
  englishContent: !!article.englishTab?.content, // ✅ UPDATED
});
```

### **Task 6.8: Documentation Updates (30 minutes)**

#### **6.8.1: API Documentation**

Update all API documentation to reflect new field names

#### **6.8.2: README Updates**

Update project README with new field structure

#### **6.8.3: Type Documentation**

Update TypeScript interface documentation

## 🧪 **COMPREHENSIVE TESTING STRATEGY**

### **Phase 1: Pre-Migration Testing**

- [ ] Full test suite runs with current field names
- [ ] All validation logic works correctly
- [ ] All API endpoints functional
- [ ] All UI components render properly

### **Phase 2: Migration Testing**

- [ ] Database migration runs without errors
- [ ] All data migrated correctly (zero data loss)
- [ ] Both old and new field names accessible (transition period)
- [ ] Rollback procedure works correctly

### **Phase 3: Post-Migration Testing**

- [ ] All updated tests pass
- [ ] All API endpoints work with new field names
- [ ] All UI components work with new field names
- [ ] AI processing works with new prompts
- [ ] Content enhancement/translation functional

### **Phase 4: Integration Testing**

- [ ] Full article creation flow
- [ ] Enhancement process end-to-end
- [ ] Translation process end-to-end
- [ ] Publication workflow
- [ ] Admin UI functionality

## 📊 **SUCCESS CRITERIA**

1. **✅ Zero Data Loss**: All existing article content preserved
2. **✅ Functionality Maintained**: All features work identically
3. **✅ Tests Updated**: 100% test coverage with new field names
4. **✅ AI Processing**: All prompts and processing updated
5. **✅ Clean Codebase**: No references to old "enhanced" field names
6. **✅ Rollback Capability**: Can revert changes if needed

## 🚨 **ROLLBACK PLAN**

### **Emergency Rollback Procedure**

1. **Database**: Run down migration to restore old column names
2. **Code**: Git revert to restore old field references
3. **Cache**: Clear all application caches
4. **Verification**: Run full test suite to confirm rollback

```bash
# Emergency rollback commands
npm run payload migrate:down
git revert HEAD~[number-of-commits]
npm run payload build
npm run test
```

## ⚠️ **CRITICAL CONSIDERATIONS**

### **Breaking Changes**

- **API Responses**: Will contain new field names
- **Database Schema**: Column names will change
- **Cache Keys**: May need invalidation
- **External Integrations**: Any external systems using field names

### **Backwards Compatibility**

- Keep old columns during transition period
- Consider field aliases for API backwards compatibility
- Update API versioning if needed

### **Performance Impact**

- Migration may take time for large datasets
- Consider running during low-traffic period
- Monitor database performance during migration

## 🔄 **NEXT SPRINT**

**Sprint 7**: Final Testing & Production Readiness - Comprehensive testing of all features with new field structure.

## 📝 **NOTES**

- **High Risk Operation**: This touches every part of the system
- **Thorough Testing Required**: Cannot afford data loss or functionality breaks
- **Coordination Needed**: All team members must be aware of changes
- **Documentation Critical**: Every change must be documented
- **Rollback Plan Essential**: Must be able to revert quickly if issues arise

**🎯 SPRINT 6 SCOPE**: Complete field renaming with zero functionality loss and comprehensive testing coverage.
