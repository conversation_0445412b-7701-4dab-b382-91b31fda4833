# Sprint 4: User Experience & State Management

**Duration**: 3-4 hours  
**Priority**: Medium  
**Dependencies**: Sprint 1, 2, 3 (Field Access, Validation, API Standardization)

## 🎯 Sprint Goals

1. **Replace temporary toast** with PayloadCMS native notifications
2. **Add comprehensive loading states** and user feedback
3. **Implement mutual exclusion** (prevent simultaneous operations)
4. **Extract business logic** from UI component
5. **Add auto-refresh functionality** for content updates
6. **Improve button spacing and visual hierarchy** for better UX

## 📋 Tasks

### Task 4.1: Implement PayloadCMS Native Notifications (45 minutes)

**File**: `src/components/admin/notifications/ArticleNotifications.tsx` (new file)

**Problem**: Current toast implementation is temporary and doesn't integrate with PayloadCMS.

**Solution**: Use PayloadCMS native notification system:

```typescript
'use client';

import { useToast } from '@payloadcms/ui';

export interface NotificationOptions {
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  description?: string;
  duration?: number;
}

export const useArticleNotifications = () => {
  const { toast } = useToast();

  const showNotification = useCallback(
    (options: NotificationOptions) => {
      const { type, message, description, duration = 5000 } = options;

      toast[type](message, {
        description,
        duration,
      });
    },
    [toast]
  );

  const showEnhancementSuccess = useCallback(() => {
    showNotification({
      type: 'success',
      message: 'Content Enhanced Successfully!',
      description:
        'Your article content has been enhanced and is ready for review.',
      duration: 4000,
    });
  }, [showNotification]);

  const showTranslationSuccess = useCallback(() => {
    showNotification({
      type: 'success',
      message: 'Translation Completed!',
      description:
        'Your article has been translated to German and is ready for review.',
      duration: 4000,
    });
  }, [showNotification]);

  const showOperationError = useCallback(
    (operation: 'enhancement' | 'translation', error?: string) => {
      showNotification({
        type: 'error',
        message: `${operation === 'enhancement' ? 'Enhancement' : 'Translation'} Failed`,
        description:
          error ||
          `Failed to ${operation === 'enhancement' ? 'enhance' : 'translate'} article. Please try again.`,
        duration: 6000,
      });
    },
    [showNotification]
  );

  return {
    showNotification,
    showEnhancementSuccess,
    showTranslationSuccess,
    showOperationError,
  };
};
```

### Task 4.2: Add Comprehensive Loading States (60 minutes)

**File**: `src/components/admin/article-actions/DocumentControls.tsx`

**Problem**: Users need better feedback during operations.

**Solution**: Comprehensive loading states and user feedback:

```typescript
// Enhanced state management
const [isEnhancing, setIsEnhancing] = useState(false);
const [isTranslating, setIsTranslating] = useState(false);
const [enhancementJustCompleted, setEnhancementJustCompleted] = useState(false);
const [translationJustCompleted, setTranslationJustCompleted] = useState(false);
const [operationProgress, setOperationProgress] = useState<{
  operation: 'enhance' | 'translate' | null;
  stage: string;
  progress: number;
}>({ operation: null, stage: '', progress: 0 });

// Progress tracking for operations
const updateOperationProgress = useCallback((operation: 'enhance' | 'translate', stage: string, progress: number) => {
  setOperationProgress({ operation, stage, progress });
}, []);

// Enhanced button rendering with loading states
const renderEnhanceButton = () => {
  if (!buttonVisibility.showEnhanceButton) return null;

  const isDisabled = !canEnhanceContent || isEnhancing || isTranslating;
  const buttonText = isEnhancing
    ? `Enhancing... (${operationProgress.stage})`
    : enhanceValidation.buttonText || 'Enhance Content';

  return (
    <button
      onClick={handleEnhance}
      disabled={isDisabled}
      style={{
        backgroundColor: isEnhancing ? '#6B7280' : enhancementJustCompleted ? '#10B981' : '#3B82F6',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        padding: '12px 20px',
        cursor: isDisabled ? 'not-allowed' : 'pointer',
        position: 'relative',
        minWidth: '160px',
        transition: 'all 0.2s ease',
      }}
    >
      {isEnhancing && (
        <div style={{
          position: 'absolute',
          left: '8px',
          top: '50%',
          transform: 'translateY(-50%)',
          width: '16px',
          height: '16px',
          border: '2px solid transparent',
          borderTop: '2px solid white',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite',
        }} />
      )}
      <span style={{ marginLeft: isEnhancing ? '24px' : '0' }}>
        {buttonText}
      </span>
      {enhancementJustCompleted && (
        <span style={{ marginLeft: '8px' }}>✓</span>
      )}
    </button>
  );
};

// Similar for translate button...
```

### Task 4.3: Implement Mutual Exclusion (30 minutes)

**Problem**: Users can accidentally trigger multiple operations simultaneously.

**Solution**: Prevent simultaneous operations:

```typescript
// Mutual exclusion logic
const isAnyOperationRunning = isEnhancing || isTranslating;

// Update handlers to check mutual exclusion
const handleEnhance = useCallback(async () => {
  if (!canEnhanceContent || isAnyOperationRunning) return;

  setIsEnhancing(true);
  updateOperationProgress('enhance', 'Preparing...', 10);

  try {
    updateOperationProgress('enhance', 'Sending to AI...', 30);

    const response = await fetch('/api/articles/enhance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ articleId: id }),
    });

    updateOperationProgress('enhance', 'Processing response...', 70);
    const result = await response.json();

    if (result.success) {
      updateOperationProgress('enhance', 'Updating content...', 90);
      updateFormAfterEnhancement(result);

      showEnhancementSuccess();
      setEnhancementJustCompleted(true);
      setTimeout(() => setEnhancementJustCompleted(false), 3000);
    } else {
      showOperationError('enhancement', result.error);
    }
  } catch (error) {
    showOperationError('enhancement');
  } finally {
    setIsEnhancing(false);
    setOperationProgress({ operation: null, stage: '', progress: 0 });
  }
}, [
  canEnhanceContent,
  isAnyOperationRunning,
  id,
  updateFormAfterEnhancement,
  showEnhancementSuccess,
  showOperationError,
]);
```

### Task 4.4: Extract Business Logic (75 minutes)

**File**: `src/lib/services/article-operations.ts` (new file)

**Problem**: DocumentControls component contains too much business logic.

**Solution**: Extract operations to service layer:

```typescript
export interface OperationResult {
  success: boolean;
  data?: any;
  error?: string;
  metrics?: {
    processingTime: number;
    [key: string]: any;
  };
}

export interface OperationProgress {
  stage: string;
  progress: number;
  message?: string;
}

export class ArticleOperationsService {
  private progressCallback?: (progress: OperationProgress) => void;

  setProgressCallback(callback: (progress: OperationProgress) => void) {
    this.progressCallback = callback;
  }

  private updateProgress(stage: string, progress: number, message?: string) {
    if (this.progressCallback) {
      this.progressCallback({ stage, progress, message });
    }
  }

  async enhanceArticle(articleId: string): Promise<OperationResult> {
    try {
      this.updateProgress('Preparing enhancement...', 10);

      const response = await fetch('/api/articles/enhance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ articleId }),
      });

      this.updateProgress('Processing AI response...', 70);
      const result = await response.json();

      this.updateProgress('Finalizing...', 90);

      if (!response.ok) {
        throw new Error(result.error || 'Enhancement failed');
      }

      this.updateProgress('Complete!', 100);
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Enhancement failed',
      };
    }
  }

  async translateArticle(articleId: string): Promise<OperationResult> {
    try {
      this.updateProgress('Preparing translation...', 10);

      const response = await fetch('/api/articles/translate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ articleId }),
      });

      this.updateProgress('Processing translation...', 70);
      const result = await response.json();

      this.updateProgress('Finalizing...', 90);

      if (!response.ok) {
        throw new Error(result.error || 'Translation failed');
      }

      this.updateProgress('Complete!', 100);
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Translation failed',
      };
    }
  }
}

export const articleOperations = new ArticleOperationsService();
```

### Task 4.5: Browser Tools UX Testing (45 minutes)

**Testing Process**:

1. Monitor form state updates in real-time using browser console
2. Test loading states and user feedback with browser tools
3. Debug API response handling and form refresh issues
4. Test mutual exclusion and progress tracking

**Console Debugging**:

```typescript
// Add UX debugging
console.log('🔍 UX State Debug:', {
  isEnhancing,
  isTranslating,
  isAnyOperationRunning,
  operationProgress,
  enhancementJustCompleted,
  translationJustCompleted,
});
```

### ✅ Task 4.6: Improve Button Spacing and Layout (COMPLETED - 20 minutes)

**File**: `src/components/admin/article-actions/DocumentControls.tsx`

**Problem**: Enhancement and translation buttons are touching each other with no visual separation, creating poor visual hierarchy.

**✅ Solution Implemented**: Added proper spacing and improved button layout with user-requested refinements:

```typescript
// ✅ FINAL IMPLEMENTATION: Updated main container styling
return (
  <div style={{
    display: 'flex',
    flexDirection: 'column',
    gap: '12px', // Consistent spacing between buttons
    marginBottom: '20px', // External spacing below container
    padding: '16px', // Consistent internal padding
    paddingBottom: '16px', // Explicit bottom internal padding
    backgroundColor: '#f8fafc',
    borderRadius: '8px',
    border: '1px solid #e2e8f0',
    maxWidth: '300px'
  }}>
    {/* Enhancement Button */}
    {buttonVisibility.showEnhanceButton && id && (
      <button
        onClick={handleEnhance}
        disabled={!canEnhanceContent || isEnhancing || isTranslating}
        style={{
          backgroundColor: isEnhancing ? '#6B7280' : enhancementJustCompleted ? '#10B981' : '#3B82F6',
          color: 'white',
          border: 'none',
          borderRadius: '6px',
          padding: '12px 20px',
          cursor: canEnhanceContent ? 'pointer' : 'not-allowed',
          fontSize: '14px',
          fontWeight: '500',
          minWidth: '160px',
          transition: 'all 0.2s ease',
          boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
          // Remove any margin that might cause touching
          margin: '0',
        }}
      >
        {/* Button content */}
      </button>
    )}

    {/* Translation Button */}
    {id && (
      <button
        onClick={handleTranslate}
        disabled={!canTranslate || isTranslating || isEnhancing}
        style={{
          backgroundColor: isTranslating ? '#6B7280' : translationJustCompleted ? '#10B981' : '#059669',
          color: 'white',
          border: 'none',
          borderRadius: '6px',
          padding: '12px 20px',
          cursor: canTranslate ? 'pointer' : 'not-allowed',
          fontSize: '14px',
          fontWeight: '500',
          minWidth: '160px',
          transition: 'all 0.2s ease',
          boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
          // Remove any margin that might cause touching
          margin: '0',
        }}
      >
        {/* Button content */}
      </button>
    )}
  </div>
);
```

**✅ Final Visual Improvements Achieved**:

- ✅ **12px gap** between buttons using flexbox (consistent with PayloadCMS spacing)
- ✅ **20px external margin** below container (`marginBottom`) for proper separation
- ✅ **16px consistent internal padding** on all sides for content spacing
- ✅ **Container styling** with subtle background (#f8fafc) and border (#e2e8f0)
- ✅ **300px max-width** for better proportions
- ✅ **Consistent button sizing** with `minWidth: 160px`
- ✅ **Box shadows** for better depth perception
- ✅ **Proper margin/padding balance** - no more touching buttons!

**✅ User-Requested Refinements Applied**:

- ✅ Changed from `marginTop` to `marginBottom: '20px'`
- ✅ Added explicit `paddingBottom: '16px'` for internal bottom spacing
- ✅ Maintained consistent 16px padding on all other sides

### Task 4.7: Unit Tests (60 minutes)

**Files**:

- `src/lib/services/__tests__/article-operations.test.ts`
- `src/components/admin/notifications/__tests__/ArticleNotifications.test.tsx`

Test scenarios:

- [ ] Operation service API communication
- [ ] Progress tracking and callbacks
- [ ] Mutual exclusion logic
- [ ] Notification system integration
- [ ] Loading state management
- [ ] Button spacing and layout rendering

## 🧪 Testing Checklist

- [ ] PayloadCMS notifications work correctly
- [ ] Loading states provide good user feedback
- [ ] Mutual exclusion prevents simultaneous operations
- [ ] Progress tracking shows operation stages
- [ ] Auto-completion feedback works
- [ ] Error handling provides helpful messages

## 📊 Success Criteria

1. **Native Notifications**: PayloadCMS toast system integration working
2. **Loading States**: Clear progress feedback during operations
3. **Mutual Exclusion**: Cannot run enhance and translate simultaneously
4. **Business Logic Separation**: Operations extracted from UI component
5. **User Experience**: Smooth, responsive interface with good feedback

## 🔄 Next Sprint

**Sprint 5**: Sources Tab Conditional Logic - Implement conditional tab visibility and schema updates.

## 📝 Notes

- Focus on user experience and feedback quality
- Test all loading states and progress indicators
- Ensure notifications are helpful and not annoying
- Use browser tools to monitor state changes and user interactions
