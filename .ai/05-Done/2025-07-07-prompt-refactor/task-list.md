# Prompt Refactoring Project: Task List
**Project**: Consolidate Börsen Blick Content Processing Prompts
**Goal**: Reduce 7 prompts to 1 unified prompt, achieve 70% faster processing and 65% cost reduction
**Timeline**: 2-3 weeks
**Impact**: Critical system optimisation

## 📋 Project Overview

**Current State**: 7 separate prompts requiring 3-5 API calls per article ($0.12-0.15 cost, 15-20s processing)
**Target State**: 1 unified prompt requiring 1 API call per article ($0.03-0.04 cost, 6-10s processing)

**Success Metrics**:
- ✅ 70% processing time reduction
- ✅ 65% cost reduction
- ✅ 95%+ content quality maintenance
- ✅ 80% maintenance overhead reduction

---

## 🚀 Phase 1: Analysis & Design (Week 1)

### Task 1.1: Comprehensive Prompt Analysis
  **Priority**: High | **Estimate**: 2 days | **Dependencies**: None

  **Subtasks**:
  - [ ] Document all 7 existing prompts with their specific purposes
  - [ ] Identify overlapping instructions and redundant content
  - [ ] Map character cleaning requirements (repeated 4 times across prompts)
  - [ ] Analyse style guideline duplications (repeated 3 times)
  - [ ] Document market context redundancies
  - [ ] Create prompt consolidation matrix showing merge opportunities

  **Acceptance Criteria**:
  - Complete documentation of prompt overlap analysis
  - Consolidation matrix identifying 70%+ redundant content
  - Clear mapping of which instructions can be unified

  **Files to Modify**: None (analysis only)

### Task 1.2: Schema Architecture Design
  **Priority**: High | **Estimate**: 1 day | **Dependencies**: Task 1.1

  **Subtasks**:
  - [ ] Review existing `DualLanguageEnhancementSchema` in `src/lib/integrations/openai/schemas.ts`
  - [ ] Design enhanced schema to include metadata generation
  - [ ] Design schema to include quality assessment metrics
  - [ ] Design schema to include SEO optimisation fields
  - [ ] Plan backward compatibility approach
  - [ ] Document schema migration path

  **Acceptance Criteria**:
  - Enhanced schema design that covers all 7 current prompt outputs
  - Backward compatibility plan for existing API consumers
  - Clear migration path from current schemas

  **Files to Review**:
  - `src/lib/integrations/openai/schemas.ts` (lines 130-215)
  - `src/lib/integrations/openai/responses-api-client.ts`

### Task 1.3: Current System Performance Baseline
  **Priority**: Medium | **Estimate**: 1 day | **Dependencies**: None

  **Subtasks**:
  - [ ] Measure current processing times for sample articles
  - [ ] Document current API call patterns and costs
  - [ ] Analyse current error rates and fallback usage
  - [ ] Establish baseline quality metrics
  - [ ] Document current token usage patterns
  - [ ] Create performance measurement framework

  **Acceptance Criteria**:
  - Baseline metrics established for all success criteria
  - Performance measurement framework ready for before/after comparison
  - Error rate analysis completed

  **Files to Review**:
  - `src/app/api/articles/enhance/route.ts`
  - `src/lib/integrations/openai/responses-api-client.ts`

---

## 🔨 Phase 2: Implementation (Week 2)

### Task 2.1: Create Master Unified Prompt
**Priority**: High | **Estimate**: 3 days | **Dependencies**: Task 1.1, 1.2

**Subtasks**:
- [ ] Consolidate enhancement guidelines from `ENHANCE_GERMAN_CONTENT_PROMPT`
- [ ] Integrate translation requirements from `TRANSLATE_TO_ENGLISH_PROMPT`
- [ ] Merge character cleaning rules (remove 4x duplication)
- [ ] Integrate metadata generation instructions
- [ ] Combine market relevance scoring criteria
- [ ] Add content quality assessment requirements
- [ ] Ensure Lexical-compatible HTML output specifications
- [ ] Add comprehensive error handling instructions

**Acceptance Criteria**:
- Single comprehensive prompt covering all 7 current prompt functions
- 50%+ reduction in total prompt length through deduplication
- Clear structured output requirements matching enhanced schema
- All Börsen Blick style guidelines preserved

**Files to Create**:
- `src/lib/integrations/prompts-unified.ts` (new unified prompt)

### Task 2.2: Enhanced Schema Implementation
**Priority**: High | **Estimate**: 2 days | **Dependencies**: Task 1.2, 2.1

**Subtasks**:
- [ ] Extend `DualLanguageEnhancementSchema` with metadata fields
- [ ] Add quality assessment fields to schema
- [ ] Add SEO optimisation metrics to schema
- [ ] Implement content validation rules in schema
- [ ] Add performance tracking fields
- [ ] Create comprehensive TypeScript types for new schema
- [ ] Add Zod validation rules for all new fields

**Acceptance Criteria**:
- Enhanced schema validates all unified prompt outputs
- TypeScript types provide full IDE support
- Schema includes all fields from current 7-prompt system
- Validation prevents malformed outputs

**Files to Modify**:
- `src/lib/integrations/openai/schemas.ts` (extend existing schema)

### Task 2.3: Unified API Function Development
**Priority**: High | **Estimate**: 2 days | **Dependencies**: Task 2.1, 2.2

**Subtasks**:
- [ ] Create new `unifiedContentEnhancement` function
- [ ] Implement unified prompt with enhanced schema
- [ ] Add comprehensive error handling and fallback logic
- [ ] Implement performance metrics tracking
- [ ] Add input validation and sanitisation
- [ ] Create backward compatibility wrapper
- [ ] Implement timeout and retry logic

**Acceptance Criteria**:
- Function successfully processes German content to enhanced German + English
- Single API call replaces 3-5 current calls
- Error handling covers all failure scenarios
- Performance metrics tracking implemented

**Files to Create**:
- `src/lib/integrations/openai/unified-enhancement.ts` (new unified function)

**Files to Modify**:
- `src/lib/integrations/openai/responses-api-client.ts` (add new function)

---

## 🔧 Phase 3: Testing & Integration (Week 3)

### Task 3.1: API Endpoint Integration
  **Priority**: High | **Estimate**: 1 day | **Dependencies**: Task 2.3

  **Subtasks**:
  - [ ] Modify `/api/articles/enhance/route.ts` to use unified function
  - [ ] Update error handling for single-call approach
  - [ ] Add performance monitoring and logging
  - [ ] Update response format to match existing API
  - [ ] Add comprehensive request validation
  - [ ] Test with sample articles in development

  **Acceptance Criteria**:
  - API endpoint uses unified enhancement function
  - Response format maintains compatibility with PayloadCMS fields
  - Error handling covers all edge cases
  - Performance improvements are measurable

  **Files to Modify**:
  - `src/app/api/articles/enhance/route.ts` (major refactor)

### Task 3.2: Comprehensive System Testing
  **Priority**: High | **Estimate**: 2 days | **Dependencies**: Task 3.1

  **Subtasks**:
  - [ ] Create test dataset with 20+ diverse articles
  - [ ] Test unified prompt with varied content types
  - [ ] Validate output quality consistency
  - [ ] Performance benchmark against old system
  - [ ] Test error scenarios and edge cases
  - [ ] Verify Lexical format conversion accuracy
  - [ ] Test with actual RSS feed content

  **Acceptance Criteria**:
  - All test cases pass with 95%+ accuracy
  - Performance improvements meet targets (70% faster)
  - Cost reduction verified (65% less expensive)
  - Quality metrics maintained or improved

  **Files to Modify**:
  - `src/lib/utils/__tests__/` (new test files)
  - `scripts/test-unified-prompt.js` (new testing script)

### Task 3.3: PayloadCMS Integration Testing
  **Priority**: High | **Estimate**: 1 day | **Dependencies**: Task 3.2

  **Subtasks**:
  - [ ] Test article creation with unified enhancement
  - [ ] Verify rich text field handling
  - [ ] Test German/English content generation
  - [ ] Validate SEO metadata generation
  - [ ] Test admin interface integration
  - [ ] Verify database field mappings

  **Acceptance Criteria**:
  - Articles create successfully with unified prompt
  - Rich text fields render correctly in admin
  - Both languages generate properly
  - SEO metadata is accurate and well-formed

  **Files to Modify**:
  - `src/collections/Articles.ts` (validation updates)

### Task 3.4: Final System Deployment
  **Priority**: High | **Estimate**: 1 day | **Dependencies**: Task 3.3

  **Subtasks**:
  - [ ] Replace old prompt system with unified system
  - [ ] Remove deprecated prompts from `prompts.ts`
  - [ ] Clean up legacy code and functions
  - [ ] Update documentation to reflect new architecture
  - [ ] Verify all integration points work correctly
  - [ ] Final performance validation

  **Acceptance Criteria**:
  - Unified system fully operational
  - Legacy prompts removed without affecting functionality
  - Documentation updated to reflect new system
  - System architecture simplified by 70%

  **Files to Modify**:
  - `src/lib/integrations/prompts.ts` (major cleanup)
  - `src/lib/integrations/openai/migration-wrapper.ts` (removal)
  - `src/lib/integrations/openai/client.ts` (legacy function removal)

---

## 🎯 Success Metrics & Validation

### Performance Targets
- **Processing Time**: 15-20s → 6-10s (70% improvement)
- **Cost Per Article**: $0.12-0.15 → $0.03-0.04 (65% reduction)
- **API Calls**: 3-5 → 1 (80% reduction)
- **Code Maintenance**: 516 lines → ~100 lines (80% reduction)

### Quality Assurance
- **Content Quality**: Maintain 95%+ accuracy
- **SEO Metadata**: Consistent generation
- **Lexical Format**: 100% compatibility
- **Error Rate**: <1% processing failures

### Operational Metrics
- **System Stability**: 99.9% uptime
- **Error Handling**: Comprehensive coverage
- **Monitoring**: Real-time performance tracking
- **Documentation**: Complete technical guides

---

## 📋 Implementation Checklist

### Pre-Implementation
  - [ ] Development environment prepared
  - [ ] Testing framework established
  - [ ] Backup of current system created

### During Implementation
  - [ ] Regular progress reviews
  - [ ] Quality checkpoints at each phase
  - [ ] Performance monitoring active

### Post-Implementation
  - [ ] Performance metrics validated
  - [ ] Cost savings confirmed
  - [ ] Quality standards maintained
  - [ ] Documentation completed

---

## 🚨 Risk Mitigation

### Technical Risks
- **Content Quality**: Extensive testing and validation
- **Performance**: Monitoring and benchmarking
- **Integration**: Comprehensive testing
- **Reliability**: Error handling and monitoring

### Business Risks
- **Cost**: Regular cost monitoring and optimisation
- **Timeline**: Phased approach with checkpoints
- **Quality**: Continuous quality assessment

### Mitigation Strategies
- Comprehensive testing at each phase
- Performance monitoring and alerting
- Backup system ready for rollback if needed
- Regular progress check-ins

---

## 📞 Project Communication

### Progress Check-ins
  - Daily standups during active development
  - Quality metrics review
  - Performance benchmarks
  - Risk assessment updates

### Key Stakeholders
  - Development team
  - Content quality team
  - System administrators

### Success Criteria
  - All technical objectives met
  - Performance targets achieved
  - Quality standards maintained
  - Documentation complete

---

**Next Steps**:
1. Begin Phase 1 tasks
2. Set up development environment
3. Create unified prompt prototype
4. Schedule regular progress reviews

**Expected Completion**: 2-3 weeks from start date
**Total Estimated Effort**: 12-15 developer days
