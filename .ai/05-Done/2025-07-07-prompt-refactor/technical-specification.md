# Technical Specification: Prompt Refactoring Implementation

**Document Version**: 1.0
**Date**: 2025-07-07
**Project**: Consolidate Börsen Blick Content Processing Prompts
**Scope**: Detailed technical implementation specification

---

## 🎯 Technical Objectives

### Primary Goal
  Replace 7 individual prompts with 1 unified prompt that maintains quality while achieving:
  - **70% processing time reduction** (15-20s → 6-10s)
  - **65% cost reduction** ($0.12-0.15 → $0.03-0.04)
  - **95%+ content quality maintenance**
  - **80% code maintenance reduction**

### Architecture Transformation
```
BEFORE: Sequential Multi-Prompt Architecture
German Content → Enhancement → Translation → Metadata → Quality → Output

AFTER: Unified Single-Prompt Architecture
German Content → Unified Enhancement → Output
```

---

## 📋 Implementation Specification

### 1. Unified Prompt Design

#### Core Structure
```typescript
export const UNIFIED_CONTENT_ENHANCEMENT_PROMPT = `
You are a senior financial content editor for Börsen Blick, a German financial platform covering global markets. Your expertise encompasses German content enhancement, English translation, Canadian market analysis, and SEO optimisation.

## PRIMARY TASKS
Execute all tasks in a single comprehensive process:

1. **German Content Enhancement**: Enhance the provided German financial content for Canadian market context while maintaining Börsen Blick's authoritative voice
2. **English Translation**: Translate the enhanced German content to English with identical structure and Canadian market focus
3. **Metadata Generation**: Create SEO-optimised metadata for both German and English versions
4. **Quality Assessment**: Evaluate content quality and provide improvement metrics
5. **Insights Extraction**: Generate key insights for Canadian investors in both languages

## UNIFIED STYLE GUIDELINES
[Consolidated guidelines from all 7 current prompts]

## CHARACTER CLEANING REQUIREMENTS
[Single unified character cleaning section]

## OUTPUT FORMAT
Provide structured JSON response using the specified schema with all required fields populated.
`;
```

#### Prompt Size Projection
- **Current Total**: 516 lines across 7 prompts
- **Projected Unified**: ~150 lines (70% reduction)
- **Consolidation Areas**:
  - Character cleaning: 4 sections → 1 section (75% reduction)
  - Style guidelines: 3 sections → 1 section (67% reduction)
  - Market context: 2 sections → 1 section (50% reduction)

### 2. Enhanced Schema Implementation

#### Schema Extension Plan
```typescript
// File: src/lib/integrations/openai/schemas.ts

export const UnifiedContentEnhancementSchema = z.object({
  enhancedGerman: z.object({
    title: z.string().min(10).max(200).describe('Enhanced German title with Canadian context'),
    content: z.string().min(100).describe('Enhanced German content in clean HTML format'),
    summary: z.string().min(50).max(450).describe('German summary (max 450 characters)'),
    insights: z.array(z.string()).min(3).max(10).describe('Key insights for Canadian investors'),
    metadata: z.object({
      seoTitle: z.string().min(30).max(60).describe('SEO-optimised German title'),
      description: z.string().min(120).max(160).describe('Meta description in German'),
      keywords: z.array(z.string()).min(5).max(12).describe('German SEO keywords')
    })
  }),

  enhancedEnglish: z.object({
    title: z.string().min(10).max(200).describe('English translation of enhanced German title'),
    content: z.string().min(100).describe('English translation in clean HTML format'),
    summary: z.string().min(50).max(450).describe('English summary (max 450 characters)'),
    insights: z.array(z.string()).min(3).max(10).describe('Key insights in English'),
    metadata: z.object({
      seoTitle: z.string().min(30).max(60).describe('SEO-optimised English title'),
      description: z.string().min(120).max(160).describe('Meta description in English'),
      keywords: z.array(z.string()).min(5).max(12).describe('English SEO keywords')
    })
  }),

  qualityMetrics: z.object({
    contentScore: z.number().min(0).max(100).describe('Overall content quality score'),
    translationQuality: z.enum(['excellent', 'good', 'fair']).describe('Translation quality rating'),
    structurePreservation: z.boolean().describe('Whether structure was preserved between languages'),
    canadianRelevance: z.number().min(0).max(100).describe('Canadian market relevance score'),
    seoOptimisation: z.number().min(0).max(100).describe('SEO optimisation score')
  }),

  consistency: z.object({
    structureMatches: z.boolean().describe('German/English structure consistency'),
    contentAlignment: z.enum(['perfect', 'good', 'fair']).describe('Content alignment quality'),
    translationAccuracy: z.enum(['excellent', 'good', 'fair']).describe('Translation accuracy')
  }),

  processingMetadata: z.object({
    modelUsed: z.string().describe('OpenAI model used for processing'),
    processingTime: z.number().describe('Processing time in milliseconds'),
    tokenUsage: z.object({
      prompt: z.number().describe('Prompt tokens'),
      completion: z.number().describe('Completion tokens'),
      total: z.number().describe('Total tokens')
    })
  })
});

export type UnifiedContentEnhancement = z.infer<typeof UnifiedContentEnhancementSchema>;
```

### 3. API Function Implementation

#### New Unified Function
```typescript
// File: src/lib/integrations/openai/unified-enhancement.ts

import { UnifiedContentEnhancementSchema, type UnifiedContentEnhancement } from './schemas';
import { UNIFIED_CONTENT_ENHANCEMENT_PROMPT } from './prompts-unified';

export async function unifiedContentEnhancement(
  title: string,
  content: string,
  options: {
    temperature?: number;
    enableFallback?: boolean;
    timeout?: number;
  } = {}
): Promise<{
  success: boolean;
  data?: UnifiedContentEnhancement;
  error?: string;
  fallbackUsed?: boolean;
  metrics: {
    processingTime: number;
    tokenUsage: number;
    apiCalls: number;
  };
}> {
  const startTime = Date.now();

  try {
    // Validate input
    if (!content || content.length < 100) {
      throw new Error('Content too short for enhancement');
    }

    // Prepare unified prompt
    const systemPrompt = UNIFIED_CONTENT_ENHANCEMENT_PROMPT;
    const userPrompt = `
Title: ${title}
Content: ${content}

Process this German financial content according to all specified requirements and provide the complete structured output.
    `;

    // Make structured API call
    const result = await makeStructuredCall(
      UnifiedContentEnhancementSchema,
      'unified_content_enhancement',
      systemPrompt,
      userPrompt,
      {
        temperature: options.temperature || 0.7,
        timeout: options.timeout || 30000
      }
    );

    // Track performance metrics
    const processingTime = Date.now() - startTime;

    return {
      success: true,
      data: result,
      metrics: {
        processingTime,
        tokenUsage: result.processingMetadata.tokenUsage.total,
        apiCalls: 1
      }
    };

  } catch (error) {
    const processingTime = Date.now() - startTime;

    // Attempt fallback if enabled
    if (options.enableFallback) {
      try {
        const fallbackResult = await fallbackToLegacySystem(title, content);
        return {
          success: true,
          data: fallbackResult,
          fallbackUsed: true,
          metrics: {
            processingTime: Date.now() - startTime,
            tokenUsage: 0, // Will be tracked by legacy system
            apiCalls: 3 // Typical legacy system calls
          }
        };
      } catch (fallbackError) {
        console.error('Both unified and fallback systems failed:', fallbackError);
      }
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      metrics: {
        processingTime,
        tokenUsage: 0,
        apiCalls: 1
      }
    };
  }
}

// Fallback to legacy multi-prompt system
async function fallbackToLegacySystem(
  title: string,
  content: string
): Promise<UnifiedContentEnhancement> {
  // Implementation calls existing enhanceAndTranslateDualLanguage
  // and transforms response to unified format
  const legacyResult = await enhanceAndTranslateDualLanguage(title, content);

  // Transform legacy response to unified schema
  return transformLegacyToUnified(legacyResult);
}
```

### 4. API Endpoint Integration

#### Enhanced Route Implementation
```typescript
// File: src/app/api/articles/enhance/route.ts

import { unifiedContentEnhancement } from '@/lib/integrations/openai/unified-enhancement';

export async function POST(request: NextRequest) {
  try {
    const { articleId } = await request.json();

    // Fetch article and validate
    const article = await payload.findByID({
      collection: 'articles',
      id: articleId,
    });

    // Use unified system directly
    const result = await unifiedContentEnhancement(
      article.sourceTab?.originalTitle || article.title,
      originalContentText,
      {
        temperature: 0.7,
        enableFallback: true,
        timeout: 45000
      }
    );

    if (result.success && result.data) {
      // Transform unified result to PayloadCMS format
      const updateData = transformUnifiedToPayload(result.data);

      // Convert HTML to Lexical
      const [germanLexical, englishLexical] = await Promise.all([
        htmlToLexical(result.data.enhancedGerman.content),
        htmlToLexical(result.data.enhancedEnglish.content)
      ]);

      updateData.enhancedTab.enhancedGermanContent = germanLexical.result;
      updateData.enhancedTab.enhancedEnglishContent = englishLexical.result;

      // Update article
      const updatedArticle = await payload.update({
        collection: 'articles',
        id: articleId,
        data: updateData,
      });

      return NextResponse.json({
        success: true,
        message: 'Article enhanced using unified system',
        metrics: result.metrics,
        article: updatedArticle,
      });
    }

    // Handle failure case
    throw new Error('Unified enhancement failed');

  } catch (error) {
    // Error handling and article status update
    return handleEnhancementError(error, request);
  }
}
```

### 5. Performance Optimisation

#### Caching Strategy
```typescript
// File: src/lib/integrations/openai/enhancement-cache.ts

interface CacheEntry {
  contentHash: string;
  result: UnifiedContentEnhancement;
  timestamp: number;
  expiresAt: number;
}

class EnhancementCache {
  private cache = new Map<string, CacheEntry>();
  private readonly TTL = 24 * 60 * 60 * 1000; // 24 hours

  generateContentHash(title: string, content: string): string {
    // Create hash from title + content for cache key
    return crypto.createHash('sha256')
      .update(`${title}:${content}`)
      .digest('hex')
      .substring(0, 16);
  }

  get(title: string, content: string): UnifiedContentEnhancement | null {
    const hash = this.generateContentHash(title, content);
    const entry = this.cache.get(hash);

    if (entry && entry.expiresAt > Date.now()) {
      return entry.result;
    }

    // Clean expired entry
    if (entry) {
      this.cache.delete(hash);
    }

    return null;
  }

  set(title: string, content: string, result: UnifiedContentEnhancement): void {
    const hash = this.generateContentHash(title, content);
    this.cache.set(hash, {
      contentHash: hash,
      result,
      timestamp: Date.now(),
      expiresAt: Date.now() + this.TTL
    });
  }
}
```

#### Monitoring Integration
```typescript
// File: src/lib/integrations/openai/unified-monitoring.ts

export class UnifiedEnhancementMonitor {
  private metrics: Array<{
    timestamp: number;
    processingTime: number;
    tokenUsage: number;
    success: boolean;
    fallbackUsed: boolean;
    qualityScore: number;
  }> = [];

  recordOperation(result: {
    processingTime: number;
    tokenUsage: number;
    success: boolean;
    fallbackUsed?: boolean;
    qualityScore?: number;
  }): void {
    this.metrics.push({
      timestamp: Date.now(),
      ...result,
      fallbackUsed: result.fallbackUsed || false,
      qualityScore: result.qualityScore || 0
    });

    // Keep only last 1000 entries
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
  }

  getPerformanceStats(windowHours: number = 24): {
    averageProcessingTime: number;
    averageTokenUsage: number;
    successRate: number;
    fallbackRate: number;
    averageQualityScore: number;
    totalOperations: number;
  } {
    const cutoff = Date.now() - (windowHours * 60 * 60 * 1000);
    const recentMetrics = this.metrics.filter(m => m.timestamp > cutoff);

    if (recentMetrics.length === 0) {
      return {
        averageProcessingTime: 0,
        averageTokenUsage: 0,
        successRate: 0,
        fallbackRate: 0,
        averageQualityScore: 0,
        totalOperations: 0
      };
    }

    const successful = recentMetrics.filter(m => m.success);
    const fallbacks = recentMetrics.filter(m => m.fallbackUsed);

    return {
      averageProcessingTime: successful.reduce((sum, m) => sum + m.processingTime, 0) / successful.length,
      averageTokenUsage: successful.reduce((sum, m) => sum + m.tokenUsage, 0) / successful.length,
      successRate: (successful.length / recentMetrics.length) * 100,
      fallbackRate: (fallbacks.length / recentMetrics.length) * 100,
      averageQualityScore: successful.reduce((sum, m) => sum + m.qualityScore, 0) / successful.length,
      totalOperations: recentMetrics.length
    };
  }
}
```

---

## 🧪 Testing Strategy

### 1. Unit Testing

#### Unified Prompt Testing
```typescript
// File: src/lib/integrations/openai/__tests__/unified-enhancement.test.ts

describe('Unified Content Enhancement', () => {
  const sampleGermanContent = `
    Tesla steigert Umsatz im dritten Quartal um 24 Prozent
    Der Elektroautohersteller Tesla hat im dritten Quartal seinen Umsatz...
  `;

  it('should enhance German content and translate to English in single call', async () => {
    const result = await unifiedContentEnhancement(
      'Tesla Q3 Ergebnisse',
      sampleGermanContent
    );

    expect(result.success).toBe(true);
    expect(result.data?.enhancedGerman.title).toBeDefined();
    expect(result.data?.enhancedEnglish.title).toBeDefined();
    expect(result.data?.qualityMetrics.contentScore).toBeGreaterThan(80);
    expect(result.metrics.apiCalls).toBe(1);
  });

  it('should fallback to legacy system on failure', async () => {
    // Mock unified system failure
    jest.spyOn(openai.responses, 'parse').mockRejectedValueOnce(new Error('API Error'));

    const result = await unifiedContentEnhancement(
      'Test Title',
      sampleGermanContent,
      { enableFallback: true }
    );

    expect(result.success).toBe(true);
    expect(result.fallbackUsed).toBe(true);
    expect(result.metrics.apiCalls).toBeGreaterThan(1);
  });
});
```

#### Schema Validation Testing
```typescript
describe('Unified Schema Validation', () => {
  it('should validate complete unified response', () => {
    const mockResponse = {
      enhancedGerman: {
        title: 'Enhanced German Title',
        content: '<p>Enhanced German content...</p>',
        summary: 'German summary...',
        insights: ['Insight 1', 'Insight 2', 'Insight 3'],
        metadata: {
          seoTitle: 'SEO German Title',
          description: 'German meta description...',
          keywords: ['tesla', 'umsatz', 'quartal', 'elektroauto', 'finanzen']
        }
      },
      // ... complete schema validation
    };

    const validation = UnifiedContentEnhancementSchema.safeParse(mockResponse);
    expect(validation.success).toBe(true);
  });
});
```

### 2. Integration Testing

#### API Endpoint Testing
```typescript
// File: src/app/api/articles/__tests__/enhance-unified.test.ts

describe('Enhanced Article API with Unified System', () => {
  it('should process article through unified enhancement', async () => {
    const response = await fetch('/api/articles/enhance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ articleId: 'test-article-id' })
    });

    const result = await response.json();

    expect(result.success).toBe(true);
    expect(result.metrics.processingTime).toBeLessThan(10000); // Under 10 seconds
    expect(result.article.enhancedTab.enhancedGermanContent).toBeDefined();
    expect(result.article.enhancedTab.enhancedEnglishContent).toBeDefined();
  });
});
```

### 3. Performance Testing

#### Load Testing Framework
```typescript
// File: src/lib/integrations/openai/__tests__/performance.test.ts

describe('Unified Enhancement Performance', () => {
  it('should meet performance targets under load', async () => {
    const testArticles = generateTestArticles(10);
    const startTime = Date.now();

    const results = await Promise.all(
      testArticles.map(article =>
        unifiedContentEnhancement(article.title, article.content)
      )
    );

    const totalTime = Date.now() - startTime;
    const averageTime = totalTime / testArticles.length;

    expect(averageTime).toBeLessThan(8000); // Target: under 8 seconds average
    expect(results.every(r => r.success)).toBe(true);
  });
});
```

---

## 🚀 Deployment Strategy

### 1. Feature Flag Implementation

#### Environment Configuration
```typescript
// Environment variables for gradual rollout
UNIFIED_ENHANCEMENT_ENABLED=false          // Master toggle
UNIFIED_ENHANCEMENT_PERCENTAGE=0           // Percentage of traffic (0-100)
UNIFIED_ENHANCEMENT_WHITELIST=[]           // Specific article IDs for testing
UNIFIED_ENHANCEMENT_MONITORING=true        // Enable detailed monitoring
```

#### Runtime Control
```typescript
// File: src/lib/integrations/openai/feature-flags.ts

export function shouldUseUnifiedEnhancement(articleId?: string): boolean {
  // Check master toggle
  if (process.env.UNIFIED_ENHANCEMENT_ENABLED !== 'true') {
    return false;
  }

  // Check whitelist for specific testing
  const whitelist = JSON.parse(process.env.UNIFIED_ENHANCEMENT_WHITELIST || '[]');
  if (whitelist.length > 0) {
    return whitelist.includes(articleId);
  }

  // Check percentage rollout
  const percentage = parseInt(process.env.UNIFIED_ENHANCEMENT_PERCENTAGE || '0');
  if (percentage === 0) return false;
  if (percentage >= 100) return true;

  // Use article ID hash for consistent routing
  const hash = crypto.createHash('md5').update(articleId || '').digest('hex');
  const hashNumber = parseInt(hash.substring(0, 8), 16);
  return (hashNumber % 100) < percentage;
}
```

### 2. Monitoring Dashboard

#### Real-time Metrics
```typescript
// File: src/app/api/admin/enhancement-metrics/route.ts

export async function GET() {
  const unifiedStats = unifiedMonitor.getPerformanceStats(24);
  const legacyStats = legacyMonitor.getPerformanceStats(24);

  return NextResponse.json({
    unified: {
      ...unifiedStats,
      costPerArticle: (unifiedStats.averageTokenUsage * 0.00002).toFixed(4),
      targetMet: {
        processingTime: unifiedStats.averageProcessingTime < 10000,
        cost: (unifiedStats.averageTokenUsage * 0.00002) < 0.04,
        quality: unifiedStats.averageQualityScore > 95
      }
    },
    legacy: {
      ...legacyStats,
      costPerArticle: estimateLegacyCost(legacyStats).toFixed(4)
    },
    comparison: {
      processingTimeImprovement: calculateImprovement(
        legacyStats.averageProcessingTime,
        unifiedStats.averageProcessingTime
      ),
      costImprovement: calculateCostImprovement(legacyStats, unifiedStats),
      qualityDifference: unifiedStats.averageQualityScore - legacyStats.averageQualityScore
    }
  });
}
```

### 3. Rollback Procedures

#### Automatic Rollback Triggers
```typescript
// File: src/lib/integrations/openai/auto-rollback.ts

export class AutoRollbackManager {
  private readonly thresholds = {
    maxErrorRate: 10,        // 10% error rate
    minQualityScore: 90,     // Minimum quality score
    maxProcessingTime: 15000 // 15 seconds
  };

  async checkRollbackConditions(): Promise<boolean> {
    const stats = unifiedMonitor.getPerformanceStats(1); // Last hour

    // Check error rate
    if (stats.successRate < (100 - this.thresholds.maxErrorRate)) {
      await this.triggerRollback('High error rate');
      return true;
    }

    // Check quality score
    if (stats.averageQualityScore < this.thresholds.minQualityScore) {
      await this.triggerRollback('Quality degradation');
      return true;
    }

    // Check processing time
    if (stats.averageProcessingTime > this.thresholds.maxProcessingTime) {
      await this.triggerRollback('Performance degradation');
      return true;
    }

    return false;
  }

  private async triggerRollback(reason: string): Promise<void> {
    // Disable unified system
    process.env.UNIFIED_ENHANCEMENT_ENABLED = 'false';

    // Log rollback
    console.error(`ROLLBACK TRIGGERED: ${reason}`);

    // Send alerts
    await sendRollbackAlert(reason);
  }
}
```

---

## 📊 Success Metrics Tracking

### Performance Metrics
```typescript
interface PerformanceTargets {
  processingTime: {
    current: 17500; // 17.5 seconds average
    target: 7000;   // 7 seconds (70% improvement)
    measurement: 'milliseconds';
  };
  costPerArticle: {
    current: 0.135; // $0.135 average
    target: 0.04;   // $0.04 (65% reduction)
    measurement: 'USD';
  };
  apiCalls: {
    current: 4;     // 3-5 calls average
    target: 1;      // Single call
    measurement: 'count';
  };
  qualityScore: {
    current: 95;    // 95% quality maintenance
    target: 95;     // Maintain or improve
    measurement: 'percentage';
  };
}
```

### Quality Assurance Metrics
```typescript
interface QualityMetrics {
  contentStructure: {
    germanEnhancement: number;      // Quality score 0-100
    englishTranslation: number;     // Quality score 0-100
    structurePreservation: boolean; // Structure maintained
  };

  seoOptimisation: {
    titleOptimisation: number;      // SEO score 0-100
    metadataQuality: number;        // Metadata completeness
    keywordRelevance: number;       // Keyword quality 0-100
  };

  marketContext: {
    canadianRelevance: number;      // Relevance score 0-100
    insightQuality: number;         // Insight usefulness 0-100
    factualAccuracy: boolean;       // Factual accuracy maintained
  };
}
```

---

## 🎯 Success Criteria

### Technical Success Criteria
1. **✅ Single API Call Architecture**: Replace 3-5 calls with 1 call
2. **✅ Schema Validation**: 100% structured output compliance
3. **✅ Lexical Compatibility**: No regression in HTML→Lexical conversion
4. **✅ Backward Compatibility**: Legacy system remains functional during transition

### Performance Success Criteria
1. **✅ 70% Processing Time Improvement**: Average time under 10 seconds
2. **✅ 65% Cost Reduction**: Average cost under $0.04 per article
3. **✅ Quality Maintenance**: 95%+ content quality score maintained
4. **✅ Error Rate**: Maintain <5% error rate

### Operational Success Criteria
1. **✅ Zero Downtime Deployment**: Successful feature flag rollout
2. **✅ Monitoring Coverage**: Comprehensive metrics tracking
3. **✅ Rollback Capability**: Automatic fallback mechanisms functional
4. **✅ Code Simplification**: 70% reduction in prompt management complexity

**Implementation Timeline**: 4-6 weeks with phased rollout and comprehensive validation
