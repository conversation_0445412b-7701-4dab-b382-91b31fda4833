# Unified Schema Design - Task 1.2 Results

**Date**: 2025-07-07
**Task**: 1.2 Schema Architecture Design
**Status**: ✅ Complete

## 📋 Current Schema Analysis

### Existing DualLanguageEnhancementSchema Structure
```typescript
// Current schema (lines 130-215 in schemas.ts)
export const DualLanguageEnhancementSchema = z.object({
  enhancedGerman: {
    title: string,
    content: string,
    summary: string,
    keyInsights: string[],
    canadianContext: string,
  },
  enhancedEnglish: {
    title: string,
    content: string,
    summary: string,
    keyInsights: string[],
    canadianContext: string,
  },
  consistency: {
    structureMatches: boolean,
    contentAlignment: 'perfect' | 'good' | 'fair',
    translationQuality: 'excellent' | 'good' | 'fair',
  },
  enhancement: {
    canadianRelevance: number,
    addedValue: string[],
    marketImplications: string,
  },
});
```

### Functions Missing from Current Schema
1. **Metadata Generation** (SEO titles, descriptions, keywords)
2. **Quality Assessment** (content quality scores, improvement suggestions)
3. **Market Relevance Scoring** (comprehensive market analysis)
4. **Processing Metadata** (performance tracking, token usage)

## 🎯 Enhanced Unified Schema Design

### UnifiedContentEnhancementSchema

```typescript
export const UnifiedContentEnhancementSchema = z.object({
  // Enhanced German Content
  enhancedGerman: z.object({
    title: z
      .string()
      .min(10)
      .max(200)
      .describe('Enhanced German title with Canadian market context'),
    content: z
      .string()
      .min(100)
      .describe('Enhanced German content in clean HTML format suitable for Lexical conversion'),
    summary: z
      .string()
      .min(50)
      .max(450)
      .describe('German summary (max 450 characters)'),
    insights: z
      .array(z.string())
      .min(3)
      .max(10)
      .describe('Key insights for Canadian investors in German'),
    metadata: z.object({
      seoTitle: z
        .string()
        .min(30)
        .max(60)
        .describe('SEO-optimized German title (30-60 characters)'),
      description: z
        .string()
        .min(120)
        .max(160)
        .describe('Meta description in German (120-160 characters)'),
      keywords: z
        .array(z.string())
        .min(5)
        .max(12)
        .describe('German SEO keywords based on content'),
    }),
  }),

  // Enhanced English Content
  enhancedEnglish: z.object({
    title: z
      .string()
      .min(10)
      .max(200)
      .describe('English translation of enhanced German title'),
    content: z
      .string()
      .min(100)
      .describe('English translation in clean HTML format suitable for Lexical conversion'),
    summary: z
      .string()
      .min(50)
      .max(450)
      .describe('English summary (max 450 characters)'),
    insights: z
      .array(z.string())
      .min(3)
      .max(10)
      .describe('Key insights for Canadian investors in English'),
    metadata: z.object({
      seoTitle: z
        .string()
        .min(30)
        .max(60)
        .describe('SEO-optimized English title (30-60 characters)'),
      description: z
        .string()
        .min(120)
        .max(160)
        .describe('Meta description in English (120-160 characters)'),
      keywords: z
        .array(z.string())
        .min(5)
        .max(12)
        .describe('English SEO keywords based on content'),
    }),
  }),

  // Quality Assessment Metrics
  qualityMetrics: z.object({
    contentScore: z
      .number()
      .min(0)
      .max(100)
      .describe('Overall content quality score (0-100)'),
    translationQuality: z
      .enum(['excellent', 'good', 'fair'])
      .describe('Translation quality assessment'),
    structurePreservation: z
      .boolean()
      .describe('Whether structure was preserved between languages'),
    canadianRelevance: z
      .number()
      .min(0)
      .max(100)
      .describe('Canadian market relevance score (0-100)'),
    seoOptimization: z
      .number()
      .min(0)
      .max(100)
      .describe('SEO optimization score (0-100)'),
    readabilityScore: z
      .number()
      .min(0)
      .max(100)
      .describe('Content readability score (0-100)'),
  }),

  // Market Analysis
  marketAnalysis: z.object({
    relevanceScore: z
      .number()
      .min(0)
      .max(100)
      .describe('Overall market relevance score'),
    reasoning: z
      .string()
      .min(50)
      .max(500)
      .describe('Explanation of relevance assessment'),
    companiesMentioned: z
      .array(z.string())
      .optional()
      .describe('Companies mentioned in the content'),
    relevantSectors: z
      .array(z.string())
      .min(1)
      .max(10)
      .describe('Market sectors relevant to the content'),
    keywordsMatched: z
      .array(z.string())
      .min(3)
      .max(15)
      .describe('Financial keywords found in content'),
    geographicalRelevance: z
      .string()
      .optional()
      .describe('Geographical market connections (if naturally present)'),
  }),

  // Content Consistency Check
  consistency: z.object({
    structureMatches: z
      .boolean()
      .describe('Whether German and English content have identical structure'),
    contentAlignment: z
      .enum(['perfect', 'good', 'fair'])
      .describe('Content alignment quality between languages'),
    translationAccuracy: z
      .enum(['excellent', 'good', 'fair'])
      .describe('Translation accuracy assessment'),
    insightsConsistency: z
      .boolean()
      .describe('Whether insights are consistent between languages'),
    metadataAlignment: z
      .boolean()
      .describe('Whether metadata is properly aligned between languages'),
  }),

  // Enhancement Details
  enhancement: z.object({
    addedValue: z
      .array(z.string())
      .min(1)
      .max(5)
      .describe('Specific value additions made for Canadian audience'),
    marketImplications: z
      .string()
      .min(50)
      .max(500)
      .describe('Market implications for Canadian investors'),
    improvementSuggestions: z
      .array(z.string())
      .max(5)
      .describe('Suggestions for further content improvement'),
    canadianContext: z
      .string()
      .min(20)
      .max(300)
      .describe('Canadian market context added to content'),
  }),

  // Processing Metadata
  processingMetadata: z.object({
    modelUsed: z
      .string()
      .describe('OpenAI model used for processing'),
    processingTime: z
      .number()
      .describe('Processing time in milliseconds'),
    tokenUsage: z.object({
      prompt: z.number().describe('Prompt tokens used'),
      completion: z.number().describe('Completion tokens used'),
      total: z.number().describe('Total tokens used'),
    }),
    apiCallsReduced: z
      .number()
      .describe('Number of API calls saved by using unified system'),
    originalSystemEquivalent: z
      .number()
      .describe('Equivalent API calls in old 7-prompt system'),
  }),
});
```

## 🔄 Schema Migration Strategy

### Backward Compatibility
```typescript
// Transformation function for existing API consumers
export function transformToLegacyFormat(
  unifiedResult: UnifiedContentEnhancement
): DualLanguageEnhancement {
  return {
    enhancedGerman: {
      title: unifiedResult.enhancedGerman.title,
      content: unifiedResult.enhancedGerman.content,
      summary: unifiedResult.enhancedGerman.summary,
      keyInsights: unifiedResult.enhancedGerman.insights,
      canadianContext: unifiedResult.enhancement.canadianContext,
    },
    enhancedEnglish: {
      title: unifiedResult.enhancedEnglish.title,
      content: unifiedResult.enhancedEnglish.content,
      summary: unifiedResult.enhancedEnglish.summary,
      keyInsights: unifiedResult.enhancedEnglish.insights,
      canadianContext: unifiedResult.enhancement.canadianContext,
    },
    consistency: {
      structureMatches: unifiedResult.consistency.structureMatches,
      contentAlignment: unifiedResult.consistency.contentAlignment,
      translationQuality: unifiedResult.consistency.translationAccuracy,
    },
    enhancement: {
      canadianRelevance: unifiedResult.qualityMetrics.canadianRelevance,
      addedValue: unifiedResult.enhancement.addedValue,
      marketImplications: unifiedResult.enhancement.marketImplications,
    },
  };
}
```

## 📊 Schema Coverage Analysis

### Functions Covered by Unified Schema

| Original Prompt Function | Schema Section | Coverage Level |
|--------------------------|----------------|----------------|
| `ENHANCE_GERMAN_CONTENT_PROMPT` | `enhancedGerman` | ✅ Complete |
| `TRANSLATE_TO_ENGLISH_PROMPT` | `enhancedEnglish` | ✅ Complete |
| `GENERATE_METADATA_PROMPT` | `metadata` (both languages) | ✅ Complete |
| `MARKET_RELEVANCE_PROMPT` | `marketAnalysis` | ✅ Complete |
| `CONTENT_QUALITY_PROMPT` | `qualityMetrics` | ✅ Complete |
| `LITERAL_TRANSLATION_PROMPT` | `enhancedEnglish` + `consistency` | ✅ Complete |
| `STRUCTURE_AWARE_TRANSLATION_PROMPT` | `consistency` + HTML output | ✅ Complete |

### New Features Added
- **Comprehensive SEO Metadata**: Both German and English SEO optimization
- **Quality Assessment**: Multiple quality metrics with scoring
- **Market Analysis**: Enhanced market relevance with detailed reasoning
- **Processing Metadata**: Performance tracking and API call reduction metrics
- **Content Consistency**: Cross-language validation and alignment checks

## 🔧 Implementation Details

### PayloadCMS Field Mapping
```typescript
// Mapping unified schema to PayloadCMS fields
const payloadFieldMapping = {
  'enhancedTab.enhancedGermanContent': 'enhancedGerman.content',
  'enhancedTab.enhancedEnglishContent': 'enhancedEnglish.content',
  'enhancedTab.enhancedGermanSummary': 'enhancedGerman.summary',
  'enhancedTab.enhancedEnglishSummary': 'enhancedEnglish.summary',
  'enhancedTab.germanInsights': 'enhancedGerman.insights',
  'enhancedTab.englishInsights': 'enhancedEnglish.insights',
  'seoTab.germanSeoTitle': 'enhancedGerman.metadata.seoTitle',
  'seoTab.englishSeoTitle': 'enhancedEnglish.metadata.seoTitle',
  'seoTab.germanDescription': 'enhancedGerman.metadata.description',
  'seoTab.englishDescription': 'enhancedEnglish.metadata.description',
  'seoTab.germanKeywords': 'enhancedGerman.metadata.keywords',
  'seoTab.englishKeywords': 'enhancedEnglish.metadata.keywords',
};
```

### Validation Rules
- **Content Length**: Minimum 100 characters for main content
- **Summary Length**: Maximum 450 characters for summaries
- **SEO Compliance**: Titles 30-60 chars, descriptions 120-160 chars
- **Quality Thresholds**: Minimum quality score of 70/100
- **Market Relevance**: Minimum relevance score of 40/100

## 📋 Task 1.2 Completion Checklist

✅ **Reviewed existing DualLanguageEnhancementSchema structure**
✅ **Designed enhanced schema to include metadata generation**
✅ **Added quality assessment fields to schema**
✅ **Integrated market relevance scoring**
✅ **Added processing metadata for performance tracking**
✅ **Created comprehensive TypeScript types**
✅ **Designed backward compatibility transformation**
✅ **Planned PayloadCMS field mapping**

## 🎯 Next Steps

**Task 1.3**: Establish current system performance baseline
**Task 2.1**: Create master unified prompt using this schema
**Task 2.2**: Implement the enhanced schema with validation

**Schema File**: `src/lib/integrations/openai/schemas.ts` (extend existing)
**Confidence Level**: High - Schema covers all 7 prompt functions comprehensively
