# Field Investigation & Remediation Plan

## <PERSON><PERSON><PERSON> Blick Article Collection Missing Fields

**Date**: 2025-01-10  
**Status**: Investigation Complete - Action Plan Ready  
**Priority**: HIGH - Critical functionality missing  
**Estimated Effort**: 2-3 hours

---

## 🚨 **EXECUTIVE SUMMARY**

After successful schema cleanup (fixing `status`→`workflowStage`), **critical article fields are missing or broken**:

- ❌ **Company/ticker data not displaying** (relatedCompanies field missing)
- ❌ **Key insights missing** (field definition unclear)
- ❌ **English summary not appearing** (SEO plugin field mismatch)
- ❌ **Translation button missing** (admin component logic issues)

**Root Cause**: Schema cleanup focused on `workflowStage` but inadvertently affected other field definitions and references.

**Impact**: Content processing works perfectly (logs confirm company extraction, enhancement success) but data isn't reaching the admin interface due to field mapping issues.

---

## 🔍 **DETAILED INVESTIGATION FINDINGS**

### **✅ What's Working:**

- Content pipeline processing (100% success - 9 articles created)
- Company extraction (logs: "🏢 Extracted 2 companies from English content")
- Enhancement processing (English-only enhancement successful)
- Database schema sync (workflowStage correctly implemented)

### **❌ What's Broken:**

#### **1. Missing `relatedCompanies` Field**

- **Issue**: Field completely absent from `src/collections/Articles.ts`
- **Evidence**: Processing logs show company extraction working but no schema field to store data
- **Impact**: Company/ticker information lost during save

#### **2. SEO Plugin Field Mismatch**

- **Issue**: Plugin expects `doc.englishTab?.summary` but field is `doc.englishTab?.enhancedSummary`
- **Evidence**: SEO plugin configuration references wrong field name
- **Impact**: Meta descriptions and SEO data not populating

#### **3. Admin Interface Field References**

- **Issue**: Components may reference old field names or incorrect paths
- **Evidence**: Translation button missing despite workflowStage correctly implemented
- **Impact**: Admin functionality degraded

#### **4. Key Insights Definition Unclear**

- **Issue**: No clear field definition for "key insights" in current schema
- **Evidence**: User reports missing but no obvious field in Articles.ts
- **Impact**: Important analytical data not available

---

## 🎯 **STEP-BY-STEP ACTION PLAN**

### **Phase 1: Schema Fixes (HIGH PRIORITY - 1 hour)**

#### **Step 1.1: Add Missing `relatedCompanies` Field**

```typescript
// Add to src/collections/Articles.ts in sourcesTab.fields
{
  name: 'relatedCompanies',
  type: 'array',
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'ticker',
      type: 'text',
    },
    {
      name: 'relevance',
      type: 'select',
      options: ['high', 'medium', 'low'],
      defaultValue: 'medium',
    },
    {
      name: 'confidence',
      type: 'number',
      min: 0,
      max: 100,
      defaultValue: 100,
    },
  ],
},
```

#### **Step 1.2: Fix SEO Plugin Field Reference**

```typescript
// Update src/payload.config.ts SEO plugin
seo({
  collections: ['articles'],
  fields: [
    {
      name: 'meta',
      fields: [
        {
          name: 'description',
          type: 'textarea',
          admin: {
            description: 'Generated from enhanced summary',
          },
          hooks: {
            beforeChange: [
              ({ siblingData, data }) => {
                // Fix: Use enhancedSummary instead of summary
                return data?.englishTab?.enhancedSummary || siblingData?.meta?.description;
              },
            ],
          },
        },
      ],
    },
  ],
}),
```

#### **Step 1.3: Add Key Insights Field**

```typescript
// Add to englishTab.fields in Articles.ts
{
  name: 'keyInsights',
  type: 'array',
  fields: [
    {
      name: 'insight',
      type: 'textarea',
      required: true,
    },
    {
      name: 'priority',
      type: 'select',
      options: ['high', 'medium', 'low'],
      defaultValue: 'medium',
    },
  ],
  admin: {
    description: 'Key financial insights and takeaways',
  },
},
```

### **Phase 2: Processing Code Alignment (MEDIUM PRIORITY - 30 mins)**

#### **Step 2.1: Update Company Extraction Storage**

```typescript
// Find and update the company extraction code to match new field structure
// Should be in enhancement/processing files
// Change from top-level relatedCompanies to sourcesTab.relatedCompanies
```

#### **Step 2.2: Verify Data Flow**

- Test that processed companies reach `sourcesTab.relatedCompanies`
- Verify field names match between processing and schema
- Check data transformation logic

### **Phase 3: Admin Interface Fixes (MEDIUM PRIORITY - 30 mins)**

#### **Step 3.1: Fix Translation Button Logic**

```typescript
// Update src/components/admin/article-actions/DocumentControls.tsx
// Ensure workflowStage logic is correct for showing translation button
const showTranslationButton =
  workflowStage === 'enhanced' || workflowStage === 'ready-for-review';
```

#### **Step 3.2: Add Company Display Components**

- Create admin components to display relatedCompanies data
- Ensure key insights are visible in admin interface
- Test field rendering in admin

### **Phase 4: Testing & Validation (LOW PRIORITY - 30 mins)**

#### **Step 4.1: Schema Regeneration**

```bash
pkill -f "next dev"
pnpm dev
npx payload generate:types
```

#### **Step 4.2: End-to-End Test**

```bash
curl -X POST http://localhost:3000/api/test-content-pipeline
```

#### **Step 4.3: Admin Interface Verification**

- Check that all fields display correctly
- Verify translation button appears
- Test company/ticker data visibility
- Confirm key insights are populated

---

## 🔧 **TECHNICAL INVESTIGATION METHODOLOGY**

### **Tools Used:**

- `codebase_search` - Semantic searches for field usage patterns
- `grep_search` - Exact field name matching across codebase
- `read_file` - Schema and component inspection
- Content pipeline logs - Verification of processing success

### **Key Files Investigated:**

- `src/collections/Articles.ts` - Schema definition
- `src/payload-types.ts` - Generated types
- `src/components/admin/article-actions/DocumentControls.tsx` - Admin components
- `src/payload.config.ts` - SEO plugin configuration
- Processing pipeline logs - Data flow verification

### **Evidence Pattern:**

- **Processing works** (logs confirm company extraction, enhancement)
- **Schema incomplete** (missing fields for storing processed data)
- **Admin broken** (components can't find expected data)
- **Types misaligned** (field names don't match expectations)

---

## ✅ **SUCCESS CRITERIA**

### **Immediate Success:**

- [ ] `relatedCompanies` field added to schema and visible in admin
- [ ] Key insights field defined and populated
- [ ] English summary displaying in SEO plugin
- [ ] Translation button appearing for appropriate workflow stages

### **Long-term Success:**

- [ ] Full content pipeline → admin interface data flow working
- [ ] All processed data (companies, insights, summaries) visible
- [ ] No data loss during article creation
- [ ] Admin interface fully functional

---

## 📊 **CURRENT SYSTEM STATUS**

### **✅ Working Components:**

- Database schema sync (workflowStage correctly implemented)
- Content processing pipeline (100% success rate)
- Basic admin interface (articles displaying, workflow logic working)
- RSS feed loading and keyword matching

### **❌ Broken Components:**

- Company data display (processing works, storage/display broken)
- Key insights visibility (undefined field structure)
- SEO meta generation (field name mismatch)
- Complete admin functionality (missing data fields)

---

## 🚀 **IMPLEMENTATION PRIORITY**

1. **CRITICAL (Do First)**: Add `relatedCompanies` field - companies are being extracted but lost
2. **HIGH**: Fix SEO plugin field reference - meta descriptions broken
3. **MEDIUM**: Add key insights field - important for user experience
4. **LOW**: Admin interface polish - system functional without

**Estimated Total Time**: 2-3 hours for complete remediation

---

## 📝 **NOTES FOR IMPLEMENTATION**

- **Test after each phase** - Don't batch all changes
- **Check logs carefully** - Processing success != storage success
- **Verify field paths** - Tab structure adds complexity to field references
- **Regenerate types** - Critical after schema changes
- **Monitor data flow** - From processing → database → admin interface

The system is **very close to full functionality** - these are "last mile" field mapping issues rather than fundamental problems.
