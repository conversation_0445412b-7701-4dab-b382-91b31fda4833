# Article Single Page Implementation Plan

**Date**: 2025-01-28  
**Status**: Planning Phase  
**Priority**: High - Critical Gap (404 errors on article links)  
**Estimated Time**: 1-2 days

## ⚠️ OPEN QUESTIONS

1. **SEO Metadata Strategy**: Should individual articles use the SEO plugin's meta fields or generate metadata dynamically from German/English content tabs?

Answer: We should use the SEO plugin's meta fields for individual articles.

2. **Related Articles Logic**: What algorithm should determine "related" articles - by category, keywords, or AI similarity scoring?

Answer: We should use matching keywords and categories to determine related articles for now. A future phase would be more sophisticated.

3. **Content Prioritisation**: When both German and English content exist, which should be primary for German-speaking users?

Answer: We should show the German content always

4. **Social Sharing**: Do we need Open Graph and Twitter card implementations for article sharing?

Answer: Good catch: Yes. Use the SEO Plugin meta fields for Open Graph and Twitter card metadata.

5. **Breadcrumb Navigation**: Should articles include category-based breadcrumbs or simple back navigation?

Answer: None

---

## Overview

Implement individual article page components that render published articles from the Articles collection using the established 4-column grid system. This addresses the critical gap where articles link to `/articles/[slug]` routes that currently return 404 errors.

**✅ Aligned with Reference Materials:**

- **Next.js 15 Patterns**: Following `await params` pattern, proper `generateMetadata` typing, and `unstable_cache` strategies from nextjs-llm.txt
- **PayloadCMS Best Practices**: Using `getPayload({ config })` pattern, existing `getDocument` utility, and SEO plugin integration from payloadcms-llm.txt
- **Existing Codebase**: Leveraging existing Lexical utilities, cache patterns, and component structures rather than duplicating functionality
- **Visual Design Reference**: Implementation based on provided visual mockup showing 4-column layout with clean typography and structured content hierarchy

## Current State Analysis

### Existing Infrastructure ✅

- Articles collection with comprehensive multi-tab structure (English/German/Sources/SEO)
- 4-column grid layout pattern from homepage (`max-w-[1440px] mx-auto`)
- NewsCard component for article display in various formats
- Typography system with Merriweather (serif) and Roboto (sans-serif) fonts
- Caching infrastructure with `unstable_cache` and revalidation
- PayloadCMS type generation with Article interface
- SEO plugin integrated with meta fields

### Missing Components ❌

- No article page component at `src/app/(frontend)/articles/[slug]/page.tsx`
- No article content rendering utilities for Lexical content
- No metadata generation for individual articles
- No related articles fetching logic
- No social sharing implementation

## Implementation Strategy

### Phase 1: Core Article Page Foundation (Day 1 - Morning)

#### 1.1 Create Article Page Route Structure

**Files to Create:**

- `src/app/(frontend)/articles/[slug]/page.tsx` - Main article page component
- `src/app/(frontend)/articles/[slug]/not-found.tsx` - 404 handling for invalid slugs
- `src/lib/queries/articles.ts` - Article fetching utilities

**Article Page Component Structure:**

```typescript
// Follow Next.js 15 App Router patterns
export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata>;
export async function generateStaticParams(): Promise<{ slug: string }[]>;
export default async function ArticlePage({
  params,
}: {
  params: Promise<{ slug: string }>;
});
```

**Key Requirements:**

- Use `await params` pattern for Next.js 15 compatibility
- Implement proper metadata generation using SEO plugin fields
- Handle both published articles and draft preview modes
- Include proper error boundaries and loading states

#### 1.2 Article Data Fetching Logic

**Leverage Existing Utilities:**

- Use existing `getDocument('articles', slug)` from `src/utilities/getDocument.ts`
- Extend existing cache patterns from `src/lib/cache/articles.ts`

**Functions to Create:**

```typescript
// Extend existing cache utilities following PayloadCMS patterns
async function getCachedArticleBySlug(slug: string): Promise<Article | null>;
async function getPublishedArticleSlugs(): Promise<{ slug: string }[]>;
```

**Follow PayloadCMS Best Practices (from payloadcms-llm.txt):**

```typescript
// Use getPayload pattern for consistency
import { getPayload } from 'payload';
import config from '@/payload.config';

const payload = await getPayload({ config });
const articles = await payload.find({
  collection: 'articles',
  where: {
    and: [
      { slug: { equals: slug } },
      { workflowStage: { equals: 'published' } },
    ],
  },
  depth: 2,
  limit: 1,
});
```

**Next.js 15 Caching Integration:**

- Extend existing `unstable_cache` patterns from `src/lib/cache/articles.ts`
- Use same revalidation tags as homepage (`['articles', 'tier-1', 'tier-2', 'tier-3']`)
- Follow caching patterns from nextjs-llm.txt examples
- 5-minute cache for published articles (`revalidate: 300`)

**Unit Tests Required:**

- Test integration with existing `getDocument` utility
- Test PayloadCMS collection querying with proper access control
- Test cache integration with existing article cache system

### Phase 2: Content Rendering and Layout (Day 1 - Afternoon)

#### 2.1 Content Display Components

**Files to Create (based on visual reference):**

- `src/components/articles/ArticleContent.tsx` - Lexical content rendering with typography
- `src/components/articles/ArticleHeader.tsx` - Category badge, title, date/time metadata
- `src/components/articles/ArticleMetadata.tsx` - Company info panel (bordered section)
- `src/components/articles/RelatedArticles.tsx` - Sidebar cards using existing NewsCard

**ArticleHeader Component (Column 1 elements):**

```typescript
interface ArticleHeaderProps {
  article: Article;
  category: string;
  publishDate: string;
  readTime: string;
}

// Renders:
// - Category text (NO pill background)
// - Large serif title (font-merriweather)
// - Date and read time with clock icon
```

**ArticleMetadata Component (Company panel):**

```typescript
interface ArticleMetadataProps {
  companies: Article['relatedCompanies'];
}

// Renders bordered panel with:
// - Company name
// - Founder name
// - Revenue information
// - Clean key/value layout as shown in visual
```

**ArticleContent Component (Main content):**

```typescript
interface ArticleContentProps {
  article: Article;
  locale?: 'de' | 'en';
}

// Renders:
// - Hero image (prominent, rounded-lg)
// - Lexical content with proper typography
// - Section headers like "AI FOMO"
// - Clean paragraph flow
```

**RelatedArticles Component (Right sidebar):**

```typescript
interface RelatedArticlesProps {
  articles: Article[];
  maxArticles?: number;
}

// Renders:
// - "Related Articles" heading
// - NewsCard components (variant="title-only")
// - Category colors and read times
// - Matches visual styling exactly
```

**Content Prioritisation Logic (German-first per user requirement):**

1. Always show German content if available (`germanTab?.germanContent`)
2. Fallback to English enhanced content (`englishTab?.enhancedContent`)
3. Final fallback to original content from Sources tab
4. Error state if no content available

#### 2.2 Lexical Content Rendering

**Use Existing PayloadCMS Utilities:**

- Leverage existing `lexicalToText` from `src/lib/utils/lexical.ts`
- Follow PayloadCMS Lexical rendering patterns from existing components
- Use existing `prepareLexicalForReading` from `src/lib/utils/lexical-validation.ts`

**Enhanced Content Renderer:**

```typescript
// Build on existing Lexical utilities rather than recreating
function renderArticleContent(
  lexicalContent: Article['germanTab']['germanContent']
): ReactNode {
  // Use existing PayloadCMS Lexical utilities
  const cleanedContent = prepareLexicalForReading(lexicalContent);
  // Apply typography classes consistent with existing components
  return renderLexicalToJSX(cleanedContent);
}
```

**Follow Existing Patterns:**

- Use existing font classes (`font-merriweather`, `font-roboto`) from layout
- Follow typography patterns from `NewsCard` component rendering
- Maintain consistency with existing Lexical field hooks in Articles collection
- Apply existing prose classes for content styling

**Error Handling Integration:**

- Use existing Lexical validation patterns from collection hooks
- Graceful fallback to source content if Lexical rendering fails
- Consistent error boundaries following existing component patterns

**Unit Tests Required:**

- Test integration with existing `lexicalToText` utility
- Test typography consistency with existing components
- Test error handling integration with existing validation

#### 2.3 Four-Column Grid Layout Implementation

**Layout Structure (based on provided visual reference):**

```typescript
<div className="max-w-[1440px] mx-auto px-4 py-12">
  <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
    {/* Column 1: Article metadata and details */}
    <div className="lg:col-span-1 space-y-6">
      {/* Category Badge - NO pill background, just text */}
      <span className="text-xs font-medium text-[#B08D57] dark:text-[#D4AF37]">
        {category}
      </span>

      {/* Large serif title */}
      <h1 className="text-xl md:text-2xl lg:text-3xl font-merriweather font-normal text-gray-900 dark:text-gray-100 leading-tight">
        {title}
      </h1>

      {/* Date and read time */}
      <div className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
        <div className="flex items-center gap-2">
          <span className="font-roboto">{publishDate}</span>
        </div>
        <div className="flex items-center gap-2">
          <Clock className="h-4 w-4" />
          <span className="font-roboto">{readTime}</span>
        </div>
      </div>

      {/* Company metadata panel (bordered section) */}
      <ArticleMetadata />
    </div>

    {/* Columns 2-3: Hero image and main content */}
    <div className="lg:col-span-2 space-y-8">
      {/* Prominent hero image */}
      <div className="overflow-hidden rounded-lg">
        <Image src={imageUrl} alt={title} className="w-full h-auto object-cover" />
      </div>

      {/* Article content with proper typography */}
      <div className="prose prose-base max-w-none dark:prose-invert">
        <div className="font-merriweather text-gray-800 dark:text-gray-200 leading-relaxed">
          <ArticleContent />
        </div>
      </div>
    </div>

    {/* Column 4: Related articles sidebar */}
    <div className="lg:col-span-1 space-y-1">
      <h3 className="font-roboto font-medium text-gray-900 dark:text-gray-100 mb-4 text-sm">
        Related Articles
      </h3>
      <RelatedArticles />
    </div>
  </div>
</div>
```

**Visual Design Implementation Notes:**

- **Column 1 (Left)**: Category text (no pill), large serif title, structured metadata including company info panel
- **Columns 2-3 (Center)**: Hero image prominence, clean content flow with section headers like "AI FOMO"
- **Column 4 (Right)**: Related articles using existing NewsCard component with category colors and read times
- **Typography Hierarchy**: Merriweather for headlines/content, Roboto for UI elements
- **Responsive**: Mobile stacked, desktop 4-column grid with proper gap spacing

### Phase 3: Enhanced Features and Polish (Day 2)

#### 3.1 Related Articles Logic

**File to Create:**

- `src/lib/queries/related-articles.ts`

**Algorithm Implementation:**

```typescript
async function getRelatedArticles(
  article: Article,
  limit: number = 4
): Promise<Article[]>;
```

**Ranking Logic:**

1. Same category articles (weight: 0.4)
2. Shared keywords match (weight: 0.3)
3. Same placement tier (weight: 0.2)
4. Recent publication date (weight: 0.1)

**Caching:**

- Cache related articles for 10 minutes
- Tag with `['related-articles', `article-${articleId}`]`

#### 3.2 Metadata and SEO Enhancement

**Follow SEO Plugin Patterns (from payloadcms-llm.txt):**

```typescript
// Create generateMeta utility following SEO plugin examples
import { generateMeta } from '@/utilities/generateMeta';

export async function generateMetadata({ params }): Promise<Metadata> {
  const { slug } = await params;

  // Fetch article using existing patterns
  const article = await getDocument('articles', slug);

  if (!article) {
    return {
      title: 'Article Not Found | Börsen Blick',
      description: 'The requested article could not be found.',
    };
  }

  // Use SEO plugin's generateMeta utility
  return generateMeta({ doc: article });
}
```

**SEO Plugin Integration:**

- Use existing SEO plugin meta fields (`meta.title`, `meta.description`, `meta.image`)
- Follow established `generateMeta` utility pattern
- Leverage automatic Open Graph and Twitter card generation from plugin
- Include structured data (JSON-LD) following plugin conventions

**Files to Create/Update:**

- Create `src/utilities/generateMeta.ts` (if not exists) following SEO plugin patterns
- Update `src/app/(frontend)/articles/[slug]/page.tsx` with proper metadata generation

#### 3.3 Performance Optimisations

**Image Optimisation:**

- Use Next.js `<Image>` component with proper sizing
- Implement responsive images with `sizes` attribute
- Add loading="eager" for above-fold images

**Font Optimisation:**

- Ensure font preloading for critical text
- Use appropriate font-display strategies

## Data Flow Architecture

```mermaid
graph TB
    Route["/articles/[slug]"] --> Fetch["getArticleBySlug()"]
    Fetch --> Cache{"Cached?"}
    Cache -->|Yes| Render["Render Article"]
    Cache -->|No| DB["PayloadCMS Query"]
    DB --> Store["Cache Result"]
    Store --> Render

    Render --> Layout["4-Column Layout"]
    Layout --> Header["ArticleHeader"]
    Layout --> Content["ArticleContent"]
    Layout --> Related["RelatedArticles"]

    Content --> LexicalRender["renderLexicalContent()"]
    Related --> RelatedQuery["getRelatedArticles()"]
```

## File Structure Changes

```
src/
├── app/(frontend)/articles/[slug]/
│   ├── page.tsx              # Main article page (new)
│   └── not-found.tsx         # 404 handling (new)
├── components/articles/
│   ├── ArticleContent.tsx    # Content renderer using existing Lexical utils (new)
│   ├── ArticleHeader.tsx     # Title/metadata (new)
│   ├── ArticleMetadata.tsx   # Company/date info (new)
│   └── RelatedArticles.tsx   # Sidebar using existing NewsCard (new)
├── lib/cache/
│   └── articles.ts           # Extended with single article fetching (update)
├── utilities/
│   ├── getDocument.ts        # Leverage existing (existing)
│   └── generateMeta.ts       # SEO plugin utility (create if needed)
```

**Key Changes from Original Plan:**

- **Removed** `src/lib/queries/articles.ts` - using existing `getDocument` instead
- **Removed** `src/lib/utils/lexical-renderer.tsx` - using existing Lexical utilities
- **Extended** existing cache patterns instead of creating parallel ones
- **Leveraged** existing SEO plugin utilities for metadata generation

## Data Mapping (Articles Collection → Visual Elements)

**Based on visual reference and user requirements:**

```typescript
// Left Column (Metadata)
categoryText = article.categories?.[0]?.title || 'Technology';
title =
  article.germanTab?.germanTitle ||
  article.englishTab?.enhancedTitle ||
  article.title;
publishDate = formatDate(article.publishedAt || article.createdAt);
readTime = calculateReadTime(
  article.germanTab?.germanContent || article.englishTab?.enhancedContent
);

// Company Panel (from relatedCompanies array)
companyName = article.relatedCompanies?.[0]?.name || 'Adaptify';
founderName = extractFounderFromContent(); // derived from content
revenue = extractRevenueFromContent(); // derived from content

// Center Content
heroImage = article.featuredImage?.url || fallbackImage;
mainContent =
  article.germanTab?.germanContent || article.englishTab?.enhancedContent;
contentSections = parseContentSections(mainContent); // "AI FOMO", etc.

// Right Sidebar
relatedArticles = getRelatedArticles(
  article.categories,
  article.keywordsMatched,
  4
);
```

**Visual-to-Component Mapping:**

- **"Technology" badge** → `<ArticleHeader category={categoryText} />` (no pill styling)
- **Large headline** → `<ArticleHeader title={title} />` (font-merriweather)
- **Company panel** → `<ArticleMetadata companies={relatedCompanies} />` (bordered)
- **Hero portrait** → `<ArticleContent />` hero image section
- **"Related Articles"** → `<RelatedArticles />` using existing NewsCard

## Integration Points

### Existing Components to Reuse (based on visual reference)

- **`NewsCard`** component for related articles (variant="title-only")
- **Typography system**:
  - `font-merriweather` for headlines and article content (as seen in visual)
  - `font-roboto` for UI elements, dates, metadata
- **Color scheme** from existing codebase:
  - Category colors: `text-[#B08D57] dark:text-[#D4AF37]` (no pill background)
  - Text hierarchy: `text-gray-900 dark:text-gray-100` for primary text
- **Theme provider** for dark mode support matching homepage
- **Cache utilities** from `src/lib/cache/articles.ts` for data fetching

### PayloadCMS Integration

- Use generated types from `payload-types.ts`
- Follow existing access control patterns (`authenticatedOrPublished`)
- Leverage existing SEO plugin configuration
- Maintain consistency with collection field naming

## Testing Strategy

### Unit Tests (Vitest)

- Article fetching functions with various scenarios
- Lexical content rendering with different node types
- Related articles algorithm with mock data
- Metadata generation with different content combinations

### Integration Tests

- Full article page rendering with real PayloadCMS data
- Cache behaviour verification
- SEO metadata output validation
- Related articles relevance scoring

### Test Files to Create

- `src/lib/queries/__tests__/articles.test.ts`
- `src/lib/utils/__tests__/lexical-renderer.test.tsx`
- `src/components/articles/__tests__/ArticleContent.test.tsx`

## Risk Mitigation

### High Priority Risks

1. **Lexical Content Rendering**: Complex JSON structure may have edge cases
   - **Mitigation**: Comprehensive error handling and fallback content
2. **Performance**: Large articles with images may load slowly
   - **Mitigation**: Progressive loading and image optimisation
3. **Content Language Selection**: Logic for German vs English priority
   - **Mitigation**: Clear prioritisation rules and user feedback

### Medium Priority Risks

1. **Cache Invalidation**: Related articles cache may become stale
   - **Mitigation**: Conservative TTL and manual invalidation on article updates
2. **SEO Metadata**: Missing or incomplete SEO fields
   - **Mitigation**: Robust fallback to generated metadata

---

**Next Actions (prioritised by visual reference):**

1. **Phase 1.1**: Create article page structure with proper `await params` pattern
2. **Phase 1.2**: Implement article fetching using existing `getDocument` utility
3. **Phase 2.1**: Build components matching visual layout:
   - ArticleHeader: Category text (NO pill), large serif title, date/time
   - ArticleMetadata: Company info panel with bordered styling
   - ArticleContent: Hero image + Lexical content rendering
   - RelatedArticles: Sidebar using existing NewsCard components
4. **Phase 2.3**: Implement exact 4-column responsive grid from visual
5. **Phase 3.2**: Add SEO metadata generation using plugin utilities

**Visual Implementation Priorities:**

- ✅ Exact column layout and spacing as shown
- ✅ Typography hierarchy (Merriweather/Roboto)
- ✅ Category styling (text only, no pill background)
- ✅ Company metadata panel with clean key/value layout
- ✅ Related articles using existing NewsCard patterns
- ✅ Responsive mobile stacking behavior
