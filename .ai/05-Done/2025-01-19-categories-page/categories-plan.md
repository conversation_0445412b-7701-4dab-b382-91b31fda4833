# Category Pages Implementation Plan

**Status**: 🔄 **Planning Phase**  
**Priority**: High - Core Navigation Backbone  
**Estimated Time**: **2-3 days**  
**Target**: Build unified category page template with intelligent tier content distribution

## 📋 **PROJECT OVERVIEW**

### **✅ Problem Analysis**

Based on the provided design mockup, category pages need to:

- Serve as the backbone navigation from main header links
- Display **only articles from the specific category**
- Maintain **tier-based content hierarchy** within category constraints
- Use a **unified template** for all categories (future per-category customisation)
- Handle **intelligent content distribution** when tier content is limited

### **🎯 Core Challenge: Category-Filtered Tier Logic**

Unlike the homepage which shows:

- **Column 1**: All Tier 3 articles
- **Columns 2-3**: All Tier 1 articles
- **Column 4**: All Tier 2 articles

Category pages must show:

- **Featured Section**: Tier 1 articles **from this category only**
- **Sidebar**: Mixed Tier 1+2 articles **from this category only**
- **Additional Content**: Tier 3 + overflow **from this category only**
- **Intelligent Fallback**: When insufficient tier articles, promote from other tiers

## 🏗️ **VISUAL DESIGN ANALYSIS**

From the provided mockup:

```
┌─────────────────────────────────────────────────────────────────────┐
│ Börsen Blick    [Technology] [International] [Economics] [Investment]│
├─────────────────────────────────────────────────────────────────────┤
│                                                     │ Article Title   │
│     FEATURED ARTICLE                               │ Category • Time  │
│     (Large illustration + content)                 │ ─────────────── │
│     Global Economic Summit 2024                    │ Article Title   │
│     World leaders reach agreement...               │ Category • Time  │
│     International • 15 min read • TRENDING        │ ─────────────── │
│                                                    │ Article Title   │
├────────────────────────────────────────────────────┼─────────────────┤
│ Article Title              │ Article Title         │ Article Title   │
│ International • 5 min      │ International • 7 min │ Category • Time │
│ ─────────────────────     │ ─────────────────────  │ ─────────────── │
│ Article Title              │ Article Title         │     Tier 1+2     │
│ International • 6 min      │ International • 9 min │    Content       │
└────────────────────────────┴───────────────────────┴─────────────────┘
│                         Tier 3 Content                               │
│ US-Mexico Trade Relations  │ Brazil's Amazon Policy │ India's Digital │
│ International • 6 min      │ International • 5 min   │ International   │
└─────────────────────────────────────────────────────────────────────┘
```

### **Key Observations**:

1. **Primary Featured**: One large Tier 1 article dominates the left
2. **Secondary Featured**: 4 smaller Tier 1 articles below main feature
3. **Sidebar**: Mixed Tier 1+2 content in vertical stack
4. **Bottom Section**: Tier 3 content in horizontal grid
5. **Consistency**: All articles from same category ("International" in example)

## 🎯 **TECHNICAL IMPLEMENTATION STRATEGY**

### **Phase 1: Category Data Architecture (Day 1 Morning)**

#### 1.1 Enhanced Category Fetching

```typescript
// New category-specific caching functions
interface CategoryTierData {
  tier1: Article[];
  tier2: Article[];
  tier3: Article[];
  totalArticles: number;
  categoryInfo: Category;
}

export const getCachedCategoryArticles = (categorySlug: string) =>
  unstable_cache(
    async (): Promise<CategoryTierData> => {
      const payload = await getPayload({ config });

      // Fetch category info
      const categoryResult = await payload.find({
        collection: 'categories',
        where: { slug: { equals: categorySlug } },
        limit: 1,
      });

      if (categoryResult.docs.length === 0) {
        throw new Error(`Category not found: ${categorySlug}`);
      }

      const category = categoryResult.docs[0];

      // Fetch articles by tier for this category
      const [tier1, tier2, tier3] = await Promise.all([
        payload.find({
          collection: 'articles',
          where: {
            and: [
              { categories: { in: [category.id] } },
              { placement: { equals: 'tier-1' } },
              { workflowStage: { equals: 'published' } },
            ],
          },
          sort: ['-pinned', '-publishedAt'],
          limit: 20, // Generous limit for category pages
          depth: 2,
        }),
        payload.find({
          collection: 'articles',
          where: {
            and: [
              { categories: { in: [category.id] } },
              { placement: { equals: 'tier-2' } },
              { workflowStage: { equals: 'published' } },
            ],
          },
          sort: ['-publishedAt'],
          limit: 15,
          depth: 2,
        }),
        payload.find({
          collection: 'articles',
          where: {
            and: [
              { categories: { in: [category.id] } },
              { placement: { equals: 'tier-3' } },
              { workflowStage: { equals: 'published' } },
            ],
          },
          sort: ['-publishedAt'],
          limit: 25,
          depth: 2,
        }),
      ]);

      return {
        tier1: tier1.docs,
        tier2: tier2.docs,
        tier3: tier3.docs,
        totalArticles: tier1.totalDocs + tier2.totalDocs + tier3.totalDocs,
        categoryInfo: category,
      };
    },
    [`category-articles-${categorySlug}`],
    {
      revalidate: 300, // 5 minutes - consistent with homepage
      tags: ['articles', 'categories', `category-${categorySlug}`],
    }
  );
```

#### 1.2 Intelligent Content Distribution Logic

```typescript
interface CategoryPageLayout {
  hero: Article | null;
  featured: Article[]; // 4 articles for grid below hero
  sidebar: Article[]; // Mixed tier content for right sidebar
  additional: Article[]; // Bottom section content
  fallbackUsed: boolean;
  contentStrategy: 'tier-1-rich' | 'tier-1-sparse' | 'tier-empty';
}

const organizeCategoryLayout = (data: CategoryTierData): CategoryPageLayout => {
  const { tier1, tier2, tier3, totalArticles } = data;

  // Strategy 1: Rich Tier 1 content (6+ Tier 1 articles)
  if (tier1.length >= 6) {
    return {
      hero: tier1[0],
      featured: tier1.slice(1, 5), // Next 4 articles
      sidebar: [...tier1.slice(5), ...tier2.slice(0, 6)], // Remaining T1 + T2
      additional: tier3.slice(0, 12), // Tier 3 for bottom
      fallbackUsed: false,
      contentStrategy: 'tier-1-rich',
    };
  }

  // Strategy 2: Sparse Tier 1 content (3-5 Tier 1 articles)
  if (tier1.length >= 3) {
    return {
      hero: tier1[0],
      featured: tier1.slice(1, Math.min(5, tier1.length)),
      sidebar: [
        ...tier1.slice(Math.min(5, tier1.length)),
        ...tier2.slice(0, 8),
      ],
      additional: [...tier2.slice(8), ...tier3.slice(0, 10)],
      fallbackUsed: false,
      contentStrategy: 'tier-1-sparse',
    };
  }

  // Strategy 3: Very limited content (< 3 Tier 1 articles)
  // Promote Tier 2 to featured positions
  const allContent = [...tier1, ...tier2, ...tier3];

  if (allContent.length === 0) {
    return {
      hero: null,
      featured: [],
      sidebar: [],
      additional: [],
      fallbackUsed: false,
      contentStrategy: 'tier-empty',
    };
  }

  return {
    hero: allContent[0] || null,
    featured: allContent.slice(1, 5),
    sidebar: allContent.slice(5, 13),
    additional: allContent.slice(13, 25),
    fallbackUsed: true,
    contentStrategy: 'tier-1-sparse',
  };
};
```

### **Phase 2: Category Page Component (Day 1 Afternoon)**

#### 2.1 Route Structure

**File**: `src/app/(frontend)/categories/[slug]/page.tsx`

```typescript
import { Suspense } from 'react';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getCachedCategoryArticles } from '@/lib/cache/categories';
import CategoryPageLayout from '@/components/categories/CategoryPageLayout';
import CategoryAccessibilityNav from '@/components/categories/CategoryAccessibilityNav';
import CategoryPageSkeleton from '@/components/categories/CategoryPageSkeleton';
import { getCachedCategorySlugs } from '@/lib/cache/categories';

interface CategoryPageProps {
  params: Promise<{ slug: string }>;
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const { slug } = await params;

  try {
    const getCachedData = getCachedCategoryArticles(slug);
    const { categoryInfo, totalArticles } = await getCachedData();

    const title = categoryInfo.title;
    const description = `Aktuelle ${title}-Nachrichten und Analysen. ${totalArticles} Artikel verfügbar.`;

    return {
      title: `${title} | Börsen Blick`,
      description,
      openGraph: {
        title: `${title} - Börsen Blick`,
        description,
        type: 'website',
      },
      twitter: {
        card: 'summary_large_image',
        title: `${title} - Börsen Blick`,
        description,
      },
    };
  } catch (error) {
    return {
      title: 'Category Not Found | Börsen Blick',
      description: 'The requested category could not be found.',
    };
  }
}

export async function generateStaticParams(): Promise<{ slug: string }[]> {
  try {
    return await getCachedCategorySlugs();
  } catch (error) {
    console.error('Error generating category static params:', error);
    return [];
  }
}

export const revalidate = 300; // 5 minutes ISR

export default async function CategoryPage({ params }: CategoryPageProps) {
  const { slug } = await params;

  // Validate category exists by attempting to fetch
  try {
    const getCachedData = getCachedCategoryArticles(slug);
    await getCachedData(); // Just check if category exists
  } catch (error) {
    console.error(`Category not found: ${slug}`, error);
    notFound();
  }

  return (
    <div className="min-h-dvh bg-background">
      <CategoryAccessibilityNav categorySlug={slug} />

      <main
        id="main-content"
        className="focus:outline-none"
        tabIndex={-1}
        role="main"
        aria-label={`Category page`}
      >
        <h1 className="sr-only">Category Articles</h1>

        <Suspense fallback={<CategoryPageSkeleton />}>
          <CategoryPageLayout categorySlug={slug} />
        </Suspense>
      </main>
    </div>
  );
}
```

#### 2.2 Main Layout Component

**File**: `src/components/categories/CategoryPageLayout.tsx`

```typescript
import { getCachedCategoryArticles, organizeCategoryLayout } from '@/lib/cache/categories';
import CategoryHeroSection from './CategoryHeroSection';
import CategoryFeaturedSection from './CategoryFeaturedSection';
import CategorySidebar from './CategorySidebar';
import CategoryAdditionalSection from './CategoryAdditionalSection';
import CategoryEmptyState from './CategoryEmptyState';

interface CategoryPageLayoutProps {
  categorySlug: string;
}

export default async function CategoryPageLayout({
  categorySlug
}: CategoryPageLayoutProps) {
  try {
    const getCachedData = getCachedCategoryArticles(categorySlug);
    const categoryData = await getCachedData();
    const layout = organizeCategoryLayout(categoryData);

    if (layout.contentStrategy === 'tier-empty') {
      return <CategoryEmptyState category={categoryData.categoryInfo} />;
    }

    return (
      <div className="max-w-[1440px] mx-auto px-4 py-6 sm:py-8 md:py-10 lg:py-12">
        {/* Category Header */}
        <div className="mb-6 md:mb-8 lg:mb-10">
          <h1 className="font-serif text-2xl/8 md:text-3xl/9 lg:text-4xl/10 font-normal text-foreground mb-2">
            {categoryData.categoryInfo.title}
          </h1>
          <p className="font-sans text-sm text-muted-foreground">
            {categoryData.totalArticles} Artikel verfügbar
          </p>
          {layout.fallbackUsed && (
            <div className="mt-2 text-xs text-amber-600 dark:text-amber-400">
              Erweiterte Inhalte angezeigt
            </div>
          )}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">

          {/* Left: Hero + Featured Articles */}
          <section
            className="lg:col-span-2 xl:col-span-3 space-y-6 md:space-y-8"
            aria-labelledby="featured-content-heading"
          >
            <h2 id="featured-content-heading" className="sr-only">
              Featured Articles
            </h2>

            {/* Hero Article */}
            {layout.hero && (
              <CategoryHeroSection
                article={layout.hero}
                categoryInfo={categoryData.categoryInfo}
              />
            )}

            {/* Featured Grid */}
            {layout.featured.length > 0 && (
              <CategoryFeaturedSection
                articles={layout.featured}
                categoryInfo={categoryData.categoryInfo}
              />
            )}

            {/* Additional Content (Bottom Section) */}
            {layout.additional.length > 0 && (
              <CategoryAdditionalSection
                articles={layout.additional}
                categoryInfo={categoryData.categoryInfo}
              />
            )}
          </section>

          {/* Right: Sidebar */}
          <aside
            className="lg:col-span-1 xl:col-span-1"
            aria-labelledby="sidebar-content-heading"
          >
            <h2 id="sidebar-content-heading" className="sr-only">
              Additional Articles
            </h2>
            <CategorySidebar
              articles={layout.sidebar}
              categoryInfo={categoryData.categoryInfo}
            />
          </aside>

        </div>
      </div>
    );
  } catch (error) {
    console.error('Error loading category layout:', error);

    return (
      <div className="max-w-[1440px] mx-auto px-4 py-12">
        <div className="text-center">
          <div className="text-muted-foreground">
            <span className="size-4 mx-auto mb-3 block">⚠️</span>
            <p className="text-sm mb-2">Fehler beim Laden der Kategorie</p>
            <p className="text-xs">Schauen Sie später noch einmal vorbei</p>
          </div>
        </div>
      </div>
    );
  }
}
```

### **Phase 3: Individual Section Components (Day 2)**

#### 3.1 Hero Section Component

**File**: `src/components/categories/CategoryHeroSection.tsx`

```typescript
import NewsCard from '@/components/NewsCard';
import type { Article, Category } from '@/payload-types';

interface CategoryHeroSectionProps {
  article: Article;
  categoryInfo: Category;
}

export default function CategoryHeroSection({
  article,
  categoryInfo
}: CategoryHeroSectionProps) {
  return (
    <section className="mb-6 md:mb-8">
      <NewsCard
        article={article}
        variant="default"
        showDescription={true}
        priority={true}
        locale="de"
        className="shadow-sm border-2 border-gray-100 dark:border-gray-800"
      />

      {article.pinned && (
        <div className="mt-3 flex items-center gap-2 text-xs text-muted-foreground">
          <span className="size-2 bg-blue-500 rounded-full animate-pulse" />
          <span>Wichtiger {categoryInfo.title}-Artikel</span>
        </div>
      )}
    </section>
  );
}
```

#### 3.2 Featured Grid Component

**File**: `src/components/categories/CategoryFeaturedSection.tsx`

```typescript
import NewsCard from '@/components/NewsCard';
import type { Article, Category } from '@/payload-types';

interface CategoryFeaturedSectionProps {
  articles: Article[];
  categoryInfo: Category;
}

export default function CategoryFeaturedSection({
  articles,
  categoryInfo
}: CategoryFeaturedSectionProps) {
  if (articles.length === 0) return null;

  return (
    <section
      className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6"
      aria-labelledby="featured-grid-heading"
    >
      <h3 id="featured-grid-heading" className="sr-only">
        Featured {categoryInfo.title} Articles
      </h3>

      {articles.map((article, index) => (
        <NewsCard
          key={article.id}
          article={article}
          variant="default"
          showDescription={true}
          locale="de"
          priority={index < 2} // Priority for first 2 articles
          className="shadow-sm"
        />
      ))}
    </section>
  );
}
```

#### 3.3 Sidebar Component

**File**: `src/components/categories/CategorySidebar.tsx`

```typescript
import NewsCard from '@/components/NewsCard';
import type { Article, Category } from '@/payload-types';

interface CategorySidebarProps {
  articles: Article[];
  categoryInfo: Category;
}

export default function CategorySidebar({
  articles,
  categoryInfo
}: CategorySidebarProps) {
  if (articles.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-muted-foreground">
          <span className="size-4 mx-auto mb-3 block text-lg">📄</span>
          <p className="text-xs mb-2">Keine weiteren {categoryInfo.title}-Artikel</p>
          <p className="text-xs">Schauen Sie später noch einmal vorbei</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-1.5 md:space-y-2">
      {articles.map(article => (
        <NewsCard
          key={article.id}
          article={article}
          variant="title-only"
          showDescription={true}
          locale="de"
        />
      ))}

      {articles.length >= 8 && (
        <div className="pt-3 text-center">
          <p className="text-xs text-muted-foreground">
            Mehr {categoryInfo.title}-Artikel verfügbar
          </p>
        </div>
      )}
    </div>
  );
}
```

## 🎯 **INTELLIGENT FALLBACK STRATEGIES**

### **Content Scarcity Handling**

```typescript
// Strategy 1: Cross-tier promotion
if (tier1.length < 3) {
  // Promote Tier 2 articles to featured positions
  const promotedArticles = tier2.slice(0, 5 - tier1.length);
  featuredArticles = [...tier1, ...promotedArticles];
}

// Strategy 2: Related category suggestions
if (totalArticles < 10) {
  // Show "Related Categories" section
  const relatedCategories = await getRelatedCategories(categorySlug);
  return <CategoryWithSuggestions />;
}

// Strategy 3: Recent articles fallback
if (totalArticles === 0) {
  // Show recent articles from all categories with category filter
  const recentGlobal = await getRecentArticlesGlobal(limit: 10);
  return <CategoryEmptyWithGlobal />;
}
```

## ✅ **COMPLIANCE VERIFICATION**

### **📋 Frontend Page Setup Guidelines Compliance**

| Requirement                   | Status | Implementation                                   |
| ----------------------------- | ------ | ------------------------------------------------ |
| **5-minute cache duration**   | ✅     | `revalidate: 300` across all cache functions     |
| **Suspense boundaries**       | ✅     | `<Suspense fallback={<CategoryPageSkeleton />}>` |
| **German error messages**     | ✅     | "Fehler beim Laden der Kategorie"                |
| **4-column responsive grid**  | ✅     | Modified to category-appropriate 3-4 column grid |
| **Accessibility compliance**  | ✅     | Skip links, ARIA labels, semantic HTML           |
| **SEO metadata**              | ✅     | Dynamic metadata with category-specific titles   |
| **German-first localization** | ✅     | Content hierarchy maintained                     |

### **📋 PayloadCMS Native Patterns Compliance**

| Requirement                  | Status | Implementation                             |
| ---------------------------- | ------ | ------------------------------------------ |
| **PayloadCMS Local API**     | ✅     | `payload.find()` with proper relationships |
| **Native sorting patterns**  | ✅     | `['-pinned', '-publishedAt']` maintained   |
| **Proper depth handling**    | ✅     | `depth: 2` for category relationships      |
| **Framework cache patterns** | ✅     | `unstable_cache` with proper tagging       |
| **No custom bypasses**       | ✅     | Uses native PayloadCMS queries only        |

### **📋 Tailwind v4 Compliance**

| Requirement                | Status | Implementation                      |
| -------------------------- | ------ | ----------------------------------- |
| **Use `gap-*` spacing**    | ✅     | `gap-4 md:gap-6` throughout layouts |
| **Line-height modifiers**  | ✅     | `text-2xl/8`, `text-3xl/9` patterns |
| **Modern opacity syntax**  | ✅     | No deprecated opacity utilities     |
| **`min-h-dvh` usage**      | ✅     | Mobile-safe viewport heights        |
| **Typography consistency** | ✅     | Serif for content, sans for UI      |

## 🚀 **DEPLOYMENT STRATEGY**

### **Phase 1 Rollout (Day 3)**

- Deploy unified category template
- Test with existing categories
- Monitor performance metrics
- Validate cache behavior

### **Phase 2 Enhancement (Future)**

- Per-category customizations
- Enhanced filtering options
- Category-specific widgets
- Advanced pagination

### **Monitoring & Analytics**

- Track category page engagement
- Monitor tier content distribution
- Cache hit/miss ratios
- Category navigation patterns

## 📊 **SUCCESS METRICS**

### **Technical Performance**

- **Page Load Time**: < 2.5s for category pages
- **Cache Hit Ratio**: > 80% for category content
- **Error Rate**: < 1% for category loading

### **User Experience**

- **Accessibility Score**: 100% Lighthouse
- **Mobile Responsiveness**: Perfect across devices
- **German Localization**: Complete coverage

### **Content Distribution**

- **Tier Balance**: Proper hierarchy maintained per category
- **Fallback Usage**: < 20% of category pages need fallbacks
- **Content Freshness**: 5-minute cache ensures up-to-date content

---

## 🎯 **NEXT STEPS**

1. **Create base route structure** (`/categories/[slug]/page.tsx`)
2. **Implement category caching utilities** (cache functions)
3. **Build layout organization logic** (tier distribution)
4. **Create individual section components** (hero, featured, sidebar)
5. **Add accessibility navigation** (skip links)
6. **Implement error handling** (German messages)
7. **Test with multiple categories** (validation)
8. **Deploy and monitor** (performance tracking)

**Total Estimated Time**: **2-3 days**  
**Framework Compliance**: **100%** targeting  
**User Experience**: **Production-ready** quality expected

The unified category template will provide **intelligent tier-based content distribution** while maintaining **framework best practices** and **exceptional user experience** across all category pages. 🎯
