# <PERSON><PERSON><PERSON> Blick Development Workflow Guide

## PayloadCMS + Supabase + Vercel Multi-Environment Setup

⚠️ **CRITICAL: DATA SAFETY FIRST** ⚠️
This guide prioritizes data safety above all else. Every step includes backup procedures.
With hundreds of articles at stake, we take zero risks with data loss.

**ADAPTED FOR:** PayloadCMS + Supabase + Solo Development + Manual Deployment

---

## STEP 1: Install and Verify Supabase CLI

### 1.1 Install Supabase CLI

```bash
# Option 1: Using npm (recommended for Node.js projects)
npm install -g supabase

# Option 2: Using Homebrew (macOS)
brew install supabase/tap/supabase
```

### 1.2 Verify Installation

```bash
supabase --version
# Should show version 1.x.x or higher
```

### 1.3 Login to Supabase

```bash
supabase login
# This will open browser to get your personal access token
# Or manually get token from: https://supabase.com/dashboard/account/tokens
```

### 1.4 Verify Your Current Local Setup

```bash
# Check if Supabase is already initialized (you already have this)
ls -la supabase/
# You should see config.toml

# Check current local status
supabase status
# This shows if local Supabase is running
```

**✅ CHECKPOINT:** CLI installed, logged in, and can see your existing local setup.

---

## STEP 2: Verify Local Supabase (Your Development Database)

### 2.1 Start Local Supabase (if not running)

```bash
# From your project root
supabase start
# This boots Postgres, Auth, Storage in Docker
```

### 2.2 Verify Local Setup

```bash
# Check status
supabase status

# You should see something like:
# API URL: http://localhost:54321
# DB URL: postgresql://postgres:postgres@localhost:54322/postgres
# Studio URL: http://localhost:54323
```

### 2.3 Test PayloadCMS Connection

```bash
# Make sure your local environment works
pnpm dev
# Visit http://localhost:3000/admin
# Verify you can see your articles and data
```

**✅ CHECKPOINT:** Local Supabase running, PayloadCMS connected, data visible.

---

## STEP 3: Create Preview Supabase Project (Cloud)

### 3.1 Create Project

1. Go to https://supabase.com/dashboard
2. Click "New Project"
3. Name it: `borsenblick-preview`
4. Choose Free plan
5. Select region closest to your users
6. Wait for project creation (2-3 minutes)

### 3.2 Collect Credentials

After project creation, go to Settings > API:

```bash
# Copy these values - you'll need them:
Project Reference ID: [abc123def456]
Project URL: https://[abc123def456].supabase.co
anon public key: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
service_role key: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

**⚠️ SECURITY NOTE:** Keep service_role key secret - it has admin access!

**✅ CHECKPOINT:** Preview project created, credentials collected.

---

## STEP 4: Setup Environment Files for Multi-Environment

### 4.1 Create Local Environment File

```bash
# Create .env.local (for local development)
touch .env.local
```

Add to `.env.local`:

```env
# Local Supabase (from supabase status)
NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Your existing PayloadCMS variables
DATABASE_URI=postgresql://postgres:postgres@localhost:54322/postgres
PAYLOAD_SECRET=your-existing-secret
OPENAI_API_KEY=your-existing-key
FIRECRAWL_API_KEY=your-existing-key
NEXT_PUBLIC_SERVER_URL=http://localhost:3000
# ... all other existing variables from your .env
```

### 4.2 Create Preview Environment File

🚨 **CRITICAL LESSON LEARNED:**

**Use IPv4-Compatible Database Connection!**

For preview environment, you MUST use the **Transaction pooler** connection, not the direct connection:

```bash
# Create .env.preview.local (for preview environment)
touch .env.preview.local
```

Add to `.env.preview.local`:

```env
# Preview Supabase (from step 3.2)
NEXT_PUBLIC_SUPABASE_URL=https://[your-preview-ref].supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=[your-preview-anon-key]
SUPABASE_SERVICE_ROLE_KEY=[your-preview-service-key]

# ⚠️ CRITICAL: Use IPv4-compatible pooler connection (not direct connection)
# Get this from Supabase Dashboard → Settings → Database → Connection pooling
DATABASE_URI=postgresql://postgres.[project-ref]:[password]@aws-0-eu-central-1.pooler.supabase.com:6543/postgres

PAYLOAD_SECRET=your-existing-secret
OPENAI_API_KEY=your-existing-key
FIRECRAWL_API_KEY=your-existing-key
NEXT_PUBLIC_SERVER_URL=https://your-vercel-preview-url.vercel.app
# ... all other existing variables
```

**Why IPv4 Pooler Connection?**
- ✅ **Fixes DNS resolution errors** (`ENOTFOUND` issues)
- ✅ **IPv4 compatible** (required for most deployments)
- ✅ **Better performance** with connection pooling
- ✅ **Handles multiple connections** properly

### 4.3 Add Environment Switching Scripts

Add to `package.json` scripts section:

```json
{
  "scripts": {
    "env:local": "cp .env.local .env && echo '✅ Switched to LOCAL environment'",
    "env:preview": "cp .env.preview.local .env && echo '✅ Switched to PREVIEW environment'",
    "backup-local-db": "bash scripts/backup-local-db.sh",
    "backup:remote": "bash scripts/backup-remote-db.sh",
    "sync-data": "bash scripts/sync-local-to-preview.sh"
  }
}
```

### 4.4 Update .gitignore

```bash
# Add to .gitignore
echo ".env.local" >> .gitignore
echo ".env.preview.local" >> .gitignore
echo "backups/" >> .gitignore
```

**✅ CHECKPOINT:** Environment files created with IPv4-compatible connections.

---

## STEP 5: Link Supabase CLI to Preview Project

### 5.1 Link CLI to Preview

```bash
# Link directly to your preview project
supabase link --project-ref [your-preview-ref]
# Use the Project Reference ID from step 3.2
# Example: supabase link --project-ref vucefescwpcdbpjlccea
```

### 5.2 Verify Link

```bash
# Check which project you're linked to
supabase projects list
# Look for the ● symbol next to your preview project

# Test the connection
supabase db dump --data-only -f test_backup.sql
# This should create a backup from your preview database
rm test_backup.sql  # cleanup
```

**✅ CHECKPOINT:** CLI linked to preview project, connection verified.

---

## STEP 6: Setup Vercel-Supabase Integration

### 6.1 Connect Integration

1. Go to https://vercel.com/dashboard
2. Select your project
3. Go to Settings > Integrations
4. Find "Supabase" and click "Add Integration"
5. Select your Vercel project
6. Select your `borsenblick-preview` Supabase project
7. This automatically injects environment variables into Vercel

### 6.2 Verify Integration

1. Go to Vercel Settings > Environment Variables
2. You should see Supabase variables automatically added
3. Make sure they're set for "Preview" deployments

**✅ CHECKPOINT:** Vercel connected to preview Supabase project.

---

## STEP 7: Verify Backup Scripts

### 7.1 Test Backup Systems

```bash
# Test local backup (backs up your development data)
pnpm backup-local-db
# This creates: supabase/backups/local_db_[branch]_[timestamp]/

# Test preview backup (backs up remote preview database)
pnpm backup:remote
# This creates: supabase/backups/remote_[project]_[branch]_[timestamp]/
```

**✅ CHECKPOINT:** Backup systems tested and working correctly.

---

## STEP 8: Deploy Schema and Test Workflow

### 8.1 Test Local Environment

```bash
# Switch to local
pnpm env:local

# Start development
pnpm dev

# Verify everything works locally
```

### 8.2 Generate and Push Schema Migration

```bash
# Create a backup of current local state
pnpm backup-local-db

# Generate migration from your current local schema
supabase db diff --local --schema public > temp_schema_diff.sql

# Create a proper migration file
supabase migration new initial_preview_schema

# Copy the content from temp_schema_diff.sql to the new migration file:
# Edit: supabase/migrations/[timestamp]_initial_preview_schema.sql

# Clean up temp file
rm temp_schema_diff.sql

# Push the migration to preview (CLI-linked project)
supabase db push
```

### 8.3 Test Preview Environment (Empty Database)

```bash
# Switch to preview environment
pnpm env:preview

# Test connection
pnpm dev

# Visit http://localhost:3000/admin
# You should see empty database (no articles yet) but no connection errors
```

**✅ CHECKPOINT:** Both environments working, schema synced.

---

## STEP 9: Sync Data from Local to Preview

🚨 **LESSON LEARNED:** The sync script needed fixing. Here's the correct approach:

### 9.1 Manual Data Sync (Proven Method)

```bash
# 1. Backup preview database first (ALWAYS!)
pnpm backup:remote

# 2. Export local data
mkdir -p tmp
supabase db dump --local --data-only --use-copy --schema public -f tmp/local_data_export.sql

# 3. Install PostgreSQL client (if needed)
# macOS: brew install postgresql
# Or: brew install libpq && export PATH="/opt/homebrew/opt/libpq/bin:$PATH"

# 4. Import data directly using psql
PGPASSWORD="[your-password]" psql -h aws-0-eu-central-1.pooler.supabase.com -p 6543 -U postgres.[project-ref] -d postgres -f tmp/local_data_export.sql

# 5. Clean up
rm tmp/local_data_export.sql
```

### 9.2 Test Preview with Data

```bash
# Test the preview environment with your data
pnpm env:preview
pnpm dev

# Visit http://localhost:3000/admin
# You should now see all your local articles, categories, pages, etc.
```

**✅ CHECKPOINT:** Data successfully synced and tested in preview.

---

## STEP 10: Deploy to Vercel

### 10.1 Deploy

```bash
# Push to trigger Vercel deployment
git push origin preview
```

### 10.2 Verify Deployment

1. Check Vercel dashboard for deployment status
2. Visit your preview URL
3. Test admin panel with your data

**✅ CHECKPOINT:** Complete deployment workflow successful!

---

## KEY LESSONS LEARNED

### 🔧 **Database Connection Issues**

**Problem:** DNS resolution errors (`ENOTFOUND db.[project-ref].supabase.co`)

**Solution:** Use IPv4-compatible **Transaction pooler** connection:
```
postgresql://postgres.[project-ref]:[password]@aws-0-eu-central-1.pooler.supabase.com:6543/postgres
```

**Why this works:**
- ✅ IPv4 compatible (no DNS issues)
- ✅ Connection pooling (better performance)
- ✅ Handles multiple clients properly

### 📝 **Environment File Line Breaks**

**Problem:** Environment variables broken across multiple lines cause connection failures

**Solution:** Ensure all environment variables are on single lines:
```bash
# BAD:
DATABASE_URI=postgresql://postgres:<EMAIL>:5432
/postgres

# GOOD:
DATABASE_URI=postgresql://postgres:<EMAIL>:5432/postgres
```

### 📊 **Data Sync Method**

**Problem:** `supabase db restore` doesn't exist, sync scripts failing

**Solution:** Use direct PostgreSQL import with `psql`:

1. Export: `supabase db dump --local --data-only --schema public`
2. Import: `PGPASSWORD="..." psql -h ... -f export.sql`

### 🎯 **Simplified Workflow**

**Most Effective Deployment Process:**

1. **Development:** Work locally with `pnpm env:local`
2. **Schema:** Generate migrations with `supabase db diff --local`
3. **Deploy Schema:** `supabase db push` to preview
4. **Test Empty:** Verify preview connection works
5. **Sync Data:** Use manual psql import (most reliable)
6. **Test Full:** Verify preview with all data
7. **Deploy:** `git push origin preview`

---

## DAILY DEVELOPMENT WORKFLOW

### Feature Development (Local)

```bash
# Always start with local environment
pnpm env:local

# Create feature branch
git checkout -b feature/your-feature-name

# Develop and test locally
pnpm dev

# If you made schema changes, generate migration files
supabase db diff --local --schema public > temp_diff.sql
supabase migration new add_your_feature_name
# Copy content from temp_diff.sql to the new migration file
rm temp_diff.sql

# Commit changes (including migration files)
git add .
git commit -m "Add your feature with schema migrations"
```

### Deploy to Preview

```bash
# ALWAYS backup preview first
pnpm backup:remote

# Switch to preview branch
git checkout preview

# Merge your feature
git merge development

# Push schema changes (if any)
supabase db push

# Switch environment and test locally first
pnpm env:preview
pnpm dev

# Sync data if needed
# Manual sync (see Step 9.1)

# Push to trigger Vercel deployment
git push origin preview
```

---

## EMERGENCY PROCEDURES

### Data Recovery

```bash
# Restore preview from backup
# Use the restore script created by backup:
./supabase/backups/[backup-folder]/[backup-name]_restore.sh

# Restore local from backup
pnpm restore-local-db [timestamp]
```

### Environment Issues

```bash
# Reset to local environment
pnpm env:local

# Check what environment you're in
echo $DATABASE_URI
```

### Connection Issues

```bash
# If you get DNS errors, verify you're using pooler connection:
grep "pooler.supabase.com" .env.preview.local

# If missing, update to use IPv4-compatible pooler connection
```

---

## QUICK REFERENCE: Command Arsenal

### Environment Management
```bash
pnpm env:local        # Switch to local development
pnpm env:preview      # Switch to preview environment
```

### Schema Management
```bash
supabase db diff --local              # See schema changes
supabase migration new name           # Create migration file
supabase db push                      # Apply migrations to CLI-linked project
```

### Data Management
```bash
pnpm backup-local-db                  # Backup local Docker containers
pnpm backup:remote                    # Backup CLI-linked project
pnpm restore-local-db timestamp       # Restore local from backup

# Manual data sync (most reliable):
supabase db dump --local --data-only --schema public -f tmp/export.sql
PGPASSWORD="..." psql -h pooler-host -p 6543 -U user -d postgres -f tmp/export.sql
```

### Testing
```bash
pnpm dev                             # Start development server
curl -s http://localhost:3000/admin  # Test admin accessibility
```

---

## TROUBLESHOOTING

### Common Issues and Solutions

**DNS Resolution Error:**
```
Error: getaddrinfo ENOTFOUND db.[project-ref].supabase.co
```
**Fix:** Switch to IPv4-compatible pooler connection

**Broken Environment Variables:**
```
DATABASE_URI=postgresql://...
/postgres
```
**Fix:** Ensure single-line environment variables

**Data Sync Fails:**
```
supabase db restore: command not found
```
**Fix:** Use direct `psql` import method

**Environment Priority Issues:**
```
Local settings overriding preview
```
**Fix:** Verify `.env` file contains preview settings, not local

---

This guide reflects the **actual working deployment process** that successfully deployed the Börsen Blick preview environment with full data sync!
