# PayloadCMS Collection APIs & Authentication Best Practices

## Overview

This document outlines the established patterns and best practices for implementing PayloadCMS collections in Börsen Blick, covering authentication, API access control, and security standards.

## 🔐 Access Control Patterns

### Core Access Control Functions

**Location:** `src/access/`

#### 1. `authenticated` - Strict Authentication Required

```typescript
// src/access/authenticated.ts
import type { Access } from 'payload';

export const authenticated: Access = ({ req: { user } }) => <PERSON><PERSON><PERSON>(user);
```

**Use Case:** Admin-only operations (create, update, delete content)

#### 2. `authenticatedOrPublished` - Hybrid Access

```typescript
// src/access/authenticatedOrPublished.ts
import type { Access } from 'payload';

export const authenticatedOrPublished: Access = ({ req: { user } }) => {
  if (user) return true;

  return {
    _status: { equals: 'published' },
  };
};
```

**Use Case:** Content that should be public when published, private when draft

### Access Control Matrix

| Collection     | Create          | Read                       | Update          | Delete          | Rationale                               |
| -------------- | --------------- | -------------------------- | --------------- | --------------- | --------------------------------------- |
| **Articles**   | `authenticated` | `authenticatedOrPublished` | `authenticated` | `authenticated` | Draft protection, public when published |
| **Categories** | `authenticated` | `() => true`               | `authenticated` | `authenticated` | Public categories, admin-managed        |
| **Users**      | `authenticated` | `authenticated`            | `authenticated` | `authenticated` | Full privacy protection                 |
| **Keywords**   | `authenticated` | `() => true`               | `authenticated` | `authenticated` | Public for RSS processing               |
| **RSS Feeds**  | `authenticated` | `authenticated`            | `authenticated` | `authenticated` | Configuration data - admin only         |
| **Media**      | `authenticated` | `() => true`               | `authenticated` | `authenticated` | Public assets, admin-managed            |

## 📋 Collection Configuration Standards

### 1. Articles Collection Pattern (Content with Workflow)

```typescript
export const Articles: CollectionConfig = {
  slug: 'articles',
  access: {
    create: authenticated,
    read: authenticatedOrPublished, // 🔑 Hybrid access for draft protection
    update: authenticated,
    delete: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'articleType', '_status', 'updatedAt'],
    group: 'Content',
    // Preview configuration for draft functionality
    preview: ({ slug, collection }) => {
      if (!slug) return null;

      const params = new URLSearchParams({
        slug: String(slug),
        collection: 'articles',
        path: `/articles/${String(slug)}`,
        previewSecret: process.env.PAYLOAD_PUBLIC_DRAFT_SECRET || '',
      });

      return `/api/preview?${params.toString()}`;
    },
  },
  // Enable drafts for content workflow
  versions: {
    drafts: true,
  },
  fields: [
    // Field definitions...
  ],
};
```

**Key Features:**

- ✅ Draft/publish workflow with `_status` field
- ✅ Preview functionality for drafts
- ✅ Hybrid access control
- ✅ Versioning enabled

### 2. Configuration Collection Pattern (Admin-Only)

```typescript
export const RSSFeeds: CollectionConfig = {
  slug: 'rss-feeds',
  access: {
    create: authenticated,
    read: authenticated, // 🔒 Admin-only access
    update: authenticated,
    delete: authenticated,
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'url', 'isActive', 'lastChecked'],
    group: 'Configuration', // 📁 Logical grouping
  },
  fields: [
    // Field definitions...
  ],
};
```

**Key Features:**

- 🔒 Complete authentication requirement
- 📁 Grouped under "Configuration"
- 🛡️ Protected from public access

### 3. Reference Data Collection Pattern (Public Read)

```typescript
export const Categories: CollectionConfig = {
  slug: 'categories',
  access: {
    create: authenticated,
    read: () => true, // 🌐 Public read access
    update: authenticated,
    delete: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'slug', 'updatedAt'],
    group: 'Content',
  },
  fields: [
    // Field definitions...
  ],
};
```

**Key Features:**

- 🌐 Public read access for frontend consumption
- 🔒 Admin-controlled modifications
- 📱 Frontend-friendly for navigation/filtering

## 🛡️ Security Best Practices

### 1. Field-Level Security

```typescript
{
  name: 'adminNotes',
  type: 'textarea',
  access: {
    read: authenticated,  // Only admins can read admin notes
    update: authenticated,
  },
  admin: {
    description: 'Internal notes - not visible to public',
  },
}
```

### 2. Sensitive Data Protection

```typescript
{
  name: 'apiKey',
  type: 'text',
  access: {
    read: ({ req: { user } }) => Boolean(user?.role === 'admin'),  // Role-based access
    update: ({ req: { user } }) => Boolean(user?.role === 'admin'),
  },
  admin: {
    components: {
      Field: () => '••••••••', // Mask sensitive values
    },
  },
}
```

### 3. Audit Trail Fields

```typescript
// Always include audit fields for important collections
{
  name: 'publishedBy',
  type: 'relationship',
  relationTo: 'users',
  admin: {
    readOnly: true,
    position: 'sidebar',
  },
},
{
  name: 'publishedAt',
  type: 'date',
  admin: {
    readOnly: true,
    position: 'sidebar',
  },
}
```

## 🔄 API Usage Patterns

### 1. Public API Access (Frontend)

```typescript
// ✅ Correct: Public data access
const categories = await fetch('/api/categories');
const publishedArticles = await fetch(
  '/api/articles?where[_status][equals]=published'
);

// ❌ Incorrect: Will fail due to access control
const draftArticles = await fetch('/api/articles?where[_status][equals]=draft');
```

### 2. Authenticated API Access (Admin/Server)

```typescript
// Server-side with PayloadCMS instance
const payload = await getPayload({ config });

const articles = await payload.find({
  collection: 'articles',
  draft: true, // Can access drafts when authenticated
  limit: 10,
});

// API with authentication
const response = await fetch('/api/articles', {
  headers: {
    Authorization: `Bearer ${token}`,
  },
});
```

### 3. Draft Content Access

```typescript
// ✅ Admin interface: Always has access to drafts
const adminArticles = await payload.find({
  collection: 'articles',
  draft: true,
});

// ✅ Frontend: Only published content
const publicArticles = await payload.find({
  collection: 'articles',
  where: {
    _status: { equals: 'published' },
  },
});
```

## 📊 Collection Types & Patterns

### Content Collections

**Characteristics:**

- Have draft/publish workflow (`_status` field)
- Use `authenticatedOrPublished` for read access
- Enable versioning and drafts
- Include preview functionality

**Examples:** Articles, Pages

### Reference Collections

**Characteristics:**

- Public read access (`() => true`)
- Admin-controlled modifications
- No draft workflow needed
- Used for navigation/filtering

**Examples:** Categories, Keywords

### Configuration Collections

**Characteristics:**

- Full authentication required
- Admin-only access
- Contain system configuration
- No public exposure needed

**Examples:** RSS Feeds, Users, Settings

### Media Collections

**Characteristics:**

- Public read for asset delivery
- Admin-controlled uploads
- File-based with upload validation
- CDN/optimization support

**Examples:** Media uploads, Images

## 🧪 Testing Access Control

### 1. Unit Test Pattern

```typescript
// src/__tests__/access/articles.test.ts
import { authenticatedOrPublished } from '@/access/authenticatedOrPublished';

describe('Articles Access Control', () => {
  it('should allow authenticated users to read drafts', async () => {
    const result = authenticatedOrPublished({
      req: { user: { id: '1' } },
    } as any);
    expect(result).toBe(true);
  });

  it('should only allow published content for unauthenticated users', async () => {
    const result = authenticatedOrPublished({
      req: { user: null },
    } as any);
    expect(result).toEqual({
      _status: { equals: 'published' },
    });
  });
});
```

### 2. Integration Test Pattern

```typescript
// Test API endpoints respect access control
describe('Articles API', () => {
  it('should not return draft articles to unauthenticated requests', async () => {
    const response = await fetch('/api/articles');
    const data = await response.json();

    // Should only contain published articles
    expect(data.docs.every(doc => doc._status === 'published')).toBe(true);
  });
});
```

## 🚀 Performance Considerations

### 1. Caching Strategy

```typescript
// Cache public data aggressively
export const getCachedCategories = unstable_cache(
  async () => {
    const payload = await getPayload({ config });
    return await payload.find({
      collection: 'categories',
      limit: 1000,
    });
  },
  ['categories'],
  {
    revalidate: 3600, // 1 hour
    tags: ['categories'],
  }
);

// Don't cache authenticated/draft content
export const getArticlesForAdmin = async (req: PayloadRequest) => {
  return await payload.find({
    collection: 'articles',
    draft: true, // No caching for draft content
  });
};
```

### 2. Query Optimization

```typescript
// ✅ Efficient: Use specific fields
const articles = await payload.find({
  collection: 'articles',
  select: {
    title: true,
    slug: true,
    _status: true,
  },
  where: {
    _status: { equals: 'published' },
  },
});

// ❌ Inefficient: Loading all data including rich content
const articles = await payload.find({
  collection: 'articles',
  depth: 5, // Avoid deep population unless needed
});
```

## 📋 Checklist for New Collections

### Before Implementation

- [ ] Define access control requirements
- [ ] Identify if content needs draft/publish workflow
- [ ] Determine public vs admin-only access
- [ ] Plan field-level security needs
- [ ] Consider caching strategy

### During Implementation

- [ ] Apply appropriate access control functions
- [ ] Configure admin interface properly
- [ ] Add audit trail fields where needed
- [ ] Implement preview functionality if drafts are used
- [ ] Add proper field validation

### After Implementation

- [ ] Test access control with authenticated/unauthenticated requests
- [ ] Verify API endpoints respect security rules
- [ ] Test draft/publish workflow if applicable
- [ ] Document collection purpose and usage
- [ ] Add monitoring for security violations

## 🛠️ Troubleshooting Common Issues

### "No Articles Visible in API"

**Cause:** Articles are in draft status, API call is unauthenticated  
**Solution:** Use admin interface or authenticate API calls for draft content

### "Access Denied Errors"

**Cause:** Insufficient permissions for operation  
**Solution:** Verify user authentication and access control configuration

### "Inconsistent Data Access"

**Cause:** Mixed access patterns or caching issues  
**Solution:** Review access control logic, check cache invalidation

### "Performance Issues with Large Collections"

**Cause:** Over-fetching data or missing indexes  
**Solution:** Optimize queries, add database indexes, implement proper caching

---

## 📚 Related Documentation

- [PayloadCMS Access Control](https://payloadcms.com/docs/access-control/overview)
- [Draft Preview Implementation Plan](.ai/04-Doing/draft-preview-plan.md)
- [Payload CMS Field Configuration](.ai/06-Docs/payload/payload-cms-guide.md)

---

> **Security First:** Always default to the most restrictive access level and explicitly open access only when needed. Draft content should never be publicly accessible without explicit authentication.
