---
description: PayloadCMS field changes workflow including backup strategies and schema migration best practices
---

# PayloadCMS Field Changes Workflow

## Overview
This guide covers the complete workflow for safely adding, editing, or removing fields in PayloadCMS collections, including backup strategies for both development and production environments.

## Pre-Change: Backup Strategies

### Development Environment Backups

**Quick API Backup (Recommended for Dev):**
```bash
# Backup specific collections before schema changes
curl "http://localhost:3000/api/articles?limit=1000" > backup-articles-$(date +%Y%m%d).json
curl "http://localhost:3000/api/categories?limit=1000" > backup-categories-$(date +%Y%m%d).json
curl "http://localhost:3000/api/keywords?limit=1000" > backup-keywords-$(date +%Y%m%d).json
```

**Database Backup (Comprehensive):**
```bash
# For Supabase/Postgres setups
pg_dump "your-database-url" > backup-$(date +%Y%m%d-%H%M%S).sql
```

### Production Environment Backups

**Pre-Deployment Checklist:**
- ✅ Full database backup via Supabase dashboard
- ✅ Export critical collections via API
- ✅ Document schema changes in migration notes
- ✅ Test schema changes in staging environment first

**Automated Backup Script:**
```javascript
// scripts/backup.js
const payload = require('payload');

async function backup() {
  await payload.init({
    secret: process.env.PAYLOAD_SECRET,
    local: true,
  });

  const collections = ['articles', 'categories', 'keywords'];
  
  for (const collection of collections) {
    const data = await payload.find({
      collection,
      limit: 1000,
    });
    
    const date = new Date().toISOString().split('T')[0];
    require('fs').writeFileSync(
      `./backups/${collection}-${date}.json`, 
      JSON.stringify(data, null, 2)
    );
  }
}
```

## Schema Change Workflow

### 1. Make Schema Changes
Edit collection files in [src/collections/](mdc:src/collections/):
- Add/remove/modify fields in collection configuration
- Update field types, validation, admin settings
- Add UI components or custom field logic

### 2. Apply Database Changes
```bash
# Kill existing dev server
pkill -f "next dev" || true

# Start dev server (detects schema changes automatically)
pnpm dev

# When prompted with schema warnings:
# "Accept warnings and push schema to database? › (y/N)"
# Type 'y' and press Enter to accept changes
```

### 3. Regenerate Types & Components
```bash
# Regenerate TypeScript types (critical for type safety)
pnpm payload generate:types

# Update import map (if adding new UI components)
pnpm payload generate:importmap
```

### 4. Verify Changes
- ✅ Check admin interface loads without errors
- ✅ Verify new fields appear correctly in forms
- ✅ Test field validation and behaviour
- ✅ Confirm API endpoints return expected data structure
- ✅ Test any custom UI components

## Field Change Types & Considerations

### Adding Fields
- **Safe operation** - minimal risk
- Consider default values for existing records
- Add validation rules carefully

### Modifying Field Types
- **Medium risk** - may require data transformation
- PayloadCMS will warn about potential data loss
- Test thoroughly with existing data

### Removing Fields
- **High risk** - permanent data loss
- Always backup data first
- Consider deprecation before removal

### Field Name Changes
- **High risk** - appears as remove + add to database
- Requires manual data migration
- Consider creating new field, migrating data, then removing old field

## Best Practices

### AI Assistant Guidelines
- ⚠️ **CRITICAL**: Always provide commands for the user to run manually
- ⚠️ **DO NOT automate** schema change commands via terminal tools
- ⚠️ **Interactive prompts** (schema warnings) require manual user input
- ⚠️ **Automation often fails** due to PayloadCMS interactive confirmations
- ✅ **Instead**: Provide clear command instructions and let user execute them

### 🚨 Database Safety Rules
- 🛑 **NEVER** reset the database without explicit user permission
- 🛑 **NEVER** drop tables without explicit user permission
- 🛑 **NEVER** run destructive commands like `migrate:reset`, `migrate:fresh`, or `db:reset`
- 🛑 **ALWAYS** ask for explicit permission before any destructive database operations
- ✅ **Safe operations**: Adding fields, generating types, starting dev server
- ✅ **Requires permission**: Removing fields, resetting data, dropping tables

### Development
- ✅ Use `pnpm` commands consistently (not `npx`)
- ✅ Always backup before significant schema changes
- ✅ Test field changes with sample data
- ✅ Regenerate types after every schema change

### Production
- ✅ Test all schema changes in staging first
- ✅ Create explicit migrations for complex changes
- ✅ Schedule schema changes during low-traffic periods
- ✅ Have rollback plan ready
- ✅ Monitor application after deployment

### Field Naming
- ✅ Choose descriptive, consistent field names
- ✅ Follow existing naming conventions in codebase
- ✅ Avoid frequent field name changes
- ✅ Use camelCase for field names

## Troubleshooting

### Server Won't Start After Schema Changes
```bash
# Check for syntax errors in collection files
# Verify all imports are correct
# Check logs for specific error messages
```

### Type Errors After Changes
```bash
# Regenerate types
pnpm payload generate:types

# Check payload-types.ts for new field definitions
# Update any TypeScript files using the changed fields
```

### Data Migration Issues
- Check PayloadCMS migration system: `pnpm payload migrate:status`
- Consider manual data transformation scripts
- Use API to update existing records if needed

## Related Files
- [Articles Collection](mdc:src/collections/Articles.ts) - Main article schema
- [Payload Config](mdc:src/payload.config.ts) - Global Payload configuration
- [Payload Types](mdc:payload-types.ts) - Generated TypeScript types

## Emergency Recovery

### If Schema Push Breaks Application
1. Restore database from backup
2. Revert schema changes in code
3. Restart development server
4. Investigate and fix schema issues
5. Test changes more thoroughly before re-applying

### If Data is Lost
1. Stop application immediately
2. Restore from most recent backup
3. Review schema change approach
4. Implement proper migration strategy
