# Frontend Page Setup Guidelines

_Version: 1.0 | Last Updated: January 2025_

This document outlines the mandatory standards and best practices for creating new pages in the Börsen Blick application. These guidelines ensure consistency, performance, accessibility, and maintainability across all frontend pages.

## 📋 Quick Checklist

Before considering a page complete, ensure ALL of the following are implemented:

- [ ] **Caching**: 5-minute cache duration for all data fetching
- [ ] **Loading States**: Suspense boundaries with matching skeleton components
- [ ] **Error Handling**: Try/catch blocks with German error messages
- [ ] **Accessibility**: Skip links, ARIA labels, semantic HTML
- [ ] **SEO**: Proper metadata generation (static or dynamic)
- [ ] **Layout**: Consistent 4-column grid system
- [ ] **Typography**: Merriweather (serif) + Roboto (sans-serif)
- [ ] **Localisation**: German-first content hierarchy
- [ ] **Performance**: Image optimisation and proper loading priorities

---

## 🎯 Core Standards

### 1. **Caching Strategy**

**MANDATORY**: All data fetching MUST use 5-minute cache duration for consistency.

```typescript
// ✅ CORRECT - Unified 5-minute caching with PayloadCMS
export const getCachedData = unstable_cache(
  async () => {
    const payload = await getPayload({ config });
    return await payload.find({
      collection: 'articles',
      where: { workflowStage: { equals: 'published' } },
      depth: 2,
    });
  },
  ['cache-key'],
  {
    revalidate: 300, // 5 minutes - MANDATORY
    tags: ['articles', 'specific-tags'],
  }
);

// ✅ CORRECT - Using PayloadCMS utilities
const getCachedDoc = getCachedDocument('articles', slug, 2);
const result = await getCachedDoc();

// ❌ WRONG - Different cache durations create inconsistency
revalidate: 600, // 10 minutes - DO NOT USE
revalidate: 150, // 2.5 minutes - DO NOT USE
```

### 2. **Loading States & Progressive Loading**

**MANDATORY**: All pages MUST implement Suspense boundaries for progressive loading.

```typescript
// ✅ CORRECT - Progressive loading with Suspense
<Suspense fallback={<ComponentSkeleton />}>
  <AsyncComponent />
</Suspense>

// ❌ WRONG - All-or-nothing loading
const data = await fetchAllData(); // Blocks entire page
return <PageWithAllData data={data} />;
```

**Skeleton Requirements**:

- Match exact layout of loaded content
- Use consistent animation classes: `animate-pulse`
- Provide appropriate `aria-busy="true"` and `aria-label` attributes

### 3. **Error Handling**

**MANDATORY**: All async operations MUST have try/catch blocks with German error messages.

```typescript
// ✅ CORRECT - Comprehensive error handling
export default async function PageSection() {
  try {
    const data = await getCachedData();
    return <Content data={data} />;
  } catch (error) {
    console.error('Error fetching data:', error);
    // Graceful fallback with German error message
    return (
      <ErrorState
        message="Fehler beim Laden der Inhalte"
        fallbackComponent={<EmptyState />}
      />
    );
  }
}

// ❌ WRONG - No error handling
export default async function PageSection() {
  const data = await getCachedData(); // Can crash entire page
  return <Content data={data} />;
}
```

---

## ♿ Accessibility Standards

### 1. **Skip Links (MANDATORY)**

Every page MUST include skip navigation for keyboard users.

```typescript
// ✅ REQUIRED - Skip links component
<nav className="sr-only focus-within:not-sr-only" aria-label="Skip navigation">
  <div className="fixed top-0 left-0 z-50 bg-background border border-border p-4 m-4 rounded-md shadow-lg">
    <ul className="flex flex-col gap-2 text-sm">
      <li>
        <a href="#main-content" className="text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-2 py-1">
          Skip to main content
        </a>
      </li>
      {/* Additional skip links specific to page */}
    </ul>
  </div>
</nav>
```

### 2. **ARIA & Semantic HTML (MANDATORY)**

```typescript
// ✅ CORRECT - Proper semantic structure
<main id="main-content" tabIndex={-1} role="main" aria-label="Page Title">
  <h1 className="sr-only">Page Title</h1>

  <section id="content-section" tabIndex={-1} aria-labelledby="section-heading">
    <h2 id="section-heading" className="sr-only">Section Title</h2>
    {/* Content */}
  </section>
</main>

// ❌ WRONG - Generic divs without accessibility
<div>
  <div>
    {/* Content without proper labeling */}
  </div>
</div>
```

### 3. **ARIA Live Regions**

```typescript
// ✅ REQUIRED - Status announcements
<div
  ref={liveRegionRef}
  className="sr-only"
  aria-live="polite"
  aria-atomic="true"
  role="status"
/>
```

---

## 🎨 Layout & Design Standards

### 1. **Grid System (MANDATORY)**

All pages MUST use the standardised 4-column responsive grid system.

```typescript
// ✅ REQUIRED - Standard grid layout
<div className="max-w-[1440px] mx-auto px-4 py-8 lg:py-12">
  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-0">

    {/* Column 1: Sidebar/Metadata */}
    <aside className="sm:border-r-2 sm:border-gray-50 sm:pr-2 lg:pr-3 xl:pr-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-3 sm:order-1">
      {/* Sidebar content */}
    </aside>

    {/* Columns 2-3: Main content */}
    <section className="sm:col-span-1 lg:col-span-2 xl:col-span-2 sm:border-r-2 sm:border-gray-50 sm:px-2 lg:px-3 xl:px-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-1 sm:order-2">
      {/* Main content */}
    </section>

    {/* Column 4: Secondary sidebar */}
    <aside className="xl:pl-4 order-2 sm:order-3">
      {/* Secondary content */}
    </aside>

  </div>
</div>
```

### 2. **Typography Standards**

```typescript
// ✅ REQUIRED - Font usage rules
{/* Content/Articles: Merriweather (serif) */}
<h1 className="font-serif text-xl md:text-2xl lg:text-3xl">Article Title</h1>
<p className="font-serif text-base/8">Article content...</p>

{/* UI Elements: Roboto (sans-serif) */}
<button className="font-sans text-sm">Button Text</button>
<span className="font-sans text-xs text-muted-foreground">Metadata</span>

{/* CRITICAL: Use line-height modifiers, NEVER separate leading-* classes */}
{/* ✅ CORRECT - Tailwind v4 pattern */}
<p className="text-base/7">Text with combined font-size and line-height</p>
<p className="text-lg/8">Larger text with appropriate line height</p>

{/* ❌ WRONG - Old pattern, avoid */}
<p className="text-base leading-7">Don't use separate leading classes</p>
```

### 3. **Responsive Breakpoints**

```css
/* Standard breakpoints - DO NOT DEVIATE */
sm: 640px   /* Small devices */
md: 768px   /* Medium devices */
lg: 1024px  /* Large devices */  
xl: 1280px  /* Extra large */

/* CRITICAL: Always use gap utilities for spacing in flex/grid layouts */
/* ✅ CORRECT */
.flex {
  gap: 1rem;
}
.grid {
  gap: 1rem;
}

/* ❌ NEVER use space-x-* or space-y-* in flex/grid layouts */
/* These break with flex-wrap and have edge cases */
```

---

## 🔍 SEO & Metadata Standards

### 1. **Dynamic Metadata (RECOMMENDED)**

For content pages, implement dynamic metadata generation:

```typescript
// ✅ PREFERRED - Dynamic metadata (Next.js 15 compatible)
export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const { slug } = await params; // Next.js 15 requires awaiting params
  const data = await fetchPageData(slug);

  if (!data) {
    return {
      title: 'Not Found | Börsen Blick',
      description: 'The requested content could not be found.',
    };
  }

  return {
    title: `${data.title} | Börsen Blick`,
    description: data.description,
    openGraph: {
      title: data.title,
      description: data.description,
      type: 'article', // or 'website'
      images: data.image
        ? [{ url: data.image.url, width: 1200, height: 630 }]
        : [],
    },
    twitter: {
      card: 'summary_large_image',
      title: data.title,
      description: data.description,
    },
  };
}
```

### 2. **Static Generation vs ISR**

```typescript
// ✅ For content pages - Static generation with ISR
export async function generateStaticParams(): Promise<{ slug: string }[]> {
  try {
    return await getCachedPublishedArticleSlugs(); // 5min cache
  } catch (error) {
    console.error('Error generating static params:', error);
    return [];
  }
}

export const revalidate = 300; // 5 minutes ISR

// ✅ For dynamic pages - ISR only
export const revalidate = 300; // 5 minutes
```

---

## 🌍 Localisation Standards

### 1. **Content Priority (MANDATORY)**

German content MUST take priority across all components:

```typescript
// ✅ REQUIRED - German-first hierarchy
const title =
  content.germanTab?.germanTitle ||
  content.englishTab?.enhancedTitle ||
  content.title;

const description =
  content.germanTab?.germanSummary ||
  content.englishTab?.enhancedSummary ||
  'Default description';
```

### 2. **Error Messages**

All user-facing error messages MUST be in German:

```typescript
// ✅ REQUIRED - German error messages
const errorMessages = {
  loading: 'Inhalt wird geladen...',
  error: 'Fehler beim Laden der Inhalte',
  notFound: 'Inhalt nicht gefunden',
  tryAgain: 'Erneut versuchen',
  empty: 'Derzeit keine Inhalte verfügbar',
};
```

---

## ⚡ Performance Standards

### 1. **Image Optimisation**

```typescript
// ✅ REQUIRED - Proper image handling
<Image
  src={imageUrl}
  alt={altText}
  width={800}
  height={400}
  priority={isAboveFold} // True for hero images
  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 66vw, 800px"
  className="w-full h-auto object-cover"
/>
```

### 2. **Component Performance**

```typescript
// ✅ RECOMMENDED - Memoization for heavy components
const ExpensiveComponent = memo(({ data }) => {
  const processedData = useMemo(() =>
    processData(data), [data]
  );

  return <div>{processedData}</div>;
}, (prevProps, nextProps) => {
  // Custom comparison for optimal re-rendering
  return prevProps.data.id === nextProps.data.id;
});
```

### 3. **Tailwind v4 Specific Patterns**

```typescript
// ✅ REQUIRED - Use opacity modifiers (Tailwind v4)
<div className="bg-red-500/60">Background with 60% opacity</div>
<span className="text-black/75">Text with 75% opacity</span>

// ❌ WRONG - Old opacity utilities (removed in v4)
<div className="bg-red-500 bg-opacity-60">Don't use bg-opacity-*</div>
<span className="text-black text-opacity-75">Don't use text-opacity-*</span>

// ✅ REQUIRED - New gradient utilities (Tailwind v4)
<div className="bg-linear-to-r from-blue-500 to-purple-600">Linear gradient</div>

// ❌ WRONG - Old gradient utilities (renamed in v4)
<div className="bg-gradient-to-r from-blue-500 to-purple-600">Old syntax</div>

// ✅ REQUIRED - Use size-* for equal dimensions
<div className="size-16">Square element (64x64px)</div>

// ✅ RECOMMENDED - Use min-h-dvh instead of min-h-screen
<div className="min-h-dvh">Full viewport height (mobile-safe)</div>
```

---

## 🎯 PayloadCMS Integration Standards

### 1. **Data Fetching Patterns**

```typescript
// ✅ REQUIRED - Use PayloadCMS Local API with proper error handling
import { getPayload } from 'payload';
import config from '@payload-config';

export default async function fetchArticleData(slug: string) {
  try {
    const payload = await getPayload({ config });

    const result = await payload.find({
      collection: 'articles',
      where: {
        and: [
          { slug: { equals: slug } },
          { workflowStage: { equals: 'published' } },
        ],
      },
      depth: 2, // Include relationships
      limit: 1,
    });

    return result.docs[0] || null;
  } catch (error) {
    console.error('Error fetching article:', error);
    return null;
  }
}

// ✅ REQUIRED - Thread requests for transactions (recommended)
const result = await payload.find({
  collection: 'posts',
  req, // Always pass req for transaction threading
});
```

### 2. **Content Hierarchy (Multi-language)**

```typescript
// ✅ REQUIRED - German-first content priority
const title =
  article.germanTab?.germanTitle ||
  article.englishTab?.enhancedTitle ||
  article.title;

const description =
  article.germanTab?.germanSummary ||
  article.englishTab?.enhancedSummary ||
  'Default description';

// ✅ REQUIRED - Handle media relationships properly
const featuredImage =
  typeof article.featuredImage === 'object'
    ? (article.featuredImage as Media)
    : null;
```

### 3. **Access Control Patterns**

```typescript
// ✅ RECOMMENDED - Proper access control implementation
import type { Access } from 'payload';

export const publishedOrAuthenticated: Access = ({ req: { user } }) => {
  // Authenticated users see all
  if (user) return true;

  // Public users see only published content
  return {
    workflowStage: { equals: 'published' },
  };
};
```

---

## 🧪 Testing Requirements

### 1. **Component Testing**

Every new page component MUST include:

```typescript
// ✅ REQUIRED - Basic component tests
describe('PageComponent', () => {
  it('renders loading state correctly', () => {
    // Test skeleton/loading state
  });

  it('handles error states gracefully', () => {
    // Test error boundaries and fallbacks
  });

  it('displays content when loaded', () => {
    // Test successful data loading
  });

  it('meets accessibility requirements', () => {
    // Test skip links, ARIA labels, keyboard navigation
  });
});
```

### 2. **Performance Testing**

```typescript
// ✅ RECOMMENDED - Performance benchmarks
it('loads within performance budgets', async () => {
  const metrics = await measurePageLoad();
  expect(metrics.LCP).toBeLessThan(2500); // Largest Contentful Paint
  expect(metrics.FID).toBeLessThan(100); // First Input Delay
  expect(metrics.CLS).toBeLessThan(0.1); // Cumulative Layout Shift
});
```

---

## 📁 File Structure Standards

### 1. **Page Organisation**

```
src/app/(frontend)/
├── page-name/
│   ├── page.tsx              # Main page component
│   ├── loading.tsx           # Loading skeleton
│   ├── not-found.tsx         # 404 page
│   └── error.tsx             # Error boundary
└── components/
    └── page-name/
        ├── PageSection.tsx   # Async sections
        ├── PageSkeleton.tsx  # Loading skeletons
        └── PageAccessibilityNav.tsx # Skip links
```

### 2. **Component Naming**

```typescript
// ✅ REQUIRED - Consistent naming conventions
PageNameSection.tsx        # Async data-fetching components
PageNameSkeleton.tsx      # Loading skeleton components
PageNameAccessibilityNav.tsx # Accessibility components
PageNameErrorBoundary.tsx # Error boundary components
```

---

## 🚨 Common Pitfalls to Avoid

### 1. **❌ Performance Killers**

- Different cache durations (use 300s everywhere)
- Blocking entire page loading (use Suspense)
- Missing image optimisation
- No memoization for expensive operations
- Using `min-h-screen` (use `min-h-dvh` for mobile compatibility)

### 2. **❌ Accessibility Violations**

- Missing skip links
- No ARIA labels or semantic HTML
- Missing focus management
- English-only error messages

### 3. **❌ Next.js 15 Anti-Patterns**

- Not awaiting `params` in `generateMetadata` and page components
- Using synchronous `params` access (must be awaited)
- Missing error boundaries in async components
- Improper `cookies()` usage (must be awaited in Next.js 15)

### 4. **❌ Tailwind v4 Anti-Patterns**

- Using removed opacity utilities (`bg-opacity-*`, `text-opacity-*`)
- Using old gradient syntax (`bg-gradient-*` instead of `bg-linear-*`)
- Using `space-x-*` or `space-y-*` in flex/grid layouts (use `gap`)
- Using separate `leading-*` classes (use text size with line height: `text-base/7`)
- Using arbitrary values instead of design scale

### 5. **❌ PayloadCMS Anti-Patterns**

- Not using PayloadCMS native utilities like `getCachedDocument`
- Bypassing Local API patterns with custom fetch functions
- Not handling media relationships properly (checking object vs ID)
- Missing error handling in PayloadCMS operations
- Not threading requests with `req` parameter

### 6. **❌ Inconsistent Patterns**

- Custom grid systems (use standard 4-column)
- Wrong font usage (serif for content, sans for UI)
- Inconsistent error handling
- Missing Suspense boundaries

---

## ✅ Page Review Checklist

Before deployment, every page must pass this checklist:

### **Functionality**

- [ ] All data fetching uses 5-minute caching
- [ ] Suspense boundaries implemented for all async operations
- [ ] Error handling with German fallback messages
- [ ] Static generation or ISR configured appropriately

### **Accessibility**

- [ ] Skip links implemented and functional
- [ ] ARIA labels and semantic HTML throughout
- [ ] Keyboard navigation works correctly
- [ ] Screen reader tested and functional

### **Performance**

- [ ] Images optimised with proper sizes and priority
- [ ] Components memoized where appropriate
- [ ] Loading states match content layout exactly
- [ ] Page loads within performance budgets

### **Design & UX**

- [ ] 4-column grid system implemented correctly
- [ ] Typography follows serif/sans-serif rules
- [ ] Responsive design works across all breakpoints
- [ ] German localisation priority maintained

### **SEO & Metadata**

- [ ] Dynamic or static metadata implemented
- [ ] OpenGraph and Twitter cards configured
- [ ] Proper canonical URLs and structured data
- [ ] 404 and error pages properly configured

---

## 📚 Reference Implementation

The **article single page** (`src/app/(frontend)/articles/[slug]/page.tsx`) and **homepage** (`src/app/(frontend)/page.tsx`) serve as the **gold standard** implementations of these guidelines.

Study these implementations for:

- Proper Suspense boundary usage
- Error handling patterns
- Accessibility implementation
- Caching strategy
- Layout structure
- Component organisation

---

## 🎯 Success Criteria

A page is considered **production-ready** when it:

1. **Loads progressively** with independent sections
2. **Handles errors gracefully** without crashing
3. **Provides excellent accessibility** for all users
4. **Maintains consistent performance** across devices
5. **Follows German-first localisation** patterns
6. **Implements proper SEO** for discoverability
7. **Uses standard design patterns** from reference pages

---

_This document is a living standard. Update it as new patterns emerge and requirements evolve._
