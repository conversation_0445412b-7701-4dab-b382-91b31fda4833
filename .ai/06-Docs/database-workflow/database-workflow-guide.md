# Database Development Workflow Guide

## 🎯 Overview
This guide shows you how to streamline database development using Supabase CLI and PayloadCMS migrations.

## 🛠️ Setup: Supabase CLI Commands

### Essential Commands
```bash
# Start local Supabase (Docker)
supabase start

# Stop local Supabase
supabase stop

# Reset local database (fresh start)
supabase db reset

# Generate migration from schema changes
supabase db diff --use-migra -f <migration_name>

# Apply migrations
supabase db push

# Check migration status
supabase migration list
```

## 📋 Recommended Workflow

### 1. **Development Cycle**
```bash
# 1. Start with clean state
supabase db reset

# 2. Start your app in dev mode
npm run dev

# 3. Make schema changes in PayloadCMS code
# (edit collections, fields, etc.)

# 4. Let PayloadCMS auto-sync schema
# (it will prompt you - this is fine in dev)

# 5. Generate migration from changes
supabase db diff --use-migra -f add_new_field

# 6. Review and commit the migration
git add supabase/migrations/
git commit -m "Add new field to articles"
```

### 2. **When Schema Gets Messy**
```bash
# Nuclear option: start fresh
supabase db reset
npm run dev
# Accept all schema changes

# Then generate clean migration
supabase db diff --use-migra -f clean_schema_reset
```

## 🔄 PayloadCMS + Supabase Integration

### Understanding the Components

1. **PayloadCMS Collections** (`src/collections/`)
  - Define your data structure in TypeScript
  - PayloadCMS reads these to know what tables/columns to create

2. **PayloadCMS Migrations** (`src/migrations/`)
  - Custom data transformations
  - Use for complex changes that can't be auto-generated

3. **Supabase Migrations** (`supabase/migrations/`)
  - SQL files that modify database structure
  - Generated from actual database changes

### The Two-Layer Approach

```
Your Code (Collections) → PayloadCMS Auto-Sync → Database → Supabase Diff → Migration Files
```

## 🎛️ Development Modes

### Mode 1: Schema Development (What you've been doing)
- Edit PayloadCMS collections
- Let PayloadCMS auto-sync database
- Accept schema changes
- Generate Supabase migration afterwards

### Mode 2: Migration-First (Production recommended)
- Write migrations first
- Apply them with `supabase db push`
- Update PayloadCMS collections to match

## 🚨 Avoiding Common Issues

### Issue: Enum Conflicts
**Problem**: PayloadCMS enum doesn't match database enum

**Solution**:
```bash
# Option 1: Reset and regenerate
supabase db reset

# Option 2: Fix specific enum
supabase db diff --use-migra -f fix_enum_mismatch
```

### Issue: Column Type Conflicts
**Problem**: PayloadCMS expects different column type

**Solution**:
```sql
-- Create migration manually in supabase/migrations/
ALTER TABLE articles
ALTER COLUMN my_column
SET DATA TYPE new_type
USING my_column::new_type;
```

### Issue: Data Loss Warnings
**Problem**: PayloadCMS wants to delete columns with data

**Prevention**:
```bash
# 1. Backup data first
supabase db dump --data-only > backup.sql

# 2. Or migrate data in PayloadCMS migration before schema change
```

## 📚 Key Concepts Explained

### Import Maps (PayloadCMS specific)
```bash
# Regenerate import maps when you add new components
npm run generate:importmap
```
- Not database related
- Tells PayloadCMS what components are available
- Run when you add new admin components

### Schema Sync vs Migrations
- **Schema Sync**: PayloadCMS automatically adjusting database to match your code
- **Migrations**: Planned, versioned changes to database structure

### Development vs Production
- **Development**: Schema sync is fine, experiment freely
- **Production**: Only use migrations, never schema sync

## 🎯 Best Practices

### 1. **Local Development**
```bash
# Daily routine
supabase start
npm run dev
# Make changes
# Accept schema sync
# Generate migration at end of day
supabase db diff --use-migra -f daily_changes
```

### 2. **Before Committing**
```bash
# Clean slate test
supabase db reset
npm run dev
# Verify everything works from scratch
```

### 3. **Team Collaboration**
```bash
# Pull latest changes
git pull
# Apply new migrations
supabase db reset  # or supabase db push if you have data to preserve
```

### 4. **Production Deployment**
```bash
# Apply migrations to production
supabase db push --linked
# Deploy app code
```

## 🆘 Emergency Procedures

### "My database is completely broken"
```bash
supabase db reset
npm run dev
# Accept all schema changes
supabase db diff --use-migra -f emergency_reset
```

### "I have data I can't lose"
```bash
# Backup first
supabase db dump > emergency_backup.sql
# Then fix issues
# Restore if needed: psql < emergency_backup.sql
```

### "Migrations are out of sync"
```bash
# Check status
supabase migration list
# Reset and reapply
supabase db reset
supabase db push
```

## 📖 Learning Resources

- [Supabase CLI Docs](https://supabase.com/docs/reference/cli)
- [PayloadCMS Database Docs](https://payloadcms.com/docs/database/overview)
- [PostgreSQL Migration Best Practices](https://www.postgresql.org/docs/current/ddl.html)

## 🔧 Tooling Setup

### Recommended VS Code Extensions
- PostgreSQL (for viewing database)
- Supabase (official extension)

### Useful Aliases
```bash
# Add to your ~/.zshrc or ~/.bashrc
alias sbs='supabase start'
alias sbr='supabase db reset'
alias sbd='supabase db diff --use-migra'
alias sbp='supabase db push'
```

---

  **Remember**: In development, it's okay to break things! That's what local Docker instances are for. Experiment, reset, and learn! 🚀
