# Database Maintenance Guide

This guide provides comprehensive instructions for maintaining and cleaning up your Börsen Blick database.

## 📋 Overview

Based on the database analysis, we've identified several areas for optimization:

### **Unused Columns Identified:**
- `relevance_score` in `processed_urls` table (not used in current codebase)
- `relevance_score` in `articles` table (legacy field)
- `relevance_reasoning` in `articles` table (legacy field)

### **Development Collections:**
- `IntegrationTest` collection (development only, removed from production config)

## 🛠️ Maintenance Tools

### **1. Database Inspection Script**
**Location:** `scripts/inspect-database.js`

This script provides a comprehensive analysis of your database:
```bash
node scripts/inspect-database.js
```

**What it shows:**
- Table sizes and row counts
- Column analysis for main tables
- Identification of development/test tables
- Unused index detection
- Overall database size

### **2. Database Cleanup Migration**
**Location:** `src/migrations/database-cleanup-migration.ts`

This PayloadCMS migration removes unused columns:
```bash
pnpm run payload db:push
```

**What it removes:**
- `relevance_score` from `processed_urls` table
- `relevance_score` and `relevance_reasoning` from `articles` table
- Cleans up old document locks
- Updates table statistics

### **3. Ongoing Maintenance Script**
**Location:** `scripts/database-maintenance.js`

Regular maintenance tasks:
```bash
# Full maintenance
node scripts/database-maintenance.js

# Specific tasks
node scripts/database-maintenance.js cleanup-urls
node scripts/database-maintenance.js cleanup-locks
node scripts/database-maintenance.js analyze
```

**What it does:**
- Removes old processed URLs (>90 days old, rejected/error status)
- Cleans expired document locks (>24 hours)
- Updates table statistics for better performance
- Provides health checks and database statistics

## 📝 Maintenance Checklist

### **One-Time Cleanup (Do Once)**

1. **Backup your database first!**
```bash
pg_dump $DATABASE_URI > backup_$(date +%Y%m%d_%H%M%S).sql
```

2. **Run the inspection script**
```bash
node scripts/inspect-database.js
```

3. **Run the cleanup migration**
```bash
pnpm run payload db:push
```

4. **Verify results**
```bash
node scripts/inspect-database.js
```

### **Regular Maintenance (Weekly/Monthly)**

1. **Run maintenance script**
```bash
node scripts/database-maintenance.js
```

2. **Monitor database size and performance**
  - Check table sizes regularly
  - Monitor query performance
  - Review RSS feed processing efficiency

## 🚨 Important Notes

### **Before Running Cleanup:**
- ✅ **Backup your database** - this is critical!
- ✅ Test in a development environment first
- ✅ Review what columns will be removed
- ✅ Ensure no custom code depends on removed fields

### **Removed from Production:**
- `IntegrationTest` collection has been removed from `src/payload.config.ts`
- This was development-only and safe to remove

### **Column Removal Impact:**
- `relevance_score` fields were not actively used in the codebase
- `relevance_reasoning` was a legacy field from an earlier version
- No current functionality depends on these fields

## 📊 Expected Benefits

After cleanup, you should see:
- **Reduced database size** (exact amount depends on your data)
- **Improved query performance** (fewer columns to scan)
- **Simplified schema** (easier maintenance)
- **Better organised data** (no obsolete fields)

## 🔧 Troubleshooting

### **If Migration Fails:**
1. Check database connection
2. Ensure proper permissions
3. Review migration logs
4. Restore from backup if needed

### **Performance Issues:**
1. Run `ANALYZE` on tables
2. Check for missing indexes
3. Monitor long-running queries
4. Consider `VACUUM` if needed

### **Rollback Procedure:**
```sql
-- If you need to restore columns (customize as needed)
ALTER TABLE processed_urls ADD COLUMN relevance_score numeric;
ALTER TABLE articles ADD COLUMN relevance_score numeric;
ALTER TABLE articles ADD COLUMN relevance_reasoning varchar;
```

## 📅 Recommended Schedule

### **Weekly:**
```bash
node scripts/database-maintenance.js cleanup-locks analyze
```

### **Monthly:**
```bash
node scripts/database-maintenance.js
```

### **Quarterly:**
```bash
node scripts/inspect-database.js
# Review results and plan any additional cleanup
```

## 💡 Additional Optimizations

### **Future Considerations:**
1. **Archive old articles** (after 1+ years)
2. **Implement data retention policies**
3. **Monitor and optimize RSS feed processing**
4. **Regular index maintenance**
5. **Partition large tables** (if they grow significantly)

### **Monitoring:**
- Set up alerts for database size growth
- Monitor query performance regularly
- Track RSS feed processing efficiency
- Watch for failed processing attempts

## 🆘 Support

If you encounter issues:
1. Check the database logs
2. Review the migration output
3. Ensure all dependencies are installed (`pg`, `dotenv`)
4. Verify database connection settings

## 📈 Success Metrics

After implementing this maintenance plan:
- Database size should be optimized
- Query performance should improve
- Development environment should be cleaner
- Future maintenance should be easier

---

  **Remember:** Always backup before making changes, and test in development first!
