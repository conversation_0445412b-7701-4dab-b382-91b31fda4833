# Database Backup Guide: Git-like Snapshots for Supabase

## Overview

Just like Git gives you version control for your code, you can create a similar system for your database. This guide shows you how to create "snapshots" of your Supabase database that you can easily restore if something goes wrong.

## Understanding Your Current Setup

Based on your project structure, you're using:

- **Supabase locally** running in Docker (port 54322)
- **PostgreSQL database** (that's what Supabase uses under the hood)
- **Docker containers** for the database

**Important Note**: Since you're running Supabase locally (not linked to a remote project), you'll need to use PostgreSQL commands (`pg_dump` and `psql`) instead of Supabase CLI commands. This guide has been updated with the correct commands for your local setup.

## Why Database Backups Matter

Think of database backups like Git commits for your data:

- **Before major changes**: Create a snapshot before adding new features
- **Daily snapshots**: Regular backups of your development work
- **Pre-deployment**: Backup before pushing changes to production
- **Rollback capability**: Quickly restore if something breaks

## The "Git-like" Workflow Concept

Here's how to think about it:

| Git Command               | Database Equivalent          | What It Does              |
| ------------------------- | ---------------------------- | ------------------------- |
| `git commit -m "message"` | Create snapshot with message | Save current state        |
| `git log`                 | List all snapshots           | See backup history        |
| `git checkout <commit>`   | Restore from snapshot        | Go back to previous state |
| `git branch`              | Multiple backup sets         | Separate backup streams   |

## Backup Strategies

### 1. Manual Snapshots (Recommended to Start)

**What**: Create backups whenever you want, with descriptive messages

**When to use**:

- Before making database schema changes
- After adding important test data
- Before trying experimental features

**Commands**:

```bash
# Create a snapshot (for local Supabase)
mkdir -p backups
pg_dump -h localhost -p 54322 -U postgres -d postgres > backups/backup-$(date +%Y%m%d_%H%M%S)-your-message.sql

# List your backups
ls -la backups/

# Restore from a backup
psql -h localhost -p 54322 -U postgres -d postgres < backups/your-backup-file.sql
```

### 2. Automated Daily Backups

**What**: Automatic snapshots every day at a set time

**When to use**:

- You want peace of mind
- You're working on the project regularly
- You want to track daily progress

**How**: Create a simple script that runs daily (via cron job on Mac)

### 3. Feature-Branch Backups

**What**: Create backups that align with your Git branches

**When to use**:

- You're working on multiple features
- You want to match database state with code state
- You're collaborating with others

**Example**:

```bash
# When starting a new feature
git checkout -b feature/new-rss-parser
# Create corresponding database backup
# Work on feature...
# Backup again when feature is complete
```

## Practical Implementation Options

### Option 1: Supabase CLI (For Remote Projects Only)

**Note**: This option only works if you're linked to a remote Supabase project. Since you're running locally, skip to Option 2.

**Pros**: Built-in, easy to use, designed for Supabase
**Cons**: Requires remote project link, less customisation

```bash
# Create backup (only works with remote projects)
supabase db dump -f my-backup.sql

# Restore backup (only works with remote projects)
supabase db reset
supabase db push
```

### Option 2: PostgreSQL Tools (Recommended for Local Development)

**Pros**: More features, works with any PostgreSQL, perfect for local Supabase
**Cons**: Need to install PostgreSQL tools separately (usually already installed with Supabase)

```bash
# Create backup
pg_dump -h localhost -p 54322 -U postgres -d postgres > backup.sql

# Restore backup
psql -h localhost -p 54322 -U postgres -d postgres < backup.sql
```

### Option 3: Docker Volume Snapshots (Advanced)

**Pros**: Fastest backup/restore, captures everything
**Cons**: Larger files, less portable

```bash
# Stop containers
docker-compose down

# Create volume backup
docker run --rm -v borsenblick_db_data:/data -v $(pwd)/backups:/backup alpine tar czf /backup/db-backup-$(date +%Y%m%d_%H%M%S).tar.gz -C /data .

# Restore volume backup
docker run --rm -v borsenblick_db_data:/data -v $(pwd)/backups:/backup alpine tar xzf /backup/your-backup-file.tar.gz -C /data

# Start containers
docker-compose up -d
```

## Building Your Workflow

### Phase 1: Start Simple (Week 1)

1. Create a `backups/` directory in your project
2. Add `backups/` to your `.gitignore` file
3. Practice creating manual backups before big changes
4. Learn the restore process (try it with test data first!)

### Phase 2: Add Structure (Week 2)

1. Create naming conventions: `backup-YYYYMMDD_HHMMSS-description.sql`
2. Create backup scripts for common scenarios
3. Document your backup process
4. Test restoring from backups regularly

### Phase 3: Automate (Week 3+)

1. Set up daily automated backups
2. Create cleanup scripts (keep last 10 backups)
3. Integrate with your Git workflow
4. Consider backup verification

## Sample Backup Script Structure

Here's what a simple backup script might look like:

```bash
#!/bin/bash
# backup-db.sh

# Configuration
BACKUP_DIR="./backups"
DATE=$(date +%Y%m%d_%H%M%S)
MESSAGE=${1:-"manual-backup"}

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Create the backup
echo "📸 Creating database snapshot: $DATE"
echo "💬 Message: $MESSAGE"

pg_dump -h localhost -p 54322 -U postgres -d postgres > "$BACKUP_DIR/backup-$DATE-$MESSAGE.sql"

echo "✅ Backup created: backup-$DATE-$MESSAGE.sql"

# List recent backups
echo "📋 Recent backups:"
ls -la $BACKUP_DIR | tail -5
```

## Best Practices

### 🎯 Golden Rules

1. **Always backup before major changes**
2. **Test your restore process regularly**
3. **Keep backups organised with clear names**
4. **Don't rely on just one backup method**
5. **Document what each backup contains**

### 📝 Naming Conventions

- `backup-20250113_143022-before-rss-changes.sql`
- `backup-20250113_090000-daily-auto.sql`
- `backup-20250113_120000-feature-complete.sql`

### 🧹 Cleanup Strategy

- Keep daily backups for 1 week
- Keep weekly backups for 1 month
- Keep monthly backups for 6 months
- Always keep "milestone" backups (major features)

### 🔒 Security Notes

- Never commit backup files to Git (add to `.gitignore`)
- Consider encrypting backups if they contain sensitive data
- Store important backups outside your project directory

## Directory Structure

```
borsenblick/
├── backups/                    # Your database backups
│   ├── backup-20250113_090000-daily.sql
│   ├── backup-20250113_143022-before-rss-changes.sql
│   └── backup-20250112_180000-feature-complete.sql
├── scripts/
│   ├── backup-db.sh           # Manual backup script
│   ├── restore-db.sh          # Restore script
│   └── cleanup-old-backups.sh # Cleanup script
└── docs/
    └── database-backup-guide.md # This guide
```

## Common Scenarios

### 🔧 "I'm about to change the database schema"

```bash
./scripts/backup-db.sh "before-schema-changes"
# Make your changes
# If something breaks:
./scripts/restore-db.sh backup-20250113_143022-before-schema-changes.sql
```

### 📅 "I want daily backups"

```bash
# Add to crontab (crontab -e)
0 9 * * * cd /path/to/borsenblick && ./scripts/backup-db.sh "daily-auto"
```

### 🚀 "I'm deploying to production"

```bash
./scripts/backup-db.sh "pre-deployment-$(date +%Y%m%d)"
# Deploy your changes
# Keep this backup as a milestone
```

### 💥 "Something went wrong, I need to rollback"

```bash
# List available backups
ls -la backups/
# Choose the one you want
./scripts/restore-db.sh backup-20250113_120000-working-state.sql
```

## Next Steps

1. **Start with manual backups** - Get comfortable with the process
2. **Create your first backup script** - Automate the basics
3. **Practice restoring** - Make sure you can get your data back
4. **Build it into your workflow** - Make it part of your development routine
5. **Consider automation** - Set up daily backups once you're comfortable

## Tools You'll Need

- **Supabase CLI** (you already have this)
- **Basic shell scripting** (we can help you learn)
- **Cron jobs** (for automation, built into macOS)
- **Docker knowledge** (you're already using this)

Remember: The best backup is the one you actually use! Start simple and build up your system over time.
