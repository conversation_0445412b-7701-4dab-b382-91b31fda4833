{"timing": {"startTime": 1753386610128, "totalDurationMs": 42, "phases": {"rssParsingMs": 6, "firecrawlProcessingMs": 0, "openaiProcessingMs": 0, "databaseOperationsMs": 0}, "endTime": 1753386610170}, "processing": {"feedsProcessed": 0, "articlesScraped": 0, "articlesAccepted": 0, "articlesRejected": 0, "successRate": 0, "throughputPerMinute": 0}, "apiUsage": {"firecrawl": {"requestsCount": 0, "successCount": 0, "rateLimitedCount": 0, "errorCount": 0, "averageResponseTimeMs": 0, "estimatedCost": 0}, "openai": {"requestsCount": 0, "tokensUsed": 0, "averageResponseTimeMs": 0, "estimatedCost": 0, "enhancementSuccessRate": 0}}, "feedMetrics": [], "system": {"memoryUsageMB": 1891, "cpuUsagePercent": 0, "parallelProcessingEfficiency": 14.285714285714285}, "quality": {"averageContentQuality": 0, "duplicateFilterEfficiency": 0, "keywordMatchAccuracy": 0}}