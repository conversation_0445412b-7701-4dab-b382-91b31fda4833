{"timing": {"startTime": 1753387716289, "totalDurationMs": 3365293, "phases": {"rssParsingMs": 3365109, "firecrawlProcessingMs": 0, "openaiProcessingMs": 0, "databaseOperationsMs": 0}, "endTime": 1753391081582}, "processing": {"feedsProcessed": 0, "articlesScraped": 0, "articlesAccepted": 0, "articlesRejected": 0, "successRate": 0, "throughputPerMinute": 0}, "apiUsage": {"firecrawl": {"requestsCount": 121, "successCount": 121, "rateLimitedCount": 0, "errorCount": 0, "averageResponseTimeMs": 2000, "estimatedCost": 0.242}, "openai": {"requestsCount": 0, "tokensUsed": 0, "averageResponseTimeMs": 0, "estimatedCost": 0, "enhancementSuccessRate": 0}}, "feedMetrics": [], "system": {"memoryUsageMB": 1116, "cpuUsagePercent": 0, "parallelProcessingEfficiency": 99.9945324225855}, "quality": {"averageContentQuality": 0, "duplicateFilterEfficiency": 0, "keywordMatchAccuracy": 0}}