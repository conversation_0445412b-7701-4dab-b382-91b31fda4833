{"timestamp":"2025-07-24T20:23:14.371Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Energie : Das sind die schönsten Wärmepumpen","url":"https://www.handelsblatt.com/unternehmen/energie/energie-diese-modernen-waermepumpen-verschoenern-ihren-garten/100029817.html","feedId":"1","feedName":"Handelsblatt","contentLength":153320,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Energie : Das sind die schönsten Wärmepumpen\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Energie : Das sind die schönsten Wärmepumpen\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T20:56:14.517Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Der neue Online Broker - Smartbroker","url":"https://smartbroker.de?utm_source=wallstreetonline&utm_medium=referral&utm_campaign=intern&utm_content=rss_feed","feedId":"2","feedName":"Wallstreet Online","contentLength":39569712,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Der neue Online Broker - Smartbroker\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Der neue Online Broker - Smartbroker\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T20:56:35.220Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Kursexplosion am Donnerstag: Bloom Energy: Das lässt die Wasserstoff-Aktie jetzt durch die Decke geh","url":"https://www.wallstreet-online.de/nachricht/19674408-kursexplosion-donnerstag-bloom-energy-laesst-wasserstoff-aktie-decke-gehen","feedId":"2","feedName":"Wallstreet Online","contentLength":115490,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Kursexplosion am Donnerstag: Bloom Energy: Das lässt die Wasserstoff-Aktie jetzt durch die Decke gehen!\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Kursexplosion am Donnerstag: Bloom Energy: Das lässt die Wasserstoff-Aktie jetzt durch die Decke gehen!\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T20:56:56.511Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Norwegens Cash-Cow: Peak Oil erreicht? Das steckt hinter dem Downgrade für Dividendenwert Equinor","url":"https://www.wallstreet-online.de/nachricht/19674636-norwegens-cash-cow-peak-oil-erreicht-steckt-downgrade-dividendenwert-equinor","feedId":"2","feedName":"Wallstreet Online","contentLength":117952,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Norwegens Cash-Cow: Peak Oil erreicht? Das steckt hinter dem Downgrade für Dividendenwert Equinor\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Norwegens Cash-Cow: Peak Oil erreicht? Das steckt hinter dem Downgrade für Dividendenwert Equinor\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T20:56:56.511Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Börse, Baby!: Goldrallye? Für diese Minenaktie lief es in diesem Jahr noch besser!","url":"https://www.wallstreet-online.de/nachricht/19674066-boerse-baby-goldrallye-minenaktie-lief-jahr-besser","feedId":"2","feedName":"Wallstreet Online","contentLength":152782,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Börse, Baby!: Goldrallye? Für diese Minenaktie lief es in diesem Jahr noch besser!\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Börse, Baby!: Goldrallye? Für diese Minenaktie lief es in diesem Jahr noch besser!\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T20:56:58.671Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Schockierende Dividenden-News: Das könnte jetzt Ärger auch für BASF geben!","url":"https://www.wallstreet-online.de/nachricht/19672602-schockierende-dividenden-news-aerger-basf-geben","feedId":"2","feedName":"Wallstreet Online","contentLength":115764,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Schockierende Dividenden-News: Das könnte jetzt Ärger auch für BASF geben!\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Schockierende Dividenden-News: Das könnte jetzt Ärger auch für BASF geben!\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T20:58:20.878Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Kurs schnellt hoch: MTU Aero Engines: Starkes Wachstum und optimistische Aussichten","url":"https://www.wallstreet-online.de/nachricht/19671363-kurs-schnellt-hoch-mtu-aero-engines-starkes-wachstum-optimistische-aussichten","feedId":"2","feedName":"Wallstreet Online","contentLength":120952,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Kurs schnellt hoch: MTU Aero Engines: Starkes Wachstum und optimistische Aussichten\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Kurs schnellt hoch: MTU Aero Engines: Starkes Wachstum und optimistische Aussichten\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T20:58:20.878Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"SDAX-Top-Performer: Vossloh &quot;hält, was es verspricht!&quot; Diese Zahlen können sich sehen lass","url":"https://www.wallstreet-online.de/nachricht/19671591-sdax-top-performer-vossloh-haelt-verspricht-zahlen","feedId":"2","feedName":"Wallstreet Online","contentLength":152334,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"SDAX-Top-Performer: Vossloh &quot;hält, was es verspricht!&quot; Diese Zahlen können sich sehen lassen\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"SDAX-Top-Performer: Vossloh &quot;hält, was es verspricht!&quot; Diese Zahlen können sich sehen lassen\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T20:58:23.076Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Wenn sich zwei streiten ......: Ist Amazon der lachende Dritte beim Streit zwischen Donald Trump und","url":"https://www.wallstreet-online.de/nachricht/19668393-streiten-amazon-lachende-streit-donald-trump-elon-musk","feedId":"2","feedName":"Wallstreet Online","contentLength":106926,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Wenn sich zwei streiten ......: Ist Amazon der lachende Dritte beim Streit zwischen Donald Trump und Elon Musk?\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Wenn sich zwei streiten ......: Ist Amazon der lachende Dritte beim Streit zwischen Donald Trump und Elon Musk?\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T20:58:46.293Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Nach übertroffenen Prognosen: Alphabet dreht ins Plus – Zahlen überzeugen nach anfänglicher Skepsis ","url":"https://www.wallstreet-online.de/nachricht/19669395-uebertroffenen-prognosen-alphabet-dreht-plus-zahlen-ueberzeugen-anfaenglicher-skepsis-doch","feedId":"2","feedName":"Wallstreet Online","contentLength":121790,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Nach übertroffenen Prognosen: Alphabet dreht ins Plus – Zahlen überzeugen nach anfänglicher Skepsis doch!\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Nach übertroffenen Prognosen: Alphabet dreht ins Plus – Zahlen überzeugen nach anfänglicher Skepsis doch!\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T20:59:08.791Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Lamb Weston: Eine Value-Qualitäts-Dividendenaktie mit 3,1 % Rendite im Warren-Buffett-Style","url":"https://www.wallstreet-online.de/nachricht/19666479-lamb-weston-value-qualitaets-dividendenaktie-3-1-rendite-warren-buffett-style","feedId":"2","feedName":"Wallstreet Online","contentLength":101525,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Lamb Weston: Eine Value-Qualitäts-Dividendenaktie mit 3,1 % Rendite im Warren-Buffett-Style\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Lamb Weston: Eine Value-Qualitäts-Dividendenaktie mit 3,1 % Rendite im Warren-Buffett-Style\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T20:59:08.792Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Enttäuschende Prognose: Schlechtes Omen? Dieser PayPal-Rivale bricht dramatisch ein!","url":"https://www.wallstreet-online.de/nachricht/19667970-enttaeuschende-prognose-schlechtes-omen-paypal-rivale-bricht-dramatisch-ein","feedId":"2","feedName":"Wallstreet Online","contentLength":120643,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Enttäuschende Prognose: Schlechtes Omen? Dieser PayPal-Rivale bricht dramatisch ein!\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Enttäuschende Prognose: Schlechtes Omen? Dieser PayPal-Rivale bricht dramatisch ein!\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T20:59:11.012Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Dividenden-Champion legt vor: Mehr Mobilfunkkunden als erwartet: AT&T übertrifft Gewinnprognosen","url":"https://www.wallstreet-online.de/nachricht/19667439-dividenden-champion-legt-vor-mobilfunkkunden-erwartet-at-t-uebertrifft-gewinnprognosen","feedId":"2","feedName":"Wallstreet Online","contentLength":152165,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Dividenden-Champion legt vor: Mehr Mobilfunkkunden als erwartet: AT&T übertrifft Gewinnprognosen\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Dividenden-Champion legt vor: Mehr Mobilfunkkunden als erwartet: AT&T übertrifft Gewinnprognosen\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T20:59:34.957Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Q2-Earnings bärenstark: Siemens-Energy-Konkurrent GE Vernova: Erwartungen gesprengt, Prognose angeho","url":"https://www.wallstreet-online.de/nachricht/19667112-q2-earnings-baerenstark-siemens-energy-konkurrent-ge-vernova-erwartungen-gesprengt-prognose-angehoben","feedId":"2","feedName":"Wallstreet Online","contentLength":134581,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Q2-Earnings bärenstark: Siemens-Energy-Konkurrent GE Vernova: Erwartungen gesprengt, Prognose angehoben\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Q2-Earnings bärenstark: Siemens-Energy-Konkurrent GE Vernova: Erwartungen gesprengt, Prognose angehoben\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T20:59:57.703Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Jahresziele angehoben: 56 Prozent in 3 Wochen: Diese SDax-Rakete zündet die nächste Rallye-Stufe","url":"https://www.wallstreet-online.de/nachricht/19666194-jahresziele-angehoben-56-prozent-3-wochen-sdax-rakete-zuendet-rallye-stufe","feedId":"2","feedName":"Wallstreet Online","contentLength":104877,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Jahresziele angehoben: 56 Prozent in 3 Wochen: Diese SDax-Rakete zündet die nächste Rallye-Stufe\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Jahresziele angehoben: 56 Prozent in 3 Wochen: Diese SDax-Rakete zündet die nächste Rallye-Stufe\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T20:59:57.703Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Schwache Kursreaktion: Solar-Aktie Enphase Energy: Bitte nicht schon wieder!","url":"https://www.wallstreet-online.de/nachricht/19665912-schwache-kursreaktion-solar-aktie-enphase-energy-bitte-wieder","feedId":"2","feedName":"Wallstreet Online","contentLength":121392,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Schwache Kursreaktion: Solar-Aktie Enphase Energy: Bitte nicht schon wieder!\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Schwache Kursreaktion: Solar-Aktie Enphase Energy: Bitte nicht schon wieder!\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T20:59:59.854Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Anschnallen, bitte: Donut-Aktie Krispy Kreme: Die Meme-Rallye geht weiter","url":"https://www.wallstreet-online.de/nachricht/19665915-anschnallen-bitte-donut-aktie-krispy-kreme-meme-rallye","feedId":"2","feedName":"Wallstreet Online","contentLength":105576,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Anschnallen, bitte: Donut-Aktie Krispy Kreme: Die Meme-Rallye geht weiter\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Anschnallen, bitte: Donut-Aktie Krispy Kreme: Die Meme-Rallye geht weiter\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T21:00:11.698Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Prognose angehoben: Thales: Rüstungsgeschäft beflügelt Wachstum, Aufträge sprudeln","url":"https://www.wallstreet-online.de/nachricht/19665825-prognose-angehoben-thales-ruestungsgeschaeft-befluegelt-wachstum-auftraege-sprudeln","feedId":"2","feedName":"Wallstreet Online","contentLength":118220,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Prognose angehoben: Thales: Rüstungsgeschäft beflügelt Wachstum, Aufträge sprudeln\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Prognose angehoben: Thales: Rüstungsgeschäft beflügelt Wachstum, Aufträge sprudeln\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T21:00:43.416Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Small Caps knallen: Europäische Nebenwerte glänzen – und könnten ihren Lauf fortsetzen, sagt Goldman","url":"https://www.wallstreet-online.de/nachricht/19663623-small-caps-knallen-europaeische-nebenwerte-glaenzen-lauf-fortsetzen-goldman","feedId":"2","feedName":"Wallstreet Online","contentLength":152383,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Small Caps knallen: Europäische Nebenwerte glänzen – und könnten ihren Lauf fortsetzen, sagt Goldman\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Small Caps knallen: Europäische Nebenwerte glänzen – und könnten ihren Lauf fortsetzen, sagt Goldman\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T21:00:59.737Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Dividendenwachstumswert: Darum dürfte es für diese Reichmacher-Aktie jetzt wieder aufwärts gehen!","url":"https://www.wallstreet-online.de/nachricht/19661169-dividendenwachstumswert-reichmacher-aktie-aufwaerts-gehen","feedId":"2","feedName":"Wallstreet Online","contentLength":122495,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Dividendenwachstumswert: Darum dürfte es für diese Reichmacher-Aktie jetzt wieder aufwärts gehen!\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Dividendenwachstumswert: Darum dürfte es für diese Reichmacher-Aktie jetzt wieder aufwärts gehen!\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T21:00:59.737Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Umsatzprognose verfehlt: 50-Prozent-Rallye vorbei? Philip Morris nach den Zahlen tiefrot","url":"https://www.wallstreet-online.de/nachricht/19663488-umsatzprognose-verfehlt-50-prozent-rallye-vorbei-philip-morris-zahlen-tiefrot","feedId":"2","feedName":"Wallstreet Online","contentLength":154633,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Umsatzprognose verfehlt: 50-Prozent-Rallye vorbei? Philip Morris nach den Zahlen tiefrot\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Umsatzprognose verfehlt: 50-Prozent-Rallye vorbei? Philip Morris nach den Zahlen tiefrot\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T21:01:01.882Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Buffetts Burggraben-Perle: Coke Zero wird zum Kassenschlager: Coca-Cola übertrifft die Erwartungen","url":"https://www.wallstreet-online.de/nachricht/19662288-buffetts-burggraben-perle-coke-zero-kassenschlager-coca-cola-uebertrifft-erwartungen","feedId":"2","feedName":"Wallstreet Online","contentLength":117452,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Buffetts Burggraben-Perle: Coke Zero wird zum Kassenschlager: Coca-Cola übertrifft die Erwartungen\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Buffetts Burggraben-Perle: Coke Zero wird zum Kassenschlager: Coca-Cola übertrifft die Erwartungen\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T21:01:27.316Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Rüstungsaktien: Lockheed Martin im Sturzflug: Milliardenschwere Abschreibungen schocken Anleger","url":"https://www.wallstreet-online.de/nachricht/19662291-ruestungsaktien-lockheed-martin-sturzflug-milliardenschwere-abschreibungen-schocken-anleger","feedId":"2","feedName":"Wallstreet Online","contentLength":124553,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Rüstungsaktien: Lockheed Martin im Sturzflug: Milliardenschwere Abschreibungen schocken Anleger\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Rüstungsaktien: Lockheed Martin im Sturzflug: Milliardenschwere Abschreibungen schocken Anleger\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T21:02:20.004Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Dividendenaktie: Gewinnsprung bei Munich Re – doch die Konkurrenz nimmt zu","url":"https://www.wallstreet-online.de/nachricht/19658757-dividendenaktie-gewinnsprung-munich-re-konkurrenz","feedId":"2","feedName":"Wallstreet Online","contentLength":118062,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Dividendenaktie: Gewinnsprung bei Munich Re – doch die Konkurrenz nimmt zu\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Dividendenaktie: Gewinnsprung bei Munich Re – doch die Konkurrenz nimmt zu\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T21:03:42.592Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Klar bullish: JPMorgan: Warum die Märkte trotz Unsicherheit durchstarten werden!","url":"https://www.wallstreet-online.de/nachricht/19650465-bullish-jpmorgan-maerkte-trotz-unsicherheit-durchstarten-werden","feedId":"2","feedName":"Wallstreet Online","contentLength":152363,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Klar bullish: JPMorgan: Warum die Märkte trotz Unsicherheit durchstarten werden!\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Klar bullish: JPMorgan: Warum die Märkte trotz Unsicherheit durchstarten werden!\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-24T21:04:24.203Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Großkapital 2025: 4 Value-Dividendenaktien, die Top-Investoren wie Warren Buffett für 2.444.282.341 ","url":"https://www.wallstreet-online.de/nachricht/19645104-grosskapital-2025-4-value-dividendenaktien-top-investoren-warren-buffett-2-444-282-341-us-dollar-gekauft","feedId":"2","feedName":"Wallstreet Online","contentLength":290400,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Großkapital 2025: 4 Value-Dividendenaktien, die Top-Investoren wie Warren Buffett für 2.444.282.341 US-Dollar gekauft haben\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Großkapital 2025: 4 Value-Dividendenaktien, die Top-Investoren wie Warren Buffett für 2.444.282.341 US-Dollar gekauft haben\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:477:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:540:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:116:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
