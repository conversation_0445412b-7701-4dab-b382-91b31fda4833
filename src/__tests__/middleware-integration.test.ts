/**
 * Middleware Integration Tests
 *
 * Tests the complete middleware behavior including headers,
 * responses, and rate limit enforcement across different
 * user scenarios and route types.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { NextRequest, NextResponse } from 'next/server';

// Mock console methods to avoid noise in tests
const mockConsoleLog = vi.fn();
const mockConsoleWarn = vi.fn();

beforeEach(() => {
  vi.clearAllMocks();
  global.console = {
    ...console,
    log: mockConsoleLog,
    warn: mockConsoleWarn,
  };
});

describe('Middleware Integration Tests', () => {
  beforeEach(() => {
    vi.useFakeTimers();
    vi.setSystemTime(new Date('2024-01-01T00:00:00Z'));
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('Rate Limit Headers', () => {
    it('should set correct rate limit headers for unauthenticated users', async () => {
      const request = new NextRequest('http://localhost:3000/admin', {
        headers: {
          'x-forwarded-for': '***********',
        },
      });

      // Mock the middleware logic
      const rateLimitStore = new Map();
      const RATE_LIMITS = {
        protected: { requests: 200, windowMs: 60 * 60 * 1000 },
      };

      const clientIP = '***********';
      const rateLimitKey = `protected_${clientIP}`;
      const rateLimit = RATE_LIMITS.protected;

      // Simulate first request
      rateLimitStore.set(rateLimitKey, {
        count: 1,
        resetTime: Date.now() + rateLimit.windowMs,
      });

      const response = NextResponse.next();
      const record = rateLimitStore.get(rateLimitKey);
      const remaining = Math.max(0, rateLimit.requests - record.count);

      response.headers.set('X-RateLimit-Limit', rateLimit.requests.toString());
      response.headers.set('X-RateLimit-Remaining', remaining.toString());
      response.headers.set('X-RateLimit-Category', 'protected');
      response.headers.set('X-RateLimit-Reset', record.resetTime.toString());

      expect(response.headers.get('X-RateLimit-Limit')).toBe('200');
      expect(response.headers.get('X-RateLimit-Remaining')).toBe('199');
      expect(response.headers.get('X-RateLimit-Category')).toBe('protected');
      expect(response.headers.get('X-RateLimit-Reset')).toBe(
        (Date.now() + 60 * 60 * 1000).toString()
      );
    });

    it('should set correct rate limit headers for authenticated admin users', async () => {
      const request = new NextRequest('http://localhost:3000/admin', {
        headers: {
          'x-forwarded-for': '***********',
          cookie: 'payload-token=abc123',
        },
      });

      // Mock the middleware logic
      const rateLimitStore = new Map();
      const RATE_LIMITS = {
        admin: { requests: 2000, windowMs: 60 * 60 * 1000 },
      };

      const clientIP = '***********';
      const rateLimitKey = `admin_${clientIP}`;
      const rateLimit = RATE_LIMITS.admin;

      // Simulate first request
      rateLimitStore.set(rateLimitKey, {
        count: 1,
        resetTime: Date.now() + rateLimit.windowMs,
      });

      const response = NextResponse.next();
      const record = rateLimitStore.get(rateLimitKey);
      const remaining = Math.max(0, rateLimit.requests - record.count);

      response.headers.set('X-RateLimit-Limit', rateLimit.requests.toString());
      response.headers.set('X-RateLimit-Remaining', remaining.toString());
      response.headers.set('X-RateLimit-Category', 'admin');
      response.headers.set('X-RateLimit-Reset', record.resetTime.toString());

      expect(response.headers.get('X-RateLimit-Limit')).toBe('2000');
      expect(response.headers.get('X-RateLimit-Remaining')).toBe('1999');
      expect(response.headers.get('X-RateLimit-Category')).toBe('admin');
    });
  });

  describe('Rate Limit Exceeded Responses', () => {
    it('should return appropriate error message for unauthenticated users', () => {
      const authenticated = false;
      const rateLimit = { requests: 200 };

      const response = new NextResponse(
        authenticated
          ? `Admin rate limit exceeded. You've made too many requests (${rateLimit.requests}/hour limit). Please wait before continuing your admin work.`
          : 'Too Many Requests',
        {
          status: 429,
          headers: {
            'Retry-After': '3600',
            'X-RateLimit-Limit': rateLimit.requests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': (Date.now() + 60 * 60 * 1000).toString(),
          },
        }
      );

      expect(response.status).toBe(429);
      expect(response.headers.get('Retry-After')).toBe('3600');
      expect(response.headers.get('X-RateLimit-Remaining')).toBe('0');
    });

    it('should return helpful error message for authenticated admin users', () => {
      const authenticated = true;
      const rateLimit = { requests: 2000 };

      const response = new NextResponse(
        authenticated
          ? `Admin rate limit exceeded. You've made too many requests (${rateLimit.requests}/hour limit). Please wait before continuing your admin work.`
          : 'Too Many Requests',
        {
          status: 429,
          headers: {
            'Retry-After': '3600',
            'X-RateLimit-Limit': rateLimit.requests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': (Date.now() + 60 * 60 * 1000).toString(),
          },
        }
      );

      expect(response.status).toBe(429);
      expect(response.statusText).toBe('');

      // Extract response text
      const responseText = `Admin rate limit exceeded. You've made too many requests (${rateLimit.requests}/hour limit). Please wait before continuing your admin work.`;
      expect(responseText).toContain('Admin rate limit exceeded');
      expect(responseText).toContain('2000/hour limit');
    });
  });

  describe('Logging Behavior', () => {
    it('should log admin panel access with authentication status', () => {
      const pathname = '/admin';
      const clientIP = '***********';
      const now = new Date().toISOString();

      // Simulate authenticated user logging
      const authenticated = true;
      if (pathname.startsWith('/admin') && !pathname.includes('/api/')) {
        const authStatus = authenticated ? 'authenticated' : 'unauthenticated';
        console.log(
          `Admin panel access: ${authStatus} user ${clientIP} -> ${pathname} at ${now}`
        );
      }

      expect(mockConsoleLog).toHaveBeenCalledWith(
        `Admin panel access: authenticated user ${clientIP} -> ${pathname} at ${now}`
      );
    });

    it('should log rate limit exceeded warnings with user type', () => {
      const clientIP = '***********';
      const pathname = '/admin';
      const now = new Date().toISOString();
      const authenticated = true;
      const rateLimit = { requests: 2000 };

      const userType = authenticated
        ? 'authenticated admin'
        : 'unauthenticated';
      console.warn(
        `Rate limit exceeded for ${userType} user ${clientIP} on ${pathname} at ${now} (limit: ${rateLimit.requests}/hour)`
      );

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        `Rate limit exceeded for authenticated admin user ${clientIP} on ${pathname} at ${now} (limit: 2000/hour)`
      );
    });
  });

  describe('Route Classification', () => {
    const testCases = [
      {
        path: '/api/health',
        expectedCategory: 'health',
        authenticated: false,
        description: 'health endpoint',
      },
      {
        path: '/api/graphql',
        expectedCategory: 'public',
        authenticated: false,
        description: 'public GraphQL endpoint',
      },
      {
        path: '/admin',
        expectedCategory: 'admin',
        authenticated: true,
        description: 'authenticated admin panel',
      },
      {
        path: '/admin',
        expectedCategory: 'protected',
        authenticated: false,
        description: 'unauthenticated admin panel',
      },
      {
        path: '/api/articles',
        expectedCategory: 'admin',
        authenticated: true,
        description: 'authenticated API endpoint',
      },
      {
        path: '/api/articles',
        expectedCategory: 'protected',
        authenticated: false,
        description: 'unauthenticated API endpoint',
      },
    ];

    testCases.forEach(
      ({ path, expectedCategory, authenticated, description }) => {
        it(`should classify ${description} as ${expectedCategory}`, () => {
          const PUBLIC_ROUTES = [
            '/api/health',
            '/api/firecrawl-alerts',
            '/api/graphql',
            '/api/preview',
            '/api/disable-draft',
          ];

          const isPublicRoute = (pathname: string): boolean => {
            return PUBLIC_ROUTES.some(route => pathname.startsWith(route));
          };

          let rateLimitCategory: string;

          if (path.startsWith('/api/health')) {
            rateLimitCategory = 'health';
          } else if (isPublicRoute(path)) {
            rateLimitCategory = 'public';
          } else if (
            authenticated &&
            (path.startsWith('/admin') || path.startsWith('/api/'))
          ) {
            rateLimitCategory = 'admin';
          } else {
            rateLimitCategory = 'protected';
          }

          expect(rateLimitCategory).toBe(expectedCategory);
        });
      }
    );
  });

  describe('Security Headers', () => {
    it('should add security headers for API routes', () => {
      const response = NextResponse.next();

      // Mock adding API security headers
      response.headers.set(
        'Cache-Control',
        'no-cache, no-store, must-revalidate'
      );
      response.headers.set('Pragma', 'no-cache');
      response.headers.set('Expires', '0');
      response.headers.set('X-Content-Type-Options', 'nosniff');
      response.headers.set('X-Frame-Options', 'DENY');

      expect(response.headers.get('Cache-Control')).toBe(
        'no-cache, no-store, must-revalidate'
      );
      expect(response.headers.get('X-Content-Type-Options')).toBe('nosniff');
      expect(response.headers.get('X-Frame-Options')).toBe('DENY');
    });

    it('should add special headers for preview routes', () => {
      const response = NextResponse.next();

      // Mock adding preview security headers
      response.headers.set('X-Robots-Tag', 'noindex, nofollow');
      response.headers.set(
        'Cache-Control',
        'no-cache, no-store, must-revalidate'
      );

      expect(response.headers.get('X-Robots-Tag')).toBe('noindex, nofollow');
      expect(response.headers.get('Cache-Control')).toBe(
        'no-cache, no-store, must-revalidate'
      );
    });
  });

  describe('Performance Considerations', () => {
    it('should handle cleanup of expired rate limit entries', () => {
      const rateLimitStore = new Map();
      const now = Date.now();

      // Add some entries
      rateLimitStore.set('key1', { count: 1, resetTime: now - 1000 }); // Expired
      rateLimitStore.set('key2', { count: 1, resetTime: now + 1000 }); // Valid
      rateLimitStore.set('key3', { count: 1, resetTime: now - 2000 }); // Expired

      // Simulate cleanup process
      for (const [key, record] of rateLimitStore.entries()) {
        if (now > record.resetTime) {
          rateLimitStore.delete(key);
        }
      }

      expect(rateLimitStore.has('key1')).toBe(false);
      expect(rateLimitStore.has('key2')).toBe(true);
      expect(rateLimitStore.has('key3')).toBe(false);
      expect(rateLimitStore.size).toBe(1);
    });

    it('should handle high request volumes efficiently', () => {
      const rateLimitStore = new Map();
      const limit = { requests: 100, windowMs: 60000 };

      // Simulate 50 requests for the same key
      for (let i = 0; i < 50; i++) {
        const key = 'high-volume-key';
        const now = Date.now();
        const record = rateLimitStore.get(key);

        if (!record || now > record.resetTime) {
          rateLimitStore.set(key, {
            count: 1,
            resetTime: now + limit.windowMs,
          });
        } else if (record.count < limit.requests) {
          record.count++;
        }
      }

      const finalRecord = rateLimitStore.get('high-volume-key');
      expect(finalRecord?.count).toBe(50);
      expect(rateLimitStore.size).toBe(1);
    });
  });
});
