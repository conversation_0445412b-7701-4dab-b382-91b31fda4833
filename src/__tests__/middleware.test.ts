/**
 * Middleware Rate Limiting Tests
 *
 * Tests the rate limiting functionality in the Next.js middleware
 * including authentication detection, rate limit enforcement,
 * and proper header setting.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { NextRequest, NextResponse } from 'next/server';

// Mock the middleware functions
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Security configuration - reasonable limits for a news website
const RATE_LIMITS = {
  public: { requests: 1000, windowMs: 60 * 60 * 1000 }, // 1000 requests per hour (generous for news readers)
  protected: { requests: 200, windowMs: 60 * 60 * 1000 }, // 200 requests per hour for authenticated users
  admin: { requests: 2000, windowMs: 60 * 60 * 1000 }, // 2000 requests per hour for admin users (very generous for active admin work)
  health: { requests: 500, windowMs: 60 * 60 * 1000 }, // 500 requests per hour for monitoring
};

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  '/api/health', // System monitoring for uptime checks
  '/api/firecrawl-alerts', // Service monitoring (read-only)
  // PayloadCMS routes have their own auth
  '/api/graphql',
  '/api/preview',
  '/api/disable-draft',
];

// Utility functions from middleware
function getClientIP(request: NextRequest): string {
  return (
    request.headers.get('x-forwarded-for')?.split(',')[0] ||
    request.headers.get('x-real-ip') ||
    'unknown'
  );
}

function isAuthenticated(request: NextRequest): boolean {
  // Check for PayloadCMS authentication token
  const token = request.cookies.get('payload-token')?.value;
  return !!token;
}

function isPublicRoute(pathname: string): boolean {
  return PUBLIC_ROUTES.some(route => pathname.startsWith(route));
}

function checkRateLimit(
  key: string,
  limit: { requests: number; windowMs: number }
): boolean {
  const now = Date.now();
  const record = rateLimitStore.get(key);

  if (!record || now > record.resetTime) {
    // Reset or create new record
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + limit.windowMs,
    });
    return true;
  }

  if (record.count >= limit.requests) {
    return false; // Rate limit exceeded
  }

  record.count++;
  return true;
}

describe('Middleware Rate Limiting', () => {
  beforeEach(() => {
    rateLimitStore.clear();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('getClientIP', () => {
    it('should extract IP from x-forwarded-for header', () => {
      const headers = new Headers();
      headers.set('x-forwarded-for', '***********, ********');

      const request = new NextRequest('http://localhost:3000/', {
        headers,
      });

      expect(getClientIP(request)).toBe('***********');
    });

    it('should extract IP from x-real-ip header when x-forwarded-for is not present', () => {
      const headers = new Headers();
      headers.set('x-real-ip', '***********');

      const request = new NextRequest('http://localhost:3000/', {
        headers,
      });

      expect(getClientIP(request)).toBe('***********');
    });

    it('should return "unknown" when no IP headers are present', () => {
      const request = new NextRequest('http://localhost:3000/');
      expect(getClientIP(request)).toBe('unknown');
    });
  });

  describe('isAuthenticated', () => {
    it('should return true when payload-token cookie is present', () => {
      const headers = new Headers();
      headers.set('cookie', 'payload-token=abc123; other-cookie=value');

      const request = new NextRequest('http://localhost:3000/', {
        headers,
      });

      expect(isAuthenticated(request)).toBe(true);
    });

    it('should return false when payload-token cookie is not present', () => {
      const headers = new Headers();
      headers.set('cookie', 'other-cookie=value');

      const request = new NextRequest('http://localhost:3000/', {
        headers,
      });

      expect(isAuthenticated(request)).toBe(false);
    });

    it('should return false when no cookies are present', () => {
      const request = new NextRequest('http://localhost:3000/');
      expect(isAuthenticated(request)).toBe(false);
    });
  });

  describe('isPublicRoute', () => {
    it('should return true for health API route', () => {
      expect(isPublicRoute('/api/health')).toBe(true);
    });

    it('should return true for firecrawl-alerts route', () => {
      expect(isPublicRoute('/api/firecrawl-alerts')).toBe(true);
    });

    it('should return true for graphql route', () => {
      expect(isPublicRoute('/api/graphql')).toBe(true);
    });

    it('should return false for admin route', () => {
      expect(isPublicRoute('/admin')).toBe(false);
    });

    it('should return false for protected API route', () => {
      expect(isPublicRoute('/api/articles')).toBe(false);
    });
  });

  describe('checkRateLimit', () => {
    const testLimit = { requests: 3, windowMs: 1000 };

    it('should allow first request', () => {
      expect(checkRateLimit('test-key', testLimit)).toBe(true);
    });

    it('should allow requests within limit', () => {
      expect(checkRateLimit('test-key', testLimit)).toBe(true);
      expect(checkRateLimit('test-key', testLimit)).toBe(true);
      expect(checkRateLimit('test-key', testLimit)).toBe(true);
    });

    it('should reject requests when limit exceeded', () => {
      // Use up the limit
      expect(checkRateLimit('test-key', testLimit)).toBe(true);
      expect(checkRateLimit('test-key', testLimit)).toBe(true);
      expect(checkRateLimit('test-key', testLimit)).toBe(true);

      // This should be rejected
      expect(checkRateLimit('test-key', testLimit)).toBe(false);
    });

    it('should reset limit after window expires', () => {
      // Use up the limit
      expect(checkRateLimit('test-key', testLimit)).toBe(true);
      expect(checkRateLimit('test-key', testLimit)).toBe(true);
      expect(checkRateLimit('test-key', testLimit)).toBe(true);
      expect(checkRateLimit('test-key', testLimit)).toBe(false);

      // Advance time past the window
      vi.advanceTimersByTime(1001);

      // Should be allowed again
      expect(checkRateLimit('test-key', testLimit)).toBe(true);
    });

    it('should handle different keys independently', () => {
      // Use up limit for key1
      expect(checkRateLimit('key1', testLimit)).toBe(true);
      expect(checkRateLimit('key1', testLimit)).toBe(true);
      expect(checkRateLimit('key1', testLimit)).toBe(true);
      expect(checkRateLimit('key1', testLimit)).toBe(false);

      // key2 should still be allowed
      expect(checkRateLimit('key2', testLimit)).toBe(true);
      expect(checkRateLimit('key2', testLimit)).toBe(true);
    });
  });

  describe('Rate Limit Categories', () => {
    it('should apply health limits to health endpoints', () => {
      const headers = new Headers();
      headers.set('x-forwarded-for', '***********');

      const request = new NextRequest('http://localhost:3000/api/health', {
        headers,
      });

      const clientIP = getClientIP(request);
      const pathname = '/api/health';
      const authenticated = isAuthenticated(request);

      let rateLimitKey: string;
      let rateLimit: { requests: number; windowMs: number };

      if (pathname.startsWith('/api/health')) {
        rateLimitKey = `health_${clientIP}`;
        rateLimit = RATE_LIMITS.health;
      } else if (isPublicRoute(pathname)) {
        rateLimitKey = `public_${clientIP}`;
        rateLimit = RATE_LIMITS.public;
      } else if (
        authenticated &&
        (pathname.startsWith('/admin') || pathname.startsWith('/api/'))
      ) {
        rateLimitKey = `admin_${clientIP}`;
        rateLimit = RATE_LIMITS.admin;
      } else {
        rateLimitKey = `protected_${clientIP}`;
        rateLimit = RATE_LIMITS.protected;
      }

      expect(rateLimitKey).toBe('health_***********');
      expect(rateLimit).toEqual(RATE_LIMITS.health);
    });

    it('should apply public limits to public routes', () => {
      const headers = new Headers();
      headers.set('x-forwarded-for', '***********');

      const request = new NextRequest('http://localhost:3000/api/graphql', {
        headers,
      });

      const clientIP = getClientIP(request);
      const pathname = '/api/graphql';
      const authenticated = isAuthenticated(request);

      let rateLimitKey: string;
      let rateLimit: { requests: number; windowMs: number };

      if (pathname.startsWith('/api/health')) {
        rateLimitKey = `health_${clientIP}`;
        rateLimit = RATE_LIMITS.health;
      } else if (isPublicRoute(pathname)) {
        rateLimitKey = `public_${clientIP}`;
        rateLimit = RATE_LIMITS.public;
      } else if (
        authenticated &&
        (pathname.startsWith('/admin') || pathname.startsWith('/api/'))
      ) {
        rateLimitKey = `admin_${clientIP}`;
        rateLimit = RATE_LIMITS.admin;
      } else {
        rateLimitKey = `protected_${clientIP}`;
        rateLimit = RATE_LIMITS.protected;
      }

      expect(rateLimitKey).toBe('public_***********');
      expect(rateLimit).toEqual(RATE_LIMITS.public);
    });

    it('should apply admin limits to authenticated admin routes', () => {
      const headers = new Headers();
      headers.set('x-forwarded-for', '***********');
      headers.set('cookie', 'payload-token=abc123');

      const request = new NextRequest('http://localhost:3000/admin', {
        headers,
      });

      const clientIP = getClientIP(request);
      const pathname = '/admin';
      const authenticated = isAuthenticated(request);

      let rateLimitKey: string;
      let rateLimit: { requests: number; windowMs: number };

      if (pathname.startsWith('/api/health')) {
        rateLimitKey = `health_${clientIP}`;
        rateLimit = RATE_LIMITS.health;
      } else if (isPublicRoute(pathname)) {
        rateLimitKey = `public_${clientIP}`;
        rateLimit = RATE_LIMITS.public;
      } else if (
        authenticated &&
        (pathname.startsWith('/admin') || pathname.startsWith('/api/'))
      ) {
        rateLimitKey = `admin_${clientIP}`;
        rateLimit = RATE_LIMITS.admin;
      } else {
        rateLimitKey = `protected_${clientIP}`;
        rateLimit = RATE_LIMITS.protected;
      }

      expect(rateLimitKey).toBe('admin_***********');
      expect(rateLimit).toEqual(RATE_LIMITS.admin);
      expect(rateLimit.requests).toBe(2000); // Generous admin limit
    });

    it('should apply protected limits to unauthenticated protected routes', () => {
      const headers = new Headers();
      headers.set('x-forwarded-for', '***********');

      const request = new NextRequest('http://localhost:3000/admin', {
        headers,
      });

      const clientIP = getClientIP(request);
      const pathname = '/admin';
      const authenticated = isAuthenticated(request);

      let rateLimitKey: string;
      let rateLimit: { requests: number; windowMs: number };

      if (pathname.startsWith('/api/health')) {
        rateLimitKey = `health_${clientIP}`;
        rateLimit = RATE_LIMITS.health;
      } else if (isPublicRoute(pathname)) {
        rateLimitKey = `public_${clientIP}`;
        rateLimit = RATE_LIMITS.public;
      } else if (
        authenticated &&
        (pathname.startsWith('/admin') || pathname.startsWith('/api/'))
      ) {
        rateLimitKey = `admin_${clientIP}`;
        rateLimit = RATE_LIMITS.admin;
      } else {
        rateLimitKey = `protected_${clientIP}`;
        rateLimit = RATE_LIMITS.protected;
      }

      expect(rateLimitKey).toBe('protected_***********');
      expect(rateLimit).toEqual(RATE_LIMITS.protected);
      expect(rateLimit.requests).toBe(200); // Restrictive for unauthenticated
    });
  });

  describe('Edge Cases', () => {
    it('should handle malformed cookies gracefully', () => {
      const headers = new Headers();
      headers.set('cookie', 'malformed-cookie');

      const request = new NextRequest('http://localhost:3000/', {
        headers,
      });

      expect(isAuthenticated(request)).toBe(false);
    });

    it('should handle empty IP headers', () => {
      const headers = new Headers();
      headers.set('x-forwarded-for', '');
      headers.set('x-real-ip', '');

      const request = new NextRequest('http://localhost:3000/', {
        headers,
      });

      expect(getClientIP(request)).toBe('unknown');
    });

    it('should handle multiple commas in x-forwarded-for', () => {
      const headers = new Headers();
      headers.set('x-forwarded-for', '***********, , ********, ');

      const request = new NextRequest('http://localhost:3000/', {
        headers,
      });

      expect(getClientIP(request)).toBe('***********');
    });
  });
});
