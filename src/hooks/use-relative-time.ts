/**
 * Auto-refreshing relative time hook
 * Automatically updates relative time displays for recent articles
 */

import { useState, useEffect } from 'react';
import { getRelativeTime, isRecentDate } from '@/lib/utils/relative-time';

/**
 * Hook that provides auto-updating relative time with intelligent refresh intervals
 * @param date Date to display relative time for
 * @param locale Language locale
 * @returns Current relative time string that updates automatically
 */
export function useRelativeTime(
  date: string | Date,
  locale: 'en' | 'de' = 'en'
): string {
  const [relativeTime, setRelativeTime] = useState(() =>
    getRelativeTime(date, locale)
  );

  useEffect(() => {
    // Update immediately when date or locale changes
    setRelativeTime(getRelativeTime(date, locale));

    // Only set up auto-refresh for recent dates
    if (!isRecentDate(date)) {
      return;
    }

    // Smart refresh intervals:
    // - First 5 minutes: update every 30 seconds
    // - Next hour: update every 5 minutes
    // - Next 24 hours: update every 30 minutes
    const getRefreshInterval = () => {
      try {
        const now = new Date();
        const past = new Date(date);
        const diffInMinutes = (now.getTime() - past.getTime()) / (1000 * 60);

        if (diffInMinutes < 5) {
          return 30 * 1000; // 30 seconds
        } else if (diffInMinutes < 60) {
          return 5 * 60 * 1000; // 5 minutes
        } else {
          return 30 * 60 * 1000; // 30 minutes
        }
      } catch {
        return 5 * 60 * 1000; // Default to 5 minutes
      }
    };

    const interval = setInterval(() => {
      const newRelativeTime = getRelativeTime(date, locale);
      setRelativeTime(newRelativeTime);

      // Stop updating if date is no longer recent
      if (!isRecentDate(date)) {
        clearInterval(interval);
      }
    }, getRefreshInterval());

    return () => clearInterval(interval);
  }, [date, locale]);

  return relativeTime;
}

/**
 * Optimized version that only updates when the relative time string actually changes
 * Prevents unnecessary re-renders
 */
export function useRelativeTimeOptimized(
  date: string | Date,
  locale: 'en' | 'de' = 'en'
): string {
  const [relativeTime, setRelativeTime] = useState(() =>
    getRelativeTime(date, locale)
  );

  useEffect(() => {
    let mounted = true;

    const updateRelativeTime = () => {
      if (!mounted) return;

      const newRelativeTime = getRelativeTime(date, locale);

      // Only update state if the string actually changed
      setRelativeTime(current => {
        if (current !== newRelativeTime) {
          return newRelativeTime;
        }
        return current;
      });
    };

    // Update immediately
    updateRelativeTime();

    // Only set up auto-refresh for recent dates
    if (!isRecentDate(date)) {
      return () => {
        mounted = false;
      };
    }

    // Use requestAnimationFrame for smooth updates
    let animationId: number;
    let timeoutId: NodeJS.Timeout;

    const scheduleUpdate = () => {
      if (!mounted) return;

      const interval = (() => {
        try {
          const now = new Date();
          const past = new Date(date);
          const diffInMinutes = (now.getTime() - past.getTime()) / (1000 * 60);

          if (diffInMinutes < 5) return 30 * 1000; // 30 seconds
          if (diffInMinutes < 60) return 5 * 60 * 1000; // 5 minutes
          return 30 * 60 * 1000; // 30 minutes
        } catch {
          return 5 * 60 * 1000; // Default to 5 minutes
        }
      })();

      timeoutId = setTimeout(() => {
        animationId = requestAnimationFrame(() => {
          updateRelativeTime();
          scheduleUpdate(); // Schedule next update
        });
      }, interval);
    };

    scheduleUpdate();

    return () => {
      mounted = false;
      if (timeoutId) clearTimeout(timeoutId);
      if (animationId) cancelAnimationFrame(animationId);
    };
  }, [date, locale]);

  return relativeTime;
}
