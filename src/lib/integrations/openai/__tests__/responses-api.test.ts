/**
 * OpenAI Responses API Test Suite
 * Tests for structured output functionality and performance
 *
 * <AUTHOR> Blick Development Team
 * @created 2025-06-29
 * @sprint Sprint 2: OpenAI Responses API Upgrade
 */

import { describe, test, expect, vi, beforeEach } from 'vitest';
import {
  translateContent,
  enhanceContent,
  optimizeTitle,
  getPerformanceStats,
  healthCheck,
} from '../responses-api-client';

// Mock OpenAI for testing
vi.mock('openai');

describe('OpenAI Responses API', () => {
  const mockGermanTitle =
    'DAX steigt um 2% - Deutsche Bank profitiert von Zinswende';
  const mockGermanContent = `
    Die Deutsche Börse verzeichnete heute einen starken Handelstag. Der DAX stieg um 2% auf 15.500 Punkte.
    
    Deutsche Bank Aktien stiegen um 3,5% nach positiven Analystenbewertungen. Die Zinswende der EZB 
    könnte deutschen Banken zugutekommen.
    
    Weitere wichtige Entwicklungen:
    - BMW Aktien +1,8%
    - SAP Aktien +2,1% 
    - Siemens Aktien +1,5%
    
    Analysten sehen positive Aussichten für den deutschen Markt im Q4 2024.
  `;

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
  });

  describe('Relevance Analysis', () => {
    it('should analyze Canadian market relevance', async () => {
      // Mock successful API response
      const mockResponse = {
        relevanceScore: 75,
        reasoning:
          'German banking sector affects global markets including Canada',
        keyFactors: ['Banking sector', 'Interest rates', 'Global markets'],
        category: 'high' as const,
        keywords: ['DAX', 'Deutsche Bank', 'interest rates'],
        confidence: 85,
      };

      // Mock the OpenAI client response
      const mockOpenAI = require('openai');
      mockOpenAI.prototype.beta = {
        chat: {
          completions: {
            parse: jest.fn().mockResolvedValue({
              choices: [{ message: { parsed: mockResponse } }],
              usage: { total_tokens: 150 },
            }),
          },
        },
      };

      const result = await analyzeRelevance(mockGermanTitle, mockGermanContent);

      expect(result.relevanceScore).toBe(75);
      expect(result.category).toBe('high');
      expect(result.keyFactors).toContain('Banking sector');
    });

    it('should handle low relevance content', async () => {
      const mockResponse = {
        relevanceScore: 15,
        reasoning: 'Local German news with minimal Canadian impact',
        keyFactors: ['Local market'],
        category: 'low' as const,
        keywords: ['local', 'german'],
        confidence: 90,
      };

      const mockOpenAI = require('openai');
      mockOpenAI.prototype.beta = {
        chat: {
          completions: {
            parse: jest.fn().mockResolvedValue({
              choices: [{ message: { parsed: mockResponse } }],
              usage: { total_tokens: 100 },
            }),
          },
        },
      };

      const result = await analyzeRelevance(
        'Local German News',
        'Local content'
      );

      expect(result.relevanceScore).toBe(15);
      expect(result.category).toBe('low');
    });
  });

  describe('Content Translation', () => {
    it('should translate German content to English', async () => {
      const mockResponse = {
        translatedTitle:
          'DAX Rises 2% - Deutsche Bank Benefits from Interest Rate Shift',
        translatedContent:
          'The German stock exchange recorded a strong trading day...',
        summary: 'German markets rose with banking sector leading gains.',
        keyPoints: [
          'DAX up 2%',
          'Deutsche Bank gains 3.5%',
          'ECB rate policy impact',
        ],
        translationQuality: 'excellent' as const,
        preservedStructure: true,
        wordCount: 150,
      };

      const mockOpenAI = require('openai');
      mockOpenAI.prototype.beta = {
        chat: {
          completions: {
            parse: jest.fn().mockResolvedValue({
              choices: [{ message: { parsed: mockResponse } }],
              usage: { total_tokens: 200 },
            }),
          },
        },
      };

      const result = await translateContent(mockGermanTitle, mockGermanContent);

      expect(result.translatedTitle).toContain('DAX Rises');
      expect(result.translationQuality).toBe('excellent');
      expect(result.preservedStructure).toBe(true);
    });
  });

  describe('Content Enhancement', () => {
    it('should enhance German content for Canadian market', async () => {
      const mockResponse = {
        enhancedTitle:
          'DAX steigt um 2% - Auswirkungen auf kanadische Investoren',
        enhancedContent: 'Enhanced German content with Canadian context...',
        summary:
          'German market gains with implications for Canadian investors.',
        keyInsights: ['Banking sector strength', 'Global market correlation'],
        marketImplications: 'Positive for Canadian bank stocks',
        enhancementQuality: 'excellent' as const,
        canadianContext: 'Similar trends expected in TSX banking sector',
        addedValue: ['Canadian market context', 'Investment implications'],
      };

      const mockOpenAI = require('openai');
      mockOpenAI.prototype.beta = {
        chat: {
          completions: {
            parse: jest.fn().mockResolvedValue({
              choices: [{ message: { parsed: mockResponse } }],
              usage: { total_tokens: 250 },
            }),
          },
        },
      };

      const result = await enhanceContent(
        mockGermanTitle,
        mockGermanContent,
        75
      );

      expect(result.enhancedTitle).toContain('kanadische');
      expect(result.enhancementQuality).toBe('excellent');
      expect(result.addedValue).toContain('Canadian market context');
    });
  });

  describe('Title Optimization', () => {
    it('should optimize German titles', async () => {
      const mockResponse = {
        cleanedTitle:
          'DAX steigt um 2 Prozent - Deutsche Bank profitiert von Zinswende',
        seoOptimized: 'DAX steigt 2% - Deutsche Bank Zinswende Gewinn',
        charactersCleaned: ['%'],
        optimization: {
          removedEmojis: false,
          removedSpecialChars: true,
          improvedReadability: true,
          seoScore: 85,
          lengthOptimal: true,
        },
        suggestions: ['Consider adding year for context'],
        originalLength: 58,
        optimizedLength: 45,
      };

      const mockOpenAI = require('openai');
      mockOpenAI.prototype.beta = {
        chat: {
          completions: {
            parse: jest.fn().mockResolvedValue({
              choices: [{ message: { parsed: mockResponse } }],
              usage: { total_tokens: 80 },
            }),
          },
        },
      };

      const result = await optimizeTitle(mockGermanTitle, 'de');

      expect(result.optimization.seoScore).toBe(85);
      expect(result.optimization.lengthOptimal).toBe(true);
    });
  });

  describe('Financial Content Processing', () => {
    it('should process financial content comprehensively', async () => {
      const mockResponse = {
        processedContent: {
          title: 'DAX Gains 2% as Banking Sector Leads',
          content: 'Processed financial content...',
          summary: 'German markets showed strong performance',
          keyPoints: ['DAX up 2%', 'Banking sector strength'],
        },
        analysis: {
          relevanceScore: 80,
          category: 'markets' as const,
          sentiment: 'positive' as const,
          complexity: 'intermediate' as const,
        },
        financialData: {
          stockPrices: [
            { symbol: 'DAX', price: '15500', change: '+2%' },
            { symbol: 'DBK', price: '12.50', change: '+3.5%' },
          ],
          marketIndices: ['DAX'],
          currencies: ['EUR'],
        },
        metadata: {
          language: 'de',
          wordCount: 150,
          readingTime: 1,
          tags: ['markets', 'banking', 'germany'],
        },
      };

      const mockOpenAI = require('openai');
      mockOpenAI.prototype.beta = {
        chat: {
          completions: {
            parse: jest.fn().mockResolvedValue({
              choices: [{ message: { parsed: mockResponse } }],
              usage: { total_tokens: 300 },
            }),
          },
        },
      };

      const result = await processFinancialContent(
        mockGermanTitle,
        mockGermanContent
      );

      expect(result.analysis.relevanceScore).toBe(80);
      expect(result.analysis.category).toBe('markets');
      expect(result.financialData?.stockPrices).toHaveLength(2);
    });
  });

  describe('Performance Monitoring', () => {
    it('should track performance metrics', () => {
      const stats = getPerformanceStats();

      expect(stats).toHaveProperty('totalCalls');
      expect(stats).toHaveProperty('successRate');
      expect(stats).toHaveProperty('averageLatency');
      expect(stats).toHaveProperty('totalTokens');
      expect(stats).toHaveProperty('estimatedCost');
    });

    it('should perform health checks', async () => {
      const mockResponse = {
        cleanedTitle: 'Test Title for Health Check',
        seoOptimized: 'Test Title Health Check',
        charactersCleaned: [],
        optimization: {
          removedEmojis: false,
          removedSpecialChars: false,
          improvedReadability: true,
          seoScore: 75,
          lengthOptimal: true,
        },
        suggestions: [],
        originalLength: 25,
        optimizedLength: 25,
      };

      const mockOpenAI = require('openai');
      mockOpenAI.prototype.beta = {
        chat: {
          completions: {
            parse: jest.fn().mockResolvedValue({
              choices: [{ message: { parsed: mockResponse } }],
              usage: { total_tokens: 50 },
            }),
          },
        },
      };

      const health = await healthCheck();

      expect(health).toHaveProperty('status');
      expect(health).toHaveProperty('details');
      expect(['healthy', 'degraded', 'unhealthy']).toContain(health.status);
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      const mockOpenAI = require('openai');
      mockOpenAI.prototype.beta = {
        chat: {
          completions: {
            parse: jest.fn().mockRejectedValue(new Error('API Error')),
          },
        },
      };

      await expect(
        analyzeRelevance(mockGermanTitle, mockGermanContent)
      ).rejects.toThrow('API Error');
    });

    it('should handle parsing errors', async () => {
      const mockOpenAI = require('openai');
      mockOpenAI.prototype.beta = {
        chat: {
          completions: {
            parse: jest.fn().mockResolvedValue({
              choices: [{ message: { parsed: null } }],
              usage: { total_tokens: 100 },
            }),
          },
        },
      };

      await expect(
        analyzeRelevance(mockGermanTitle, mockGermanContent)
      ).rejects.toThrow('Failed to parse relevance analysis response');
    });
  });
});
