/**
 * OpenAI Rate Limiter
 * Simple queue-based rate limiting for OpenAI API calls to prevent 429 errors
 *
 * <AUTHOR> Blick Development Team
 * @created 2025-01-24
 */

interface QueuedRequest {
  id: string;
  execute: () => Promise<any>;
  resolve: (value: any) => void;
  reject: (error: any) => void;
  timestamp: number;
}

class OpenAIRateLimiter {
  private queue: QueuedRequest[] = [];
  private processing = false;
  private lastRequestTime = 0;
  private requestCount = 0;
  private windowStart = Date.now();

  // Rate limiting configuration - optimized for OpenAI's actual limits
  private readonly MIN_DELAY_MS = 200; // Minimum 200ms between requests (much faster)
  private readonly MAX_REQUESTS_PER_MINUTE = 100; // More realistic limit for paid accounts
  private readonly WINDOW_MS = 60 * 1000; // 1 minute window

  /**
   * Add a request to the rate-limited queue
   */
  async enqueue<T>(requestId: string, requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      const queuedRequest: QueuedRequest = {
        id: requestId,
        execute: requestFn,
        resolve,
        reject,
        timestamp: Date.now(),
      };

      this.queue.push(queuedRequest);
      console.log(
        `🔄 [Rate Limiter] Queued request: ${requestId} (queue size: ${this.queue.length})`
      );

      this.processQueue();
    });
  }

  /**
   * Process the queue with rate limiting
   */
  private async processQueue(): Promise<void> {
    if (this.processing || this.queue.length === 0) {
      return;
    }

    this.processing = true;

    while (this.queue.length > 0) {
      const now = Date.now();

      // Reset window if needed
      if (now - this.windowStart >= this.WINDOW_MS) {
        this.requestCount = 0;
        this.windowStart = now;
      }

      // Check if we've hit the rate limit
      if (this.requestCount >= this.MAX_REQUESTS_PER_MINUTE) {
        const waitTime = this.WINDOW_MS - (now - this.windowStart);
        console.log(
          `⏳ [Rate Limiter] Rate limit reached, waiting ${Math.ceil(waitTime / 1000)}s...`
        );
        await this.sleep(waitTime);
        continue;
      }

      // Ensure minimum delay between requests
      const timeSinceLastRequest = now - this.lastRequestTime;
      if (timeSinceLastRequest < this.MIN_DELAY_MS) {
        const delayNeeded = this.MIN_DELAY_MS - timeSinceLastRequest;
        console.log(
          `⏳ [Rate Limiter] Enforcing ${delayNeeded}ms delay between requests...`
        );
        await this.sleep(delayNeeded);
      }

      // Process the next request
      const request = this.queue.shift()!;

      try {
        console.log(`🚀 [Rate Limiter] Processing request: ${request.id}`);
        const result = await request.execute();
        request.resolve(result);

        this.requestCount++;
        this.lastRequestTime = Date.now();

        console.log(
          `✅ [Rate Limiter] Completed request: ${request.id} (${this.requestCount}/${this.MAX_REQUESTS_PER_MINUTE} this minute)`
        );
      } catch (error) {
        console.error(`❌ [Rate Limiter] Failed request: ${request.id}`, error);
        request.reject(error);
      }
    }

    this.processing = false;
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get queue status
   */
  getStatus() {
    return {
      queueSize: this.queue.length,
      processing: this.processing,
      requestsThisMinute: this.requestCount,
      maxRequestsPerMinute: this.MAX_REQUESTS_PER_MINUTE,
      timeSinceLastRequest: Date.now() - this.lastRequestTime,
    };
  }
}

// Singleton instance
const rateLimiter = new OpenAIRateLimiter();

/**
 * Wrap an OpenAI API call with rate limiting
 * Set DISABLE_OPENAI_RATE_LIMITING=true to bypass rate limiting for testing
 */
export async function withRateLimit<T>(
  requestId: string,
  requestFn: () => Promise<T>
): Promise<T> {
  // Allow bypassing rate limiting for testing/debugging
  if (process.env.DISABLE_OPENAI_RATE_LIMITING === 'true') {
    console.log(
      `🚀 [Rate Limiter] BYPASSED for request: ${requestId} (rate limiting disabled)`
    );
    return requestFn();
  }

  return rateLimiter.enqueue(requestId, requestFn);
}

/**
 * Get rate limiter status
 */
export function getRateLimiterStatus() {
  return rateLimiter.getStatus();
}

export default rateLimiter;
