import { writeFile } from 'fs/promises';
import OpenAI from 'openai';
import { join } from 'path';
import type { OpenAIResponse } from '@/lib/types';

// Import unified system functions
import {
  newEnhanceAndTranslateContent,
  enhanceGermanArticle as newEnhanceGermanArticle,
  generateArticleMetadata as newGenerateArticleMetadata,
  literalTranslateToEnglish as newLiteralTranslateToEnglish,
  translateArticleToEnglish as newTranslateArticleToEnglish,
  processContentComprehensive,
} from './migration-wrapper';

const apiKey = process.env.OPENAI_API_KEY;

/**
 * Check if we're in build time (should not initialize clients)
 */
function isBuildTime(): boolean {
  return (
    (process.env.NODE_ENV === 'production' &&
      process.env.NEXT_PHASE === 'phase-production-build') ||
    process.argv.includes('build') ||
    process.env.BUILDING === 'true'
  );
}

// Only log and initialize during runtime, not build time
if (!isBuildTime()) {
  if (!apiKey) {
    console.warn('OPENAI_API_KEY is not set. OpenAI client will not function.');
  } else {
    console.log(`🔧 OpenAI Client Configuration:
  - API Key: ✅ Configured
  - System: ✅ Unified Prompt System
`);
  }
}

// Create client only when API key is available and not during build
let client: OpenAI | null = null;
if (apiKey && !isBuildTime()) {
  client = new OpenAI({
    apiKey,
  });
}

/**
 * Log quota errors to a dedicated file for monitoring
 */
async function logQuotaError(functionName: string, error: any): Promise<void> {
  try {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      function: functionName,
      error: {
        status: error.status,
        code: error.code,
        type: error.type,
        requestID: error.requestID,
        message: error.message,
      },
      actionRequired:
        'Check OpenAI billing and quota limits at https://platform.openai.com/usage',
    };

    const logPath = join(process.cwd(), 'logs', 'openai-quota-errors.json');

    // Read existing logs or create empty array
    let existingLogs: any[] = [];
    try {
      const { readFile } = await import('fs/promises');
      const existingContent = await readFile(logPath, 'utf-8');
      existingLogs = JSON.parse(existingContent);
    } catch {
      // File doesn't exist or is invalid, start fresh
      const { mkdir } = await import('fs/promises');
      await mkdir(join(process.cwd(), 'logs'), { recursive: true });
    }

    // Add new log entry
    existingLogs.push(logEntry);

    // Keep only last 100 entries
    if (existingLogs.length > 100) {
      existingLogs = existingLogs.slice(-100);
    }

    // Write back to file
    await writeFile(logPath, JSON.stringify(existingLogs, null, 2));

    console.log(`📝 Quota error logged to: ${logPath}`);
  } catch (logError) {
    console.error('Failed to log quota error:', logError);
  }
}

/**
 * Note: calculateCanadianRelevance has been removed from this part of the pipeline
 * as relevance filtering is handled earlier in the content processing workflow
 */

/**
 * Enhance German article content
 * Uses the unified prompt system for improved performance and reliability
 */
export async function enhanceGermanArticle(
  content: string
): Promise<OpenAIResponse> {
  if (!apiKey || !client) {
    return { success: false, error: 'OpenAI API key not configured.' };
  }

  try {
    return await newEnhanceGermanArticle(content);
  } catch (error: any) {
    // Enhanced error logging for quota issues
    if (error.status === 429) {
      console.error('🚨 CRITICAL: OpenAI API Quota Exceeded!');
      console.error('   Error Code:', error.code);
      console.error('   Error Type:', error.type);
      console.error('   Request ID:', error.requestID);
      console.error(
        '   💡 Action Required: Check billing and quota limits at https://platform.openai.com/usage'
      );

      await logQuotaError('enhanceGermanArticle', error);
    } else {
      console.error('OpenAI German enhancement error:', error);
    }

    return {
      success: false,
      error: error.message || 'OpenAI API error',
      errorCode: error.status,
      errorType: error.type,
    };
  }
}

/**
 * Literal translation from German to English (for source content)
 * Uses the unified prompt system for improved performance and reliability
 */
export async function literalTranslateToEnglish(
  content: string
): Promise<OpenAIResponse> {
  if (!apiKey || !client) {
    return { success: false, error: 'OpenAI API key not configured.' };
  }

  try {
    return await newLiteralTranslateToEnglish(content);
  } catch (error: any) {
    // Enhanced error logging for quota issues
    if (error.status === 429) {
      console.error('🚨 CRITICAL: OpenAI API Quota Exceeded!');
      console.error('   Error Code:', error.code);
      console.error('   Error Type:', error.type);
      console.error('   Request ID:', error.requestID);
      console.error(
        '   💡 Action Required: Check billing and quota limits at https://platform.openai.com/usage'
      );

      await logQuotaError('literalTranslateToEnglish', error);
    } else {
      console.error('OpenAI literal translation error:', error);
    }

    return {
      success: false,
      error: error.message || 'OpenAI API error',
      errorCode: error.status,
      errorType: error.type,
    };
  }
}

/**
 * Translate article to English with Canadian market focus (for enhancement)
 * Uses the unified prompt system for improved performance and reliability
 */
export async function translateArticleToEnglish(
  content: string
): Promise<OpenAIResponse> {
  if (!apiKey || !client) {
    return { success: false, error: 'OpenAI API key not configured.' };
  }

  try {
    return await newTranslateArticleToEnglish(content);
  } catch (error: any) {
    // Enhanced error logging for quota issues
    if (error.status === 429) {
      console.error('🚨 CRITICAL: OpenAI API Quota Exceeded!');
      console.error('   Error Code:', error.code);
      console.error('   Error Type:', error.type);
      console.error('   Request ID:', error.requestID);
      console.error(
        '   💡 Action Required: Check billing and quota limits at https://platform.openai.com/usage'
      );

      await logQuotaError('translateArticleToEnglish', error);
    } else {
      console.error('OpenAI English translation error:', error);
    }

    return {
      success: false,
      error: error.message || 'OpenAI API error',
      errorCode: error.status,
      errorType: error.type,
    };
  }
}

/**
 * Enhanced dual-language content enhancement
 * Single API call that enhances German content and provides English translation
 * Ensures consistency between German and English versions for multilingual website
 * Uses the unified prompt system for better performance and structured output
 */
export async function enhanceAndTranslateContent(
  germanContent: string,
  keyPoints: string[] = [],
  originalGermanTitle?: string
): Promise<{
  success: boolean;
  enhancedGerman?: {
    title: string;
    content: string;
    summary: string;
  };
  enhancedEnglish?: {
    title: string;
    content: string;
    summary: string;
  };
  tokensUsed?: number;
  error?: string;
  characterCleaning?: any;
}> {
  if (!apiKey || !client) {
    return { success: false, error: 'OpenAI API key not configured.' };
  }

  try {
    return await newEnhanceAndTranslateContent(
      germanContent,
      keyPoints,
      originalGermanTitle
    );
  } catch (error: any) {
    console.error('OpenAI dual-language enhancement error:', error);
    return {
      success: false,
      error: error.message || 'OpenAI API error',
    };
  }
}

/**
 * Generate article metadata (title, description, keywords)
 * Uses the unified prompt system for improved performance and reliability
 */
export async function generateArticleMetadata(
  content: string,
  language: 'de' | 'en'
): Promise<OpenAIResponse> {
  if (!apiKey || !client) {
    return { success: false, error: 'OpenAI API key not configured.' };
  }

  try {
    return await newGenerateArticleMetadata(content, language);
  } catch (error: any) {
    console.error('OpenAI metadata generation error:', error);
    return {
      success: false,
      error: error.message || 'OpenAI API error',
    };
  }
}

/**
 * Export new comprehensive processing function
 * Uses the latest unified prompt system for optimal performance
 */
export { processContentComprehensive };

/**
 * Export performance and health monitoring
 */
export { getPerformanceStats, healthCheck } from './responses-api-client';

export { client as openai };
export default client;
