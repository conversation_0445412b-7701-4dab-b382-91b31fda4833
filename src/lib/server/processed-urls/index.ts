import type { CreateProcessedUrlData } from '@/lib/types';
import type { ProcessedUrl } from '../../../payload-types';
import config from '@payload-config';
import { getPayload } from 'payload';

/**
 * Create a new processed URL record
 */
export async function createProcessedUrl(
  data: CreateProcessedUrlData
): Promise<{ id: string }> {
  try {
    const payload = await getPayload({ config });

    // Only parse feedId if it's provided and valid
    const feedId =
      data.feedId && data.feedId !== 'unknown' && !isNaN(parseInt(data.feedId))
        ? Number.parseInt(data.feedId)
        : undefined;

    // Validate and handle publication date
    let publicationDateISO: string | undefined;
    if (data.publicationDate) {
      publicationDateISO = data.publicationDate.toISOString();
      console.log(`✅ Using original publication date: ${publicationDateISO}`);
    } else {
      console.warn(
        `⚠️ No original publication date available for: ${data.url}`
      );
      console.warn(
        '   - ProcessedUrl will be created without publication date'
      );
      console.warn('   - This indicates date extraction from source failed');
      publicationDateISO = undefined; // Don't fallback to current date
    }

    const result = await payload.create({
      collection: 'processed-urls',
      overrideAccess: true, // Framework-native server pattern
      data: {
        url: data.url,
        title: data.title,
        status: data.status,
        feedId: feedId, // This can now be undefined/null
        publicationDate: publicationDateISO, // Can be undefined - no fallback
        reason: data.reason,
        articleId: data.articleId ? Number.parseInt(data.articleId) : undefined,
        processedAt: new Date().toISOString(), // Only processedAt gets current time
      },
    });

    const logMsg = publicationDateISO
      ? `${data.url} (${data.status}) with publication date ${publicationDateISO}`
      : `${data.url} (${data.status}) without publication date`;
    console.log(`✅ Created processed URL record: ${logMsg}`);

    return { id: result.id.toString() };
  } catch (error: any) {
    console.error('❌ Failed to create processed URL:', error);
    throw new Error(`Failed to create processed URL: ${error.message}`);
  }
}

/**
 * Check if a URL has already been processed
 */
export async function checkUrlExists(url: string): Promise<boolean> {
  try {
    const payload = await getPayload({ config });

    const result = await payload.find({
      collection: 'processed-urls',
      overrideAccess: true, // Framework-native server pattern
      where: {
        url: {
          equals: url,
        },
      },
      limit: 1,
    });

    return result.docs.length > 0;
  } catch (error: any) {
    console.error('❌ Failed to check URL existence:', error);
    // Return false on error to allow processing to continue
    return false;
  }
}

/**
 * Update the status of a processed URL
 */
export async function updateProcessedUrlStatus(
  id: string,
  status: 'pending' | 'accepted' | 'rejected' | 'error'
): Promise<void> {
  try {
    const payload = await getPayload({ config });

    await payload.update({
      collection: 'processed-urls',
      overrideAccess: true, // Framework-native server pattern
      id: Number.parseInt(id), // Convert string ID to number for PayloadCMS
      data: {
        status,
        processedAt: new Date().toISOString(),
      },
    });

    console.log(`✅ Updated processed URL status: ${id} -> ${status}`);
  } catch (error: any) {
    console.error('❌ Failed to update processed URL status:', error);
    throw new Error(`Failed to update processed URL status: ${error.message}`);
  }
}

/**
 * Get processed URLs by feed ID
 */
export async function getProcessedUrlsByFeed(
  feedId: string,
  limit = 100,
  page = 1
): Promise<{
  docs: ProcessedUrl[];
  totalDocs: number;
  totalPages: number;
  page: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}> {
  try {
    const payload = await getPayload({ config });

    const result = await payload.find({
      collection: 'processed-urls',
      overrideAccess: true, // Framework-native server pattern
      where: {
        feedId: {
          equals: Number.parseInt(feedId), // Convert string to number
        },
      },
      limit,
      page,
      sort: '-processedAt',
    });

    return {
      docs: result.docs as ProcessedUrl[],
      totalDocs: result.totalDocs,
      totalPages: result.totalPages,
      page: result.page || 1,
      hasNextPage: result.hasNextPage,
      hasPrevPage: result.hasPrevPage,
    };
  } catch (error: any) {
    console.error('❌ Failed to get processed URLs by feed:', error);
    throw new Error(`Failed to get processed URLs by feed: ${error.message}`);
  }
}

/**
 * Get processing statistics for a feed
 */
export async function getProcessingStats(feedId: string): Promise<{
  total: number;
  accepted: number;
  rejected: number;
  errors: number;
  acceptanceRate: number;
}> {
  try {
    const payload = await getPayload({ config });
    const feedIdNum = Number.parseInt(feedId);

    // Get total count
    const totalResult = await payload.find({
      collection: 'processed-urls',
      overrideAccess: true, // Framework-native server pattern
      where: {
        feedId: {
          equals: feedIdNum,
        },
      },
      limit: 0, // Just get count
    });

    // Get accepted count
    const acceptedResult = await payload.find({
      collection: 'processed-urls',
      overrideAccess: true, // Framework-native server pattern
      where: {
        and: [
          {
            feedId: {
              equals: feedIdNum,
            },
          },
          {
            status: {
              equals: 'accepted',
            },
          },
        ],
      },
      limit: 0,
    });

    // Get rejected count
    const rejectedResult = await payload.find({
      collection: 'processed-urls',
      overrideAccess: true, // Framework-native server pattern
      where: {
        and: [
          {
            feedId: {
              equals: feedIdNum,
            },
          },
          {
            status: {
              equals: 'rejected',
            },
          },
        ],
      },
      limit: 0,
    });

    // Get error count
    const errorResult = await payload.find({
      collection: 'processed-urls',
      overrideAccess: true, // Framework-native server pattern
      where: {
        and: [
          {
            feedId: {
              equals: feedIdNum,
            },
          },
          {
            status: {
              equals: 'error',
            },
          },
        ],
      },
      limit: 0,
    });

    const total = totalResult.totalDocs;
    const accepted = acceptedResult.totalDocs;
    const rejected = rejectedResult.totalDocs;
    const errors = errorResult.totalDocs;
    const acceptanceRate = total > 0 ? (accepted / total) * 100 : 0;

    return {
      total,
      accepted,
      rejected,
      errors,
      acceptanceRate,
    };
  } catch (error: any) {
    console.error('❌ Failed to get processing stats:', error);
    throw new Error(`Failed to get processing stats: ${error.message}`);
  }
}

/**
 * Clean up old processed URLs (older than specified days)
 */
export async function cleanupOldProcessedUrls(daysOld = 90): Promise<number> {
  try {
    const payload = await getPayload({ config });
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const result = await payload.find({
      collection: 'processed-urls',
      overrideAccess: true, // Framework-native server pattern
      where: {
        processedAt: {
          less_than: cutoffDate.toISOString(),
        },
      },
    });

    let deletedCount = 0;
    for (const doc of result.docs) {
      await payload.delete({
        collection: 'processed-urls',
        overrideAccess: true, // Framework-native server pattern
        id: doc.id,
      });
      deletedCount++;
    }

    console.log(`✅ Cleaned up ${deletedCount} old processed URLs`);
    return deletedCount;
  } catch (error: any) {
    console.error('❌ Failed to cleanup old processed URLs:', error);
    throw new Error(`Failed to cleanup old processed URLs: ${error.message}`);
  }
}
