/**
 * Centralized Article Validation Service
 * Sprint 2: Consolidates all validation logic with confirmed business rules
 */

/**
 * Helper: Check if Lexical content is empty or contains only whitespace
 */
function isLexicalContentEmpty(lexicalContent: any): boolean {
  if (!lexicalContent) return true;
  if (typeof lexicalContent !== 'object') return true;

  // Handle different Lexical formats
  const hasRootChildren = lexicalContent.root?.children?.length > 0;
  const hasContent = lexicalContent.content?.length > 0;

  if (!hasRootChildren && !hasContent) return true;

  // Convert to plain text and check if it's meaningful
  const textContent = extractTextFromLexical(lexicalContent);
  return textContent.trim().length === 0;
}

/**
 * Helper: Extract plain text from Lexical content structure
 * Supports both root.children and content formats
 */
function extractTextFromLexical(lexicalContent: any): string {
  if (!lexicalContent) return '';

  let text = '';

  function traverseNode(node: any): void {
    if (node.type === 'text' && node.text) {
      text += node.text;
    }
    if (node.children && Array.isArray(node.children)) {
      node.children.forEach(traverseNode);
    }
    if (node.content && Array.isArray(node.content)) {
      node.content.forEach(traverseNode);
    }
  }

  // Handle root.children format (PayloadCMS Lexical)
  if (lexicalContent.root?.children) {
    lexicalContent.root.children.forEach(traverseNode);
  }

  // Handle content format (test format)
  if (lexicalContent.content) {
    lexicalContent.content.forEach(traverseNode);
  }

  return text;
}

export interface ArticleValidationContext {
  articleType: 'generated' | 'curated';
  workflowStage: string;
  hasBeenEnhanced: boolean;
  hasGermanTranslation: boolean;
  hasOriginalSource: boolean; // Track if article has original RSS source (for Sprint 5)
  fields: {
    // Main article fields
    title?: string;

    // Enhanced/Content fields (primary content for both types)
    enhancedTitle?: string;
    enhancedSummary?: string;
    enhancedContent?: any;

    // Source fields (ONLY for generated articles or converted articles)
    originalTitle?: string;
    originalContent?: any;
    originalSummary?: string;

    // German fields (for re-translation detection)
    germanTitle?: string;
    germanContent?: any;
  };
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  buttonText?: string;
}

/**
 * ✅ SPRINT 5: Tab Visibility Result Interface
 */
export interface TabVisibilityResult {
  showSourcesTab: boolean;
  showEnglishTab: boolean;
  showGermanTab: boolean;
  showSeoTab: boolean;
}

/**
 * ✅ SPRINT 5: Determines tab visibility based on article type and source history
 * CONFIRMED REQUIREMENTS: Sources tab only for generated articles or converted articles
 */
export function getTabVisibility(
  context: ArticleValidationContext
): TabVisibilityResult {
  const { articleType, hasOriginalSource } = context;

  return {
    // Sources tab: Only for generated articles or curated articles converted from generated
    showSourcesTab:
      articleType === 'generated' ||
      (articleType === 'curated' && hasOriginalSource),

    // English tab: Always show (primary content tab)
    showEnglishTab: true,

    // German tab: Always show (for translations)
    showGermanTab: true,

    // SEO tab: Always show (for meta information)
    showSeoTab: true,
  };
}

/**
 * Validates if article can be enhanced
 * BUSINESS RULE: Only curated articles can be enhanced, using enhanced fields as input
 */
export function validateForEnhancement(
  context: ArticleValidationContext
): ValidationResult {
  const { articleType, fields, hasBeenEnhanced } = context;
  const errors: string[] = [];

  // Generated articles cannot be enhanced (already enhanced by RSS pipeline)
  if (articleType === 'generated') {
    return {
      isValid: false,
      errors: [
        'Generated articles are already enhanced and cannot be re-enhanced',
      ],
      warnings: [],
      buttonText: 'Already Enhanced',
    };
  }

  // For curated articles, validate enhanced fields (20+ character requirement)
  // CONFIRMED: Curated articles use enhanced fields as input for enhancement
  if (!fields.enhancedTitle || fields.enhancedTitle.length < 20) {
    errors.push('Enhanced title must be at least 20 characters');
  }

  if (!fields.enhancedSummary || fields.enhancedSummary.length < 20) {
    errors.push('Enhanced summary must be at least 20 characters');
  }

  if (isLexicalContentEmpty(fields.enhancedContent)) {
    errors.push('Enhanced content is required for enhancement');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings: [],
    buttonText: hasBeenEnhanced ? 'Re-Enhance' : 'Enhance Content',
  };
}

/**
 * Validates if article can be translated
 * BUSINESS RULE: Both generated and curated can translate, same validation rules
 */
export function validateForTranslation(
  context: ArticleValidationContext
): ValidationResult {
  const { articleType, fields, hasGermanTranslation } = context;
  const errors: string[] = [];

  if (articleType === 'generated') {
    // Generated articles: validate enhanced fields (should be pre-populated)
    if (!fields.enhancedTitle || fields.enhancedTitle.length < 20) {
      errors.push('Enhanced title must be at least 20 characters');
    }
    if (!fields.enhancedSummary || fields.enhancedSummary.length < 20) {
      errors.push('Enhanced summary must be at least 20 characters');
    }
    if (isLexicalContentEmpty(fields.enhancedContent)) {
      errors.push('Enhanced content is required for translation');
    }
  } else if (articleType === 'curated') {
    // Curated articles: validate enhanced fields (user can translate without enhancing first)
    if (!fields.enhancedTitle || fields.enhancedTitle.length < 20) {
      errors.push('Enhanced title must be at least 20 characters');
    }
    if (!fields.enhancedSummary || fields.enhancedSummary.length < 20) {
      errors.push('Enhanced summary must be at least 20 characters');
    }
    if (isLexicalContentEmpty(fields.enhancedContent)) {
      errors.push('Enhanced content is required for translation');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings: [],
    buttonText: hasGermanTranslation ? 'Re-Translate' : 'Translate to German',
  };
}

/**
 * Determines button visibility based on article type and state
 * BUSINESS RULE: Enhance only for curated, translate for all
 */
export function getButtonVisibility(context: ArticleValidationContext): {
  showEnhanceButton: boolean;
  showTranslateButton: boolean;
} {
  const { articleType } = context;

  return {
    // Enhance button: Only show for curated articles
    showEnhanceButton: articleType === 'curated',

    // Translate button: Show for all article types
    showTranslateButton: true,
  };
}

/**
 * Helper function to get the first validation error for user display
 */
export function getFirstValidationError(
  context: ArticleValidationContext,
  includeEnhanceValidation: boolean = true
): string | null {
  const translateValidation = validateForTranslation(context);

  if (includeEnhanceValidation) {
    const enhanceValidation = validateForEnhancement(context);
    const buttonVisibility = getButtonVisibility(context);

    // Show enhance errors first if button is visible and has errors
    if (buttonVisibility.showEnhanceButton && !enhanceValidation.isValid) {
      return enhanceValidation.errors[0];
    }
  }

  // Show translation errors
  if (!translateValidation.isValid) {
    return translateValidation.errors[0];
  }

  return null;
}

/**
 * Complete validation check for an article context
 */
export function validateArticleOperations(context: ArticleValidationContext) {
  const enhanceValidation = validateForEnhancement(context);
  const translateValidation = validateForTranslation(context);
  const buttonVisibility = getButtonVisibility(context);
  const tabVisibility = getTabVisibility(context);

  return {
    enhancement: enhanceValidation,
    translation: translateValidation,
    buttons: buttonVisibility,
    tabs: tabVisibility,
    overallValid: enhanceValidation.isValid && translateValidation.isValid,
    firstError: getFirstValidationError(context),
  };
}
