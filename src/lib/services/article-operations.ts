export interface OperationResult {
  success: boolean;
  data?: any;
  error?: string;
  metrics?: {
    processingTime: number;
    [key: string]: any;
  };
}

export interface OperationProgress {
  stage: string;
  progress: number;
  message?: string;
}

export class ArticleOperationsService {
  private progressCallback?: (progress: OperationProgress) => void;

  setProgressCallback(callback: (progress: OperationProgress) => void) {
    this.progressCallback = callback;
  }

  private updateProgress(stage: string, progress: number, message?: string) {
    if (this.progressCallback) {
      this.progressCallback({ stage, progress, message });
    }
  }

  async enhanceArticle(articleId: string): Promise<OperationResult> {
    try {
      this.updateProgress('Preparing enhancement...', 10);

      const response = await fetch('/api/articles/enhance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ articleId }),
      });

      this.updateProgress('Processing AI response...', 70);
      const result = await response.json();

      this.updateProgress('Finalizing...', 90);

      if (!response.ok) {
        throw new Error(result.error || 'Enhancement failed');
      }

      this.updateProgress('Complete!', 100);
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Enhancement failed',
      };
    }
  }

  async translateArticle(articleId: string): Promise<OperationResult> {
    try {
      this.updateProgress('Preparing translation...', 10);

      const response = await fetch('/api/articles/translate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ articleId }),
      });

      this.updateProgress('Processing translation...', 70);
      const result = await response.json();

      this.updateProgress('Finalizing...', 90);

      if (!response.ok) {
        throw new Error(result.error || 'Translation failed');
      }

      this.updateProgress('Complete!', 100);
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Translation failed',
      };
    }
  }

  /**
   * Validates if an operation can be performed
   */
  canPerformOperation(context: {
    hasId: boolean;
    isFormDirty: boolean;
    isValid: boolean;
    isAnyOperationRunning: boolean;
  }): { canPerform: boolean; reason?: string } {
    if (!context.hasId) {
      return { canPerform: false, reason: 'Please save the article first' };
    }

    if (context.isFormDirty) {
      return {
        canPerform: false,
        reason: 'Please save your changes before processing',
      };
    }

    if (!context.isValid) {
      return {
        canPerform: false,
        reason: 'Please complete all required fields',
      };
    }

    if (context.isAnyOperationRunning) {
      return {
        canPerform: false,
        reason: 'Another operation is currently in progress',
      };
    }

    return { canPerform: true };
  }

  /**
   * Determines operation button styling based on state
   */
  getButtonStyle(
    operation: 'enhance' | 'translate',
    state: {
      isRunning: boolean;
      justCompleted: boolean;
      hasBeenProcessed: boolean;
      isDisabled: boolean;
    }
  ) {
    const baseStyle = {
      color: 'white',
      border: 'none',
      borderRadius: '6px',
      padding: '12px 20px',
      fontSize: '14px',
      fontWeight: '500',
      minWidth: '160px',
      minHeight: '40px',
      transition: 'all 0.2s ease',
      boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '8px',
      margin: '0',
      cursor: state.isDisabled ? 'not-allowed' : 'pointer',
      opacity: state.isDisabled ? 0.6 : 1,
    };

    let backgroundColor: string;

    if (state.isRunning) {
      backgroundColor = '#6B7280'; // Gray for loading
    } else if (state.justCompleted) {
      backgroundColor = '#10B981'; // Green for success
    } else if (state.hasBeenProcessed) {
      backgroundColor = operation === 'enhance' ? '#8B5CF6' : '#059669'; // Purple for re-enhance, green for re-translate
    } else {
      backgroundColor = operation === 'enhance' ? '#3B82F6' : '#2563EB'; // Blue for first operations
    }

    return { ...baseStyle, backgroundColor };
  }
}

export const articleOperations = new ArticleOperationsService();
