/**
 * Generic Translation Service
 *
 * Provides translation functionality for any PayloadCMS collection
 * that supports bilingual English/German content.
 *
 * <AUTHOR> Development Team
 * @created 2025-01-27
 */

import { translateToGerman } from '@/lib/integrations/openai/german-translation';
import { lexicalToHTML } from '@/lib/utils/lexical';
import { htmlToLexical } from '@/lib/utils/html-to-lexical';

/**
 * Supported collections for translation
 */
export type TranslationCollection = 'articles' | 'pages';

/**
 * Content structure for translation
 */
export interface TranslatableContent {
  id: string;
  collection: TranslationCollection;
  title: string;
  content: any; // Lexical format
  summary?: string;
  [key: string]: any;
}

/**
 * Translation result data structure
 */
export interface TranslationData {
  germanTitle: string;
  germanContent: object; // Lexical format
  germanSummary?: string;
  germanKeywords?: string[];
  germanKeyInsights?: string[];
}

/**
 * Translation metrics
 */
export interface TranslationMetrics {
  processingTime: number;
  tokenUsage: number;
  costEstimate: number;
  linguisticAccuracy?: number;
  culturalAdaptation?: number;
}

/**
 * Complete translation result
 */
export interface TranslationResult {
  success: boolean;
  data?: TranslationData;
  error?: string;
  metrics: TranslationMetrics;
  validation: {
    isValid: boolean;
    issues: string[];
    qualityScore: number;
  };
}

/**
 * Translation validation result
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  buttonText: string;
}

/**
 * Translation options
 */
export interface TranslationOptions {
  temperature?: number;
  includeProcessingMetadata?: boolean;
  timeout?: number;
}

/**
 * Collection-specific field mapping
 */
interface CollectionFieldMapping {
  titleField: string;
  contentField: string;
  summaryField?: string;
  germanTitleField: string;
  germanContentField: string;
  germanSummaryField?: string;
  hasTranslationFlag: string;
}

/**
 * Field mappings for supported collections
 */
const COLLECTION_FIELD_MAPPINGS: Record<
  TranslationCollection,
  CollectionFieldMapping
> = {
  articles: {
    titleField: 'englishTab.enhancedTitle',
    contentField: 'englishTab.enhancedContent',
    summaryField: 'englishTab.enhancedSummary',
    germanTitleField: 'germanTab.germanTitle',
    germanContentField: 'germanTab.germanContent',
    germanSummaryField: 'germanTab.germanSummary',
    hasTranslationFlag: 'hasGermanTranslation',
  },
  pages: {
    titleField: 'englishTab.title',
    contentField: 'englishTab.content',
    germanTitleField: 'germanTab.germanTitle',
    germanContentField: 'germanTab.germanContent',
    hasTranslationFlag: 'hasGermanTranslation',
  },
};

/**
 * Generic Translation Service Class
 */
export class TranslationService {
  /**
   * Validate content for translation (collection-specific validation)
   */
  validateForTranslation(content: TranslatableContent): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic validation - just check for presence
    if (!content.title) {
      errors.push('Title is required for translation');
    }

    if (!content.content) {
      errors.push('Content is required for translation');
    }

    const isValid = errors.length === 0;
    const hasExistingTranslation = content.hasGermanTranslation;

    return {
      isValid,
      errors,
      warnings,
      buttonText: hasExistingTranslation
        ? 'Re-Translate to German'
        : 'Translate to German',
    };
  }

  /**
   * Extract content from collection document based on field mapping
   */
  extractContent(
    document: any,
    collection: TranslationCollection
  ): TranslatableContent {
    const mapping = COLLECTION_FIELD_MAPPINGS[collection];

    // Navigate nested object paths (e.g., 'englishTab.enhancedTitle')
    const getNestedValue = (obj: any, path: string): any => {
      return path.split('.').reduce((current, key) => current?.[key], obj);
    };

    const title =
      getNestedValue(document, mapping.titleField) || document.title;
    const content = getNestedValue(document, mapping.contentField);
    const summary = mapping.summaryField
      ? getNestedValue(document, mapping.summaryField)
      : undefined;
    const hasGermanTranslation =
      getNestedValue(document, mapping.hasTranslationFlag) || false;

    return {
      id: document.id,
      collection,
      title,
      content,
      summary,
      hasGermanTranslation,
      // Include raw document for additional context
      _document: document,
    };
  }

  /**
   * Prepare content for OpenAI translation
   */
  private prepareTranslationInput(content: TranslatableContent) {
    // Convert Lexical content to HTML for translation
    const contentHTML = content.content ? lexicalToHTML(content.content) : '';

    return {
      title: content.title,
      summary: content.summary || '',
      content: contentHTML,
      keyInsights: [], // Pages don't have key insights
      keywords: [], // Pages don't have keywords initially
    };
  }

  /**
   * Process translation result for collection
   */
  private async processTranslationResult(
    translationResult: any,
    collection: TranslationCollection
  ): Promise<TranslationData> {
    const { germanTitle, germanSummary, germanContent } = translationResult;

    // Convert HTML back to Lexical format
    const htmlResult = await htmlToLexical(germanContent);

    if (!htmlResult.metrics.success) {
      throw new Error('Failed to convert German content to Lexical format');
    }

    const result: TranslationData = {
      germanTitle,
      germanContent: htmlResult.result,
    };

    // Add optional fields if they exist
    if (germanSummary) {
      result.germanSummary = germanSummary;
    }

    // Articles-specific fields
    if (collection === 'articles' && translationResult.germanKeyInsights) {
      result.germanKeyInsights = translationResult.germanKeyInsights;
    }

    if (collection === 'articles' && translationResult.germanKeywords) {
      result.germanKeywords = translationResult.germanKeywords;
    }

    return result;
  }

  /**
   * Main translation function
   */
  async translateContent(
    content: TranslatableContent,
    options: TranslationOptions = {}
  ): Promise<TranslationResult> {
    const startTime = Date.now();

    try {
      // Validate content first
      const validation = this.validateForTranslation(content);
      if (!validation.isValid) {
        return {
          success: false,
          error: `Validation failed: ${validation.errors.join(', ')}`,
          metrics: {
            processingTime: Date.now() - startTime,
            tokenUsage: 0,
            costEstimate: 0,
          },
          validation: {
            isValid: false,
            issues: validation.errors,
            qualityScore: 0,
          },
        };
      }

      // Prepare input for OpenAI
      const translationInput = this.prepareTranslationInput(content);

      // Call OpenAI translation service
      const translationResult = await translateToGerman(translationInput, {
        temperature: options.temperature || 0.3,
        includeProcessingMetadata: true,
      });

      if (!translationResult.success || !translationResult.data) {
        return {
          success: false,
          error: translationResult.error || 'Translation service failed',
          metrics: translationResult.metrics,
          validation: translationResult.validation,
        };
      }

      // Process result for the specific collection
      const processedData = await this.processTranslationResult(
        translationResult.data,
        content.collection
      );

      return {
        success: true,
        data: processedData,
        metrics: translationResult.metrics,
        validation: translationResult.validation,
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Unknown translation error',
        metrics: {
          processingTime: Date.now() - startTime,
          tokenUsage: 0,
          costEstimate: 0,
        },
        validation: {
          isValid: false,
          issues: [error instanceof Error ? error.message : 'Unknown error'],
          qualityScore: 0,
        },
      };
    }
  }

  /**
   * Get field mapping for collection
   */
  getFieldMapping(collection: TranslationCollection): CollectionFieldMapping {
    return COLLECTION_FIELD_MAPPINGS[collection];
  }

  /**
   * Create update data structure for PayloadCMS
   */
  createUpdateData(
    translationData: TranslationData,
    collection: TranslationCollection
  ): Record<string, any> {
    const mapping = this.getFieldMapping(collection);
    const updateData: Record<string, any> = {};

    // Set the translation flag
    updateData[mapping.hasTranslationFlag] = true;

    // Create nested structure for tab fields
    if (mapping.germanTitleField.includes('.')) {
      const [tabName, fieldName] = mapping.germanTitleField.split('.');
      updateData[tabName] = updateData[tabName] || {};
      updateData[tabName][fieldName] = translationData.germanTitle;
    } else {
      updateData[mapping.germanTitleField] = translationData.germanTitle;
    }

    if (mapping.germanContentField.includes('.')) {
      const [tabName, fieldName] = mapping.germanContentField.split('.');
      updateData[tabName] = updateData[tabName] || {};
      updateData[tabName][fieldName] = translationData.germanContent;
    } else {
      updateData[mapping.germanContentField] = translationData.germanContent;
    }

    // Add summary if supported
    if (mapping.germanSummaryField && translationData.germanSummary) {
      if (mapping.germanSummaryField.includes('.')) {
        const [tabName, fieldName] = mapping.germanSummaryField.split('.');
        updateData[tabName] = updateData[tabName] || {};
        updateData[tabName][fieldName] = translationData.germanSummary;
      } else {
        updateData[mapping.germanSummaryField] = translationData.germanSummary;
      }
    }

    // Articles-specific fields
    if (collection === 'articles' && translationData.germanKeyInsights) {
      updateData.germanTab = updateData.germanTab || {};
      updateData.germanTab.germanKeyInsights =
        translationData.germanKeyInsights.map((insight: string) => ({
          insight,
        }));
    }

    if (collection === 'articles' && translationData.germanKeywords) {
      updateData.germanTab = updateData.germanTab || {};
      updateData.germanTab.germanKeywords = translationData.germanKeywords.map(
        (keyword: string) => ({ keyword })
      );
    }

    return updateData;
  }
}

/**
 * Singleton instance for reuse
 */
export const translationService = new TranslationService();
