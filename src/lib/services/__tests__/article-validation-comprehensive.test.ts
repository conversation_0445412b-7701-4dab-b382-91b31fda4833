import { describe, it, expect } from 'vitest';
import {
  validateArticleOperations,
  getButtonVisibility,
  getTabVisibility,
  type ArticleValidationContext,
} from '../article-validation';

describe('Article Validation Service - Sprint 6: Comprehensive Testing', () => {
  // ✅ Helper to create validation context
  const createContext = (
    overrides: Partial<ArticleValidationContext> = {}
  ): ArticleValidationContext => ({
    articleType: 'curated',
    workflowStage: 'curated-draft',
    hasBeenEnhanced: false,
    hasGermanTranslation: false,
    hasOriginalSource: false,
    fields: {
      title: 'Test Article',
      enhancedTitle: 'Valid title with enough characters for validation',
      enhancedSummary: 'Valid summary with enough characters for validation',
      enhancedContent: {
        root: {
          children: [
            {
              type: 'paragraph',
              children: [{ type: 'text', text: 'Valid content' }],
            },
          ],
        },
      },
      germanTitle: '',
      germanContent: undefined,
    },
    ...overrides,
  });

  describe('✅ Generated Article Requirements', () => {
    it('should hide enhance button for generated articles', () => {
      const context = createContext({
        articleType: 'generated',
        hasBeenEnhanced: true,
        hasOriginalSource: true,
      });

      const visibility = getButtonVisibility(context);
      expect(visibility.showEnhanceButton).toBe(false);
      expect(visibility.showTranslateButton).toBe(true);
    });

    it('should validate translation for generated articles with pre-populated content', () => {
      const context = createContext({
        articleType: 'generated',
        hasBeenEnhanced: true,
        hasOriginalSource: true,
      });

      const validation = validateArticleOperations(context);
      expect(validation.enhancement.isValid).toBe(false);
      expect(validation.enhancement.errors).toContain(
        'Generated articles are already enhanced and cannot be re-enhanced'
      );
      expect(validation.translation.isValid).toBe(true);
    });

    it('should show "Re-Translate" for translated generated articles', () => {
      const context = createContext({
        articleType: 'generated',
        hasBeenEnhanced: true,
        hasGermanTranslation: true,
        hasOriginalSource: true,
        fields: {
          title: 'Test Article',
          enhancedTitle: 'Enhanced title with enough characters',
          enhancedSummary: 'Enhanced summary with enough characters',
          enhancedContent: {
            root: {
              children: [
                {
                  type: 'paragraph',
                  children: [{ type: 'text', text: 'Content' }],
                },
              ],
            },
          },
          germanTitle: 'German Title',
          germanContent: {
            root: {
              children: [
                {
                  type: 'paragraph',
                  children: [{ type: 'text', text: 'German content' }],
                },
              ],
            },
          },
        },
      });

      const validation = validateArticleOperations(context);
      expect(validation.translation.buttonText).toBe('Re-Translate');
    });
  });

  describe('✅ Curated Article Requirements', () => {
    it('should show both buttons for curated articles', () => {
      const context = createContext({ articleType: 'curated' });

      const visibility = getButtonVisibility(context);
      expect(visibility.showEnhanceButton).toBe(true);
      expect(visibility.showTranslateButton).toBe(true);
    });

    it('should validate both operations for curated articles with valid content', () => {
      const context = createContext({ articleType: 'curated' });

      const validation = validateArticleOperations(context);
      expect(validation.enhancement.isValid).toBe(true);
      expect(validation.translation.isValid).toBe(true);
      expect(validation.firstError).toBeNull();
    });

    it('should show "Enhance Content" for new curated articles', () => {
      const context = createContext({
        articleType: 'curated',
        hasBeenEnhanced: false,
      });

      const validation = validateArticleOperations(context);
      expect(validation.enhancement.buttonText).toBe('Enhance Content');
    });

    it('should show "Re-Enhance" for previously enhanced curated articles', () => {
      const context = createContext({
        articleType: 'curated',
        hasBeenEnhanced: true,
      });

      const validation = validateArticleOperations(context);
      expect(validation.enhancement.buttonText).toBe('Re-Enhance');
    });
  });

  describe('✅ Generated → Curated Conversion', () => {
    it('should show both buttons when article is converted from generated to curated', () => {
      const context = createContext({
        articleType: 'curated',
        hasOriginalSource: true, // Converted from generated
        hasBeenEnhanced: true,
      });

      const visibility = getButtonVisibility(context);
      expect(visibility.showEnhanceButton).toBe(true);
      expect(visibility.showTranslateButton).toBe(true);

      const validation = validateArticleOperations(context);
      expect(validation.enhancement.isValid).toBe(true);
      expect(validation.translation.isValid).toBe(true);
    });
  });

  describe('✅ Content Validation Requirements', () => {
    it('should reject articles with insufficient title length', () => {
      const context = createContext({
        fields: {
          title: 'Test',
          enhancedTitle: 'Short', // <20 characters
          enhancedSummary: 'Valid summary with enough characters',
          enhancedContent: {
            root: {
              children: [
                {
                  type: 'paragraph',
                  children: [{ type: 'text', text: 'Content' }],
                },
              ],
            },
          },
          germanTitle: '',
          germanContent: undefined,
        },
      });

      const validation = validateArticleOperations(context);
      expect(validation.enhancement.isValid).toBe(false);
      expect(validation.translation.isValid).toBe(false);
      expect(validation.firstError).toContain('at least 20 characters');
    });

    it('should reject articles with insufficient summary length', () => {
      const context = createContext({
        fields: {
          title: 'Test',
          enhancedTitle: 'Valid title with enough characters',
          enhancedSummary: 'Short', // <20 characters
          enhancedContent: {
            root: {
              children: [
                {
                  type: 'paragraph',
                  children: [{ type: 'text', text: 'Content' }],
                },
              ],
            },
          },
          germanTitle: '',
          germanContent: undefined,
        },
      });

      const validation = validateArticleOperations(context);
      expect(validation.enhancement.isValid).toBe(false);
      expect(validation.translation.isValid).toBe(false);
    });

    it('should reject articles with empty content', () => {
      const context = createContext({
        fields: {
          title: 'Test',
          enhancedTitle: 'Valid title with enough characters',
          enhancedSummary: 'Valid summary with enough characters',
          enhancedContent: undefined, // No content
          germanTitle: '',
          germanContent: undefined,
        },
      });

      const validation = validateArticleOperations(context);
      expect(validation.enhancement.isValid).toBe(false);
      expect(validation.translation.isValid).toBe(false);
    });

    it('should handle empty Lexical content correctly', () => {
      const context = createContext({
        fields: {
          title: 'Test',
          enhancedTitle: 'Valid title with enough characters',
          enhancedSummary: 'Valid summary with enough characters',
          enhancedContent: {
            root: { children: [{ type: 'paragraph', children: [] }] },
          }, // Empty paragraph
          germanTitle: '',
          germanContent: undefined,
        },
      });

      const validation = validateArticleOperations(context);
      expect(validation.enhancement.isValid).toBe(false);
      expect(validation.translation.isValid).toBe(false);
    });

    it('should handle whitespace-only Lexical content correctly', () => {
      const context = createContext({
        fields: {
          title: 'Test',
          enhancedTitle: 'Valid title with enough characters',
          enhancedSummary: 'Valid summary with enough characters',
          enhancedContent: {
            root: {
              children: [
                {
                  type: 'paragraph',
                  children: [{ type: 'text', text: '   ' }],
                },
              ],
            },
          }, // Whitespace only
          germanTitle: '',
          germanContent: undefined,
        },
      });

      const validation = validateArticleOperations(context);
      expect(validation.enhancement.isValid).toBe(false);
      expect(validation.translation.isValid).toBe(false);
    });
  });

  describe('✅ Sources Tab Visibility Logic (Sprint 5)', () => {
    it('should show Sources tab for generated articles', () => {
      const context = createContext({
        articleType: 'generated',
        hasOriginalSource: true,
      });

      const tabVisibility = getTabVisibility(context);
      expect(tabVisibility.showSourcesTab).toBe(true);
    });

    it('should hide Sources tab for new curated articles', () => {
      const context = createContext({
        articleType: 'curated',
        hasOriginalSource: false, // New curated article
      });

      const tabVisibility = getTabVisibility(context);
      expect(tabVisibility.showSourcesTab).toBe(false);
    });

    it('should show Sources tab for converted curated articles (generated → curated)', () => {
      const context = createContext({
        articleType: 'curated',
        hasOriginalSource: true, // Converted from generated
      });

      const tabVisibility = getTabVisibility(context);
      expect(tabVisibility.showSourcesTab).toBe(true);
    });

    it('should always show other tabs', () => {
      const context = createContext();

      const tabVisibility = getTabVisibility(context);
      expect(tabVisibility.showEnglishTab).toBe(true);
      expect(tabVisibility.showGermanTab).toBe(true);
      expect(tabVisibility.showSeoTab).toBe(true);
    });
  });

  describe('✅ Translation Button Text Logic', () => {
    it('should show "Translate to German" for untranslated articles', () => {
      const context = createContext({
        hasGermanTranslation: false,
      });

      const validation = validateArticleOperations(context);
      expect(validation.translation.buttonText).toBe('Translate to German');
    });

    it('should show "Re-Translate" for translated articles', () => {
      const context = createContext({
        hasGermanTranslation: true,
        fields: {
          title: 'Test',
          enhancedTitle: 'Valid title with enough characters',
          enhancedSummary: 'Valid summary with enough characters',
          enhancedContent: {
            root: {
              children: [
                {
                  type: 'paragraph',
                  children: [{ type: 'text', text: 'Content' }],
                },
              ],
            },
          },
          germanTitle: 'German Title',
          germanContent: {
            root: {
              children: [
                {
                  type: 'paragraph',
                  children: [{ type: 'text', text: 'German content' }],
                },
              ],
            },
          },
        },
      });

      const validation = validateArticleOperations(context);
      expect(validation.translation.buttonText).toBe('Re-Translate');
    });
  });

  describe('✅ Complex Requirements Scenarios', () => {
    it('should handle all requirements for a complete workflow', () => {
      // Start: New curated article with valid content
      let context = createContext({
        articleType: 'curated',
        hasBeenEnhanced: false,
        hasGermanTranslation: false,
        hasOriginalSource: false,
      });

      let validation = validateArticleOperations(context);
      expect(validation.enhancement.isValid).toBe(true);
      expect(validation.enhancement.buttonText).toBe('Enhance Content');
      expect(validation.translation.isValid).toBe(true);
      expect(validation.translation.buttonText).toBe('Translate to German');

      // After enhancement
      context = { ...context, hasBeenEnhanced: true };
      validation = validateArticleOperations(context);
      expect(validation.enhancement.buttonText).toBe('Re-Enhance');

      // After translation
      context = { ...context, hasGermanTranslation: true };
      validation = validateArticleOperations(context);
      expect(validation.translation.buttonText).toBe('Re-Translate');
    });

    it('should handle generated article complete workflow', () => {
      // Generated article (pre-enhanced)
      const context = createContext({
        articleType: 'generated',
        hasBeenEnhanced: true,
        hasGermanTranslation: false,
        hasOriginalSource: true,
      });

      const validation = validateArticleOperations(context);
      const visibility = getButtonVisibility(context);

      // Enhancement not available for generated articles
      expect(visibility.showEnhanceButton).toBe(false);
      expect(validation.enhancement.isValid).toBe(false);

      // Translation available
      expect(visibility.showTranslateButton).toBe(true);
      expect(validation.translation.isValid).toBe(true);
      expect(validation.translation.buttonText).toBe('Translate to German');
    });

    it('should handle article type switching scenario', () => {
      // Article switched from generated to curated (preserves source)
      const context = createContext({
        articleType: 'curated',
        hasBeenEnhanced: true, // Was enhanced as generated
        hasGermanTranslation: false,
        hasOriginalSource: true, // Preserves original source from generated state
      });

      const validation = validateArticleOperations(context);
      const visibility = getButtonVisibility(context);
      const tabVisibility = getTabVisibility(context);

      // Both buttons available after conversion
      expect(visibility.showEnhanceButton).toBe(true);
      expect(visibility.showTranslateButton).toBe(true);

      // Sources tab still visible (preserves source access)
      expect(tabVisibility.showSourcesTab).toBe(true);

      // Re-enhancement available
      expect(validation.enhancement.isValid).toBe(true);
      expect(validation.enhancement.buttonText).toBe('Re-Enhance');
    });
  });

  describe('✅ Edge Cases and Error Handling', () => {
    it('should handle undefined fields gracefully', () => {
      const context = createContext({
        fields: {
          title: undefined as any,
          enhancedTitle: undefined as any,
          enhancedSummary: undefined as any,
          enhancedContent: undefined,
          germanTitle: undefined as any,
          germanContent: undefined,
        },
      });

      const validation = validateArticleOperations(context);
      expect(validation.enhancement.isValid).toBe(false);
      expect(validation.translation.isValid).toBe(false);
      expect(validation.firstError).toBeTruthy();
    });

    it('should provide helpful first error message', () => {
      const context = createContext({
        fields: {
          title: 'Test',
          enhancedTitle: 'Short', // First validation error
          enhancedSummary: 'Also short', // Second validation error
          enhancedContent: undefined, // Third validation error
          germanTitle: '',
          germanContent: undefined,
        },
      });

      const validation = validateArticleOperations(context);
      expect(validation.firstError).toContain('at least 20 characters');
    });

    it('should return null firstError when validation passes', () => {
      const context = createContext(); // Valid by default

      const validation = validateArticleOperations(context);
      expect(validation.firstError).toBeNull();
    });
  });

  describe('✅ Performance and Consistency', () => {
    it('should be consistent across multiple calls with same input', () => {
      const context = createContext();

      const validation1 = validateArticleOperations(context);
      const validation2 = validateArticleOperations(context);

      expect(validation1).toEqual(validation2);
    });

    it('should handle large content efficiently', () => {
      const largeContent = {
        root: {
          children: Array(1000).fill({
            type: 'paragraph',
            children: [
              {
                type: 'text',
                text: 'Large content paragraph repeated many times',
              },
            ],
          }),
        },
      };

      const context = createContext({
        fields: {
          title: 'Test',
          enhancedTitle: 'Valid title with enough characters',
          enhancedSummary: 'Valid summary with enough characters',
          enhancedContent: largeContent,
          germanTitle: '',
          germanContent: undefined,
        },
      });

      const startTime = Date.now();
      const validation = validateArticleOperations(context);
      const endTime = Date.now();

      expect(validation.enhancement.isValid).toBe(true);
      expect(validation.translation.isValid).toBe(true);
      expect(endTime - startTime).toBeLessThan(100); // Should be fast
    });
  });
});
