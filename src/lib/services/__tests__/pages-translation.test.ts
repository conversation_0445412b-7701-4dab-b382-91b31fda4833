/**
 * Pages Translation Service Tests
 *
 * Comprehensive test suite for Pages translation functionality,
 * ensuring the generic translation service works correctly with Pages.
 *
 * <AUTHOR> Development Team
 * @created 2025-01-27
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { translationService, TranslationService } from '../translation-service';
import type { TranslatableContent } from '../translation-service';

describe('Pages Translation Service', () => {
  let service: TranslationService;

  beforeEach(() => {
    service = translationService;
    vi.clearAllMocks();
  });

  describe('Field Mapping', () => {
    it('should have correct field mapping for pages collection', () => {
      const mapping = service.getFieldMapping('pages');

      expect(mapping).toEqual({
        titleField: 'englishTab.title',
        contentField: 'englishTab.content',
        germanTitleField: 'germanTab.germanTitle',
        germanContentField: 'germanTab.germanContent',
        hasTranslationFlag: 'hasGermanTranslation',
      });
    });

    it('should not have summary field for pages (unlike articles)', () => {
      const mapping = service.getFieldMapping('pages');
      expect(mapping.summaryField).toBeUndefined();
      expect(mapping.germanSummaryField).toBeUndefined();
    });
  });

  describe('Content Extraction', () => {
    it('should extract content from pages document correctly', () => {
      const mockPageDocument = {
        id: 'page-123',
        title: 'About Us',
        englishTab: {
          title: 'About Our Company',
          content: {
            root: {
              children: [{ type: 'text', text: 'We are a great company...' }],
            },
          },
        },
        hasGermanTranslation: false,
      };

      const extractedContent = service.extractContent(
        mockPageDocument,
        'pages'
      );

      expect(extractedContent).toEqual({
        id: 'page-123',
        collection: 'pages',
        title: 'About Our Company',
        content: mockPageDocument.englishTab.content,
        summary: undefined,
        hasGermanTranslation: false,
        _document: mockPageDocument,
      });
    });

    it('should fallback to main title if englishTab.title is missing', () => {
      const mockPageDocument = {
        id: 'page-123',
        title: 'Main Title',
        englishTab: {
          content: {
            root: { children: [{ type: 'text', text: 'Content...' }] },
          },
        },
        hasGermanTranslation: false,
      };

      const extractedContent = service.extractContent(
        mockPageDocument,
        'pages'
      );

      expect(extractedContent.title).toBe('Main Title');
    });
  });

  describe('Content Validation', () => {
    it('should validate pages content for translation', () => {
      const validContent: TranslatableContent = {
        id: 'page-123',
        collection: 'pages',
        title: 'This is a valid title for translation',
        content: {
          root: {
            children: [{ type: 'text', text: 'Valid content for translation' }],
          },
        },
        hasGermanTranslation: false,
      };

      const validation = service.validateForTranslation(validContent);

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
      expect(validation.buttonText).toBe('Translate to German');
    });

    it('should reject content with too short title', () => {
      const invalidContent: TranslatableContent = {
        id: 'page-123',
        collection: 'pages',
        title: 'Hi', // Too short
        content: {
          root: { children: [{ type: 'text', text: 'Valid content' }] },
        },
        hasGermanTranslation: false,
      };

      const validation = service.validateForTranslation(invalidContent);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain(
        'Content must have at least 20 characters for translation'
      );
    });

    it('should reject content without content field', () => {
      const invalidContent: TranslatableContent = {
        id: 'page-123',
        collection: 'pages',
        title: 'This is a valid title for translation',
        content: null,
        hasGermanTranslation: false,
      };

      const validation = service.validateForTranslation(invalidContent);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain(
        'Content is required for translation'
      );
    });

    it('should show re-translate button text for existing translations', () => {
      const contentWithTranslation: TranslatableContent = {
        id: 'page-123',
        collection: 'pages',
        title: 'This is a valid title for translation',
        content: {
          root: { children: [{ type: 'text', text: 'Valid content' }] },
        },
        hasGermanTranslation: true,
      };

      const validation = service.validateForTranslation(contentWithTranslation);

      expect(validation.buttonText).toBe('Re-Translate to German');
    });
  });

  describe('Update Data Creation', () => {
    it('should create correct update data structure for pages', () => {
      const translationData = {
        germanTitle: 'Über Uns',
        germanContent: {
          root: {
            children: [
              { type: 'text', text: 'Wir sind ein großartiges Unternehmen...' },
            ],
          },
        },
      };

      const updateData = service.createUpdateData(translationData, 'pages');

      expect(updateData).toEqual({
        hasGermanTranslation: true,
        germanTab: {
          germanTitle: 'Über Uns',
          germanContent: translationData.germanContent,
        },
      });
    });

    it('should not include summary fields for pages (unlike articles)', () => {
      const translationData = {
        germanTitle: 'Über Uns',
        germanContent: {
          root: { children: [{ type: 'text', text: 'Content...' }] },
        },
        germanSummary: 'This should be ignored for pages',
      };

      const updateData = service.createUpdateData(translationData, 'pages');

      expect(updateData.germanTab.germanSummary).toBeUndefined();
    });

    it('should not include article-specific fields for pages', () => {
      const translationData = {
        germanTitle: 'Über Uns',
        germanContent: {
          root: { children: [{ type: 'text', text: 'Content...' }] },
        },
        germanKeyInsights: ['This should be ignored'],
        germanKeywords: ['keyword1', 'keyword2'],
      };

      const updateData = service.createUpdateData(translationData, 'pages');

      expect(updateData.germanTab.germanKeyInsights).toBeUndefined();
      expect(updateData.germanTab.germanKeywords).toBeUndefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle missing content gracefully', async () => {
      const invalidContent: TranslatableContent = {
        id: 'page-123',
        collection: 'pages',
        title: '', // Empty title
        content: null,
        hasGermanTranslation: false,
      };

      const result = await service.translateContent(invalidContent);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Validation failed');
    });

    it('should validate content properly before translation', () => {
      const contentTooShort: TranslatableContent = {
        id: 'page-123',
        collection: 'pages',
        title: 'Short',
        content: { root: { children: [{ type: 'text', text: 'Too short' }] } },
        hasGermanTranslation: false,
      };

      const validation = service.validateForTranslation(contentTooShort);

      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });

    it('should distinguish between pages and articles in field mapping', () => {
      const pagesMapping = service.getFieldMapping('pages');
      const articlesMapping = service.getFieldMapping('articles');

      // Pages don't have summary fields
      expect(pagesMapping.summaryField).toBeUndefined();
      expect(pagesMapping.germanSummaryField).toBeUndefined();

      // Articles do have summary fields
      expect(articlesMapping.summaryField).toBeDefined();
      expect(articlesMapping.germanSummaryField).toBeDefined();

      // Both should have basic title and content fields
      expect(pagesMapping.titleField).toBeDefined();
      expect(pagesMapping.contentField).toBeDefined();
      expect(articlesMapping.titleField).toBeDefined();
      expect(articlesMapping.contentField).toBeDefined();
    });
  });

  describe('Collection-Specific Behavior', () => {
    it('should handle pages collection specific validation', () => {
      const pageContent: TranslatableContent = {
        id: 'page-123',
        collection: 'pages',
        title: 'About Our Company - We are the Best',
        content: {
          root: {
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'This is substantial content that should pass validation.',
                  },
                ],
              },
            ],
          },
        },
        hasGermanTranslation: false,
      };

      const validation = service.validateForTranslation(pageContent);
      expect(validation.isValid).toBe(true);
    });

    it('should create proper update structure for pages without article-specific fields', () => {
      const translationData = {
        germanTitle: 'Über Unser Unternehmen',
        germanContent: {
          root: {
            children: [
              {
                type: 'paragraph',
                children: [
                  { type: 'text', text: 'Deutsche Übersetzung des Inhalts.' },
                ],
              },
            ],
          },
        },
      };

      const updateData = service.createUpdateData(translationData, 'pages');

      // Should have basic structure
      expect(updateData.hasGermanTranslation).toBe(true);
      expect(updateData.germanTab.germanTitle).toBe('Über Unser Unternehmen');
      expect(updateData.germanTab.germanContent).toEqual(
        translationData.germanContent
      );

      // Should not have article-specific fields
      expect(updateData.germanTab.germanKeyInsights).toBeUndefined();
      expect(updateData.germanTab.germanKeywords).toBeUndefined();
      expect(updateData.germanTab.germanSummary).toBeUndefined();
    });
  });
});
