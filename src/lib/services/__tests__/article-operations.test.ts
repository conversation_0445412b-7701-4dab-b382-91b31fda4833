import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ArticleOperationsService } from '../article-operations';

// Mock fetch globally
global.fetch = vi.fn();

describe('ArticleOperationsService', () => {
  let service: ArticleOperationsService;
  const mockProgressCallback = vi.fn();

  beforeEach(() => {
    service = new ArticleOperationsService();
    service.setProgressCallback(mockProgressCallback);
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('enhanceArticle', () => {
    it('should successfully enhance an article and track progress', async () => {
      const mockResponse = {
        success: true,
        data: {
          englishTab: {
            enhancedTitle: 'Enhanced Test Title',
            enhancedSummary: 'Enhanced test summary',
          },
        },
        metrics: { processingTime: 2500 },
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValueOnce(mockResponse),
      });

      const result = await service.enhanceArticle('test-id-123');

      // Verify API call
      expect(fetch).toHaveBeenCalledWith('/api/articles/enhance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ articleId: 'test-id-123' }),
      });

      // Verify progress tracking
      expect(mockProgressCallback).toHaveBeenCalledTimes(4);
      expect(mockProgressCallback).toHaveBeenCalledWith({
        stage: 'Preparing enhancement...',
        progress: 10,
      });
      expect(mockProgressCallback).toHaveBeenCalledWith({
        stage: 'Processing AI response...',
        progress: 70,
      });
      expect(mockProgressCallback).toHaveBeenCalledWith({
        stage: 'Finalizing...',
        progress: 90,
      });
      expect(mockProgressCallback).toHaveBeenCalledWith({
        stage: 'Complete!',
        progress: 100,
      });

      // Verify result
      expect(result.success).toBe(true);
      expect(result.data.englishTab.enhancedTitle).toBe('Enhanced Test Title');
    });

    it('should handle API errors gracefully', async () => {
      (fetch as any).mockResolvedValueOnce({
        ok: false,
        json: vi.fn().mockResolvedValueOnce({
          error: 'Article not found',
        }),
      });

      const result = await service.enhanceArticle('invalid-id');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Article not found');
    });

    it('should handle network errors gracefully', async () => {
      (fetch as any).mockRejectedValueOnce(new Error('Network error'));

      const result = await service.enhanceArticle('test-id');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Network error');
    });
  });

  describe('translateArticle', () => {
    it('should successfully translate an article and track progress', async () => {
      const mockResponse = {
        success: true,
        data: {
          germanTab: {
            germanTitle: 'Deutscher Titel',
            germanSummary: 'Deutsche Zusammenfassung',
          },
        },
        metrics: { processingTime: 3000 },
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValueOnce(mockResponse),
      });

      const result = await service.translateArticle('test-id-456');

      // Verify API call
      expect(fetch).toHaveBeenCalledWith('/api/articles/translate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ articleId: 'test-id-456' }),
      });

      // Verify progress tracking
      expect(mockProgressCallback).toHaveBeenCalledTimes(4);
      expect(mockProgressCallback).toHaveBeenCalledWith({
        stage: 'Preparing translation...',
        progress: 10,
      });

      // Verify result
      expect(result.success).toBe(true);
      expect(result.data.germanTab.germanTitle).toBe('Deutscher Titel');
    });
  });

  describe('canPerformOperation', () => {
    it('should allow operation when all conditions are met', () => {
      const result = service.canPerformOperation({
        hasId: true,
        isFormDirty: false,
        isValid: true,
        isAnyOperationRunning: false,
      });

      expect(result.canPerform).toBe(true);
      expect(result.reason).toBeUndefined();
    });

    it('should reject operation when article not saved', () => {
      const result = service.canPerformOperation({
        hasId: false,
        isFormDirty: false,
        isValid: true,
        isAnyOperationRunning: false,
      });

      expect(result.canPerform).toBe(false);
      expect(result.reason).toBe('Please save the article first');
    });

    it('should reject operation when form has unsaved changes', () => {
      const result = service.canPerformOperation({
        hasId: true,
        isFormDirty: true,
        isValid: true,
        isAnyOperationRunning: false,
      });

      expect(result.canPerform).toBe(false);
      expect(result.reason).toBe('Please save your changes before processing');
    });

    it('should reject operation when validation fails', () => {
      const result = service.canPerformOperation({
        hasId: true,
        isFormDirty: false,
        isValid: false,
        isAnyOperationRunning: false,
      });

      expect(result.canPerform).toBe(false);
      expect(result.reason).toBe('Please complete all required fields');
    });

    it('should reject operation when another operation is running', () => {
      const result = service.canPerformOperation({
        hasId: true,
        isFormDirty: false,
        isValid: true,
        isAnyOperationRunning: true,
      });

      expect(result.canPerform).toBe(false);
      expect(result.reason).toBe('Another operation is currently in progress');
    });
  });

  describe('getButtonStyle', () => {
    it('should return correct style for enhance button in default state', () => {
      const style = service.getButtonStyle('enhance', {
        isRunning: false,
        justCompleted: false,
        hasBeenProcessed: false,
        isDisabled: false,
      });

      expect(style.backgroundColor).toBe('#3B82F6'); // Blue for first enhancement
      expect(style.cursor).toBe('pointer');
      expect(style.opacity).toBe(1);
    });

    it('should return correct style for translate button when running', () => {
      const style = service.getButtonStyle('translate', {
        isRunning: true,
        justCompleted: false,
        hasBeenProcessed: false,
        isDisabled: true,
      });

      expect(style.backgroundColor).toBe('#6B7280'); // Gray for loading
      expect(style.cursor).toBe('not-allowed');
      expect(style.opacity).toBe(0.6);
    });

    it('should return correct style for enhance button when just completed', () => {
      const style = service.getButtonStyle('enhance', {
        isRunning: false,
        justCompleted: true,
        hasBeenProcessed: false,
        isDisabled: false,
      });

      expect(style.backgroundColor).toBe('#10B981'); // Green for success
    });

    it('should return correct style for re-enhancement', () => {
      const style = service.getButtonStyle('enhance', {
        isRunning: false,
        justCompleted: false,
        hasBeenProcessed: true,
        isDisabled: false,
      });

      expect(style.backgroundColor).toBe('#8B5CF6'); // Purple for re-enhancement
    });

    it('should return correct style for re-translation', () => {
      const style = service.getButtonStyle('translate', {
        isRunning: false,
        justCompleted: false,
        hasBeenProcessed: true,
        isDisabled: false,
      });

      expect(style.backgroundColor).toBe('#059669'); // Green for re-translation
    });
  });

  describe('progress callback', () => {
    it('should not call progress callback when not set', () => {
      const serviceWithoutCallback = new ArticleOperationsService();

      // This should not throw an error
      expect(() => {
        (serviceWithoutCallback as any).updateProgress('Test stage', 50);
      }).not.toThrow();
    });

    it('should allow updating progress callback', () => {
      const newCallback = vi.fn();
      service.setProgressCallback(newCallback);

      (service as any).updateProgress('Test stage', 75, 'Test message');

      expect(newCallback).toHaveBeenCalledWith({
        stage: 'Test stage',
        progress: 75,
        message: 'Test message',
      });
    });
  });
});
