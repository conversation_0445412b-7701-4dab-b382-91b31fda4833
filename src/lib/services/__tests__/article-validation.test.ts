/**
 * Article Validation Service Tests
 * Sprint 2: Comprehensive testing of centralized validation service
 */

import { describe, it, expect } from 'vitest';
import {
  validateForEnhancement,
  validateForTranslation,
  getButtonVisibility,
  getTabVisibility,
  validateArticleOperations,
  getFirstValidationError,
  type ArticleValidationContext,
} from '../article-validation';

describe('Article Validation Service', () => {
  // Base contexts for testing
  const baseCuratedContext: ArticleValidationContext = {
    articleType: 'curated',
    workflowStage: 'curated-draft',
    hasBeenEnhanced: false,
    hasGermanTranslation: false,
    hasOriginalSource: false,
    fields: {
      title: 'Test Article Title',
      enhancedTitle: 'This is a test title with more than twenty characters',
      enhancedSummary:
        'This is a test summary with more than twenty characters for testing',
      enhancedContent: {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [{ type: 'text', text: 'Test content' }],
          },
        ],
      },
      germanTitle: '',
      germanContent: null,
    },
  };

  const baseGeneratedContext: ArticleValidationContext = {
    articleType: 'generated',
    workflowStage: 'enhanced-draft',
    hasBeenEnhanced: true,
    hasGermanTranslation: false,
    hasOriginalSource: true,
    fields: {
      title: 'Generated Article Title',
      enhancedTitle: 'AI-enhanced title with more than twenty characters',
      enhancedSummary:
        'AI-enhanced summary with more than twenty characters for testing',
      enhancedContent: {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [{ type: 'text', text: 'AI-enhanced content' }],
          },
        ],
      },
      originalTitle: 'Original RSS Title',
      originalContent: {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [{ type: 'text', text: 'Original RSS content' }],
          },
        ],
      },
      germanTitle: '',
      germanContent: null,
    },
  };

  describe('validateForEnhancement', () => {
    describe('Generated Articles', () => {
      it('should reject enhancement for generated articles', () => {
        const result = validateForEnhancement(baseGeneratedContext);

        expect(result.isValid).toBe(false);
        expect(result.errors).toContain(
          'Generated articles are already enhanced and cannot be re-enhanced'
        );
        expect(result.buttonText).toBe('Already Enhanced');
      });
    });

    describe('Curated Articles', () => {
      it('should pass validation for valid curated article', () => {
        const result = validateForEnhancement(baseCuratedContext);

        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
        expect(result.buttonText).toBe('Enhance Content');
      });

      it('should show "Re-Enhance" for previously enhanced articles', () => {
        const context = {
          ...baseCuratedContext,
          hasBeenEnhanced: true,
        };

        const result = validateForEnhancement(context);

        expect(result.isValid).toBe(true);
        expect(result.buttonText).toBe('Re-Enhance');
      });

      it('should fail validation when enhanced title is too short', () => {
        const context = {
          ...baseCuratedContext,
          fields: {
            ...baseCuratedContext.fields,
            enhancedTitle: 'Short title',
          },
        };

        const result = validateForEnhancement(context);

        expect(result.isValid).toBe(false);
        expect(result.errors).toContain(
          'Enhanced title must be at least 20 characters'
        );
      });

      it('should fail validation when enhanced summary is too short', () => {
        const context = {
          ...baseCuratedContext,
          fields: {
            ...baseCuratedContext.fields,
            enhancedSummary: 'Short summary',
          },
        };

        const result = validateForEnhancement(context);

        expect(result.isValid).toBe(false);
        expect(result.errors).toContain(
          'Enhanced summary must be at least 20 characters'
        );
      });

      it('should fail validation when enhanced content is missing', () => {
        const context = {
          ...baseCuratedContext,
          fields: {
            ...baseCuratedContext.fields,
            enhancedContent: null,
          },
        };

        const result = validateForEnhancement(context);

        expect(result.isValid).toBe(false);
        expect(result.errors).toContain(
          'Enhanced content is required for enhancement'
        );
      });

      it('should fail validation with multiple errors', () => {
        const context = {
          ...baseCuratedContext,
          fields: {
            ...baseCuratedContext.fields,
            enhancedTitle: 'Short',
            enhancedSummary: 'Also short',
            enhancedContent: null,
          },
        };

        const result = validateForEnhancement(context);

        expect(result.isValid).toBe(false);
        expect(result.errors).toHaveLength(3);
        expect(result.errors).toContain(
          'Enhanced title must be at least 20 characters'
        );
        expect(result.errors).toContain(
          'Enhanced summary must be at least 20 characters'
        );
        expect(result.errors).toContain(
          'Enhanced content is required for enhancement'
        );
      });
    });
  });

  describe('validateForTranslation', () => {
    describe('Generated Articles', () => {
      it('should pass validation for valid generated article', () => {
        const result = validateForTranslation(baseGeneratedContext);

        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
        expect(result.buttonText).toBe('Translate to German');
      });

      it('should show "Re-Translate" for previously translated articles', () => {
        const context = {
          ...baseGeneratedContext,
          hasGermanTranslation: true,
        };

        const result = validateForTranslation(context);

        expect(result.isValid).toBe(true);
        expect(result.buttonText).toBe('Re-Translate');
      });

      it('should fail validation when enhanced fields are invalid', () => {
        const context = {
          ...baseGeneratedContext,
          fields: {
            ...baseGeneratedContext.fields,
            enhancedTitle: 'Short',
            enhancedSummary: 'Short',
            enhancedContent: null,
          },
        };

        const result = validateForTranslation(context);

        expect(result.isValid).toBe(false);
        expect(result.errors).toHaveLength(3);
        expect(result.errors).toContain(
          'Enhanced title must be at least 20 characters'
        );
        expect(result.errors).toContain(
          'Enhanced summary must be at least 20 characters'
        );
        expect(result.errors).toContain(
          'Enhanced content is required for translation'
        );
      });
    });

    describe('Curated Articles', () => {
      it('should pass validation for valid curated article', () => {
        const result = validateForTranslation(baseCuratedContext);

        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
        expect(result.buttonText).toBe('Translate to German');
      });

      it('should fail validation when enhanced fields are invalid', () => {
        const context = {
          ...baseCuratedContext,
          fields: {
            ...baseCuratedContext.fields,
            enhancedTitle: 'Short',
            enhancedSummary: 'Short',
            enhancedContent: null,
          },
        };

        const result = validateForTranslation(context);

        expect(result.isValid).toBe(false);
        expect(result.errors).toHaveLength(3);
      });
    });
  });

  describe('getButtonVisibility', () => {
    it('should show enhance button only for curated articles', () => {
      const curatedVisibility = getButtonVisibility(baseCuratedContext);
      expect(curatedVisibility.showEnhanceButton).toBe(true);
      expect(curatedVisibility.showTranslateButton).toBe(true);

      const generatedVisibility = getButtonVisibility(baseGeneratedContext);
      expect(generatedVisibility.showEnhanceButton).toBe(false);
      expect(generatedVisibility.showTranslateButton).toBe(true);
    });
  });

  describe('getTabVisibility', () => {
    it('should show sources tab for generated articles', () => {
      const visibility = getTabVisibility(baseGeneratedContext);

      expect(visibility.showSourcesTab).toBe(true);
      expect(visibility.showEnglishTab).toBe(true);
      expect(visibility.showGermanTab).toBe(true);
      expect(visibility.showSeoTab).toBe(true);
    });

    it('should hide sources tab for pure curated articles', () => {
      const visibility = getTabVisibility(baseCuratedContext);

      expect(visibility.showSourcesTab).toBe(false);
      expect(visibility.showEnglishTab).toBe(true);
      expect(visibility.showGermanTab).toBe(true);
      expect(visibility.showSeoTab).toBe(true);
    });

    it('should show sources tab for curated articles with original source', () => {
      const context = {
        ...baseCuratedContext,
        hasOriginalSource: true,
      };

      const visibility = getTabVisibility(context);

      expect(visibility.showSourcesTab).toBe(true);
    });
  });

  describe('getFirstValidationError', () => {
    it('should return null when both validations pass', () => {
      const error = getFirstValidationError(baseCuratedContext);
      expect(error).toBeNull();
    });

    it('should return enhance error first when enhance button is visible', () => {
      const context = {
        ...baseCuratedContext,
        fields: {
          ...baseCuratedContext.fields,
          enhancedTitle: 'Short title',
        },
      };

      const error = getFirstValidationError(context);
      expect(error).toBe('Enhanced title must be at least 20 characters');
    });

    it('should return translate error when enhance validation is excluded', () => {
      const context = {
        ...baseCuratedContext,
        fields: {
          ...baseCuratedContext.fields,
          enhancedTitle: 'Short title',
        },
      };

      const error = getFirstValidationError(context, false);
      expect(error).toBe('Enhanced title must be at least 20 characters');
    });

    it('should return translate error for generated articles', () => {
      const context = {
        ...baseGeneratedContext,
        fields: {
          ...baseGeneratedContext.fields,
          enhancedTitle: 'Short title',
        },
      };

      const error = getFirstValidationError(context);
      expect(error).toBe('Enhanced title must be at least 20 characters');
    });
  });

  describe('validateArticleOperations', () => {
    it('should return complete validation results for curated article', () => {
      const results = validateArticleOperations(baseCuratedContext);

      expect(results.enhancement.isValid).toBe(true);
      expect(results.translation.isValid).toBe(true);
      expect(results.buttons.showEnhanceButton).toBe(true);
      expect(results.buttons.showTranslateButton).toBe(true);
      expect(results.tabs.showSourcesTab).toBe(false);
      expect(results.overallValid).toBe(true);
      expect(results.firstError).toBeNull();
    });

    it('should return complete validation results for generated article', () => {
      const results = validateArticleOperations(baseGeneratedContext);

      expect(results.enhancement.isValid).toBe(false);
      expect(results.translation.isValid).toBe(true);
      expect(results.buttons.showEnhanceButton).toBe(false);
      expect(results.buttons.showTranslateButton).toBe(true);
      expect(results.tabs.showSourcesTab).toBe(true);
      expect(results.overallValid).toBe(false);
      // For generated articles, enhance button is hidden, so firstError focuses on translation
      // Since translation is valid, firstError should be null
      expect(results.firstError).toBeNull();
    });

    it('should handle validation failures correctly', () => {
      const context = {
        ...baseCuratedContext,
        fields: {
          ...baseCuratedContext.fields,
          enhancedTitle: 'Short',
          enhancedSummary: 'Short',
          enhancedContent: null,
        },
      };

      const results = validateArticleOperations(context);

      expect(results.enhancement.isValid).toBe(false);
      expect(results.translation.isValid).toBe(false);
      expect(results.overallValid).toBe(false);
      expect(results.firstError).toBe(
        'Enhanced title must be at least 20 characters'
      );
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined fields gracefully', () => {
      const context = {
        ...baseCuratedContext,
        fields: {
          title: undefined,
          enhancedTitle: undefined,
          enhancedSummary: undefined,
          enhancedContent: undefined,
        },
      };

      const enhanceResult = validateForEnhancement(context);
      const translateResult = validateForTranslation(context);

      expect(enhanceResult.isValid).toBe(false);
      expect(translateResult.isValid).toBe(false);
      expect(enhanceResult.errors).toHaveLength(3);
      expect(translateResult.errors).toHaveLength(3);
    });

    it('should handle empty strings correctly', () => {
      const context = {
        ...baseCuratedContext,
        fields: {
          ...baseCuratedContext.fields,
          enhancedTitle: '',
          enhancedSummary: '',
        },
      };

      const enhanceResult = validateForEnhancement(context);

      expect(enhanceResult.isValid).toBe(false);
      expect(enhanceResult.errors).toContain(
        'Enhanced title must be at least 20 characters'
      );
      expect(enhanceResult.errors).toContain(
        'Enhanced summary must be at least 20 characters'
      );
    });

    it('should handle exactly 20 characters as valid', () => {
      const context = {
        ...baseCuratedContext,
        fields: {
          ...baseCuratedContext.fields,
          enhancedTitle: '12345678901234567890', // Exactly 20 characters
          enhancedSummary: 'abcdefghijklmnopqrst', // Exactly 20 characters
        },
      };

      const enhanceResult = validateForEnhancement(context);
      const translateResult = validateForTranslation(context);

      expect(enhanceResult.isValid).toBe(true);
      expect(translateResult.isValid).toBe(true);
    });

    it('should correctly identify empty Lexical content as invalid', () => {
      // Test empty Lexical object (common PayloadCMS format)
      const emptyLexicalContent = { root: { children: [] } };

      const context = {
        ...baseCuratedContext,
        fields: {
          ...baseCuratedContext.fields,
          enhancedTitle: 'Valid title with 20+ chars',
          enhancedSummary: 'Valid summary with 20+ characters',
          enhancedContent: emptyLexicalContent,
        },
      };

      const results = validateArticleOperations(context);
      expect(results.enhancement.isValid).toBe(false);
      expect(results.enhancement.errors).toContain(
        'Enhanced content is required for enhancement'
      );
      expect(results.translation.isValid).toBe(false);
      expect(results.translation.errors).toContain(
        'Enhanced content is required for translation'
      );
    });

    it('should correctly identify whitespace-only Lexical content as invalid', () => {
      // Test Lexical content with only whitespace
      const whitespaceContent = {
        root: {
          children: [
            {
              type: 'paragraph',
              children: [{ type: 'text', text: '   \n  \t  ' }],
            },
          ],
        },
      };

      const context = {
        ...baseCuratedContext,
        fields: {
          ...baseCuratedContext.fields,
          enhancedTitle: 'Valid title with 20+ chars',
          enhancedSummary: 'Valid summary with 20+ characters',
          enhancedContent: whitespaceContent,
        },
      };

      const results = validateArticleOperations(context);
      expect(results.enhancement.isValid).toBe(false);
      expect(results.enhancement.errors).toContain(
        'Enhanced content is required for enhancement'
      );
    });
  });
});
