import { cookies } from 'next/headers';
import { getClientSideURL } from '@/utilities/getURL';
import type { User } from '@/payload-types';

/**
 * Simple authentication check for API routes
 * Uses existing PayloadCMS authentication system
 */
export async function checkAuth(): Promise<{
  isAuthenticated: boolean;
  user: User | null;
  error?: string;
}> {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get('payload-token')?.value;

    if (!token) {
      return {
        isAuthenticated: false,
        user: null,
        error: 'No authentication token found',
      };
    }

    // Use PayloadCMS's existing /api/users/me endpoint
    const response = await fetch(`${getClientSideURL()}/api/users/me`, {
      headers: {
        Authorization: `JWT ${token}`,
      },
    });

    if (!response.ok) {
      return {
        isAuthenticated: false,
        user: null,
        error: 'Invalid or expired token',
      };
    }

    const data = await response.json();

    return {
      isAuthenticated: true,
      user: data.user,
    };
  } catch (error) {
    console.error('Authentication check failed:', error);
    return {
      isAuthenticated: false,
      user: null,
      error: 'Authentication system error',
    };
  }
}

/**
 * Middleware wrapper for API routes that require authentication
 */
export function withAuth<T extends any[]>(
  handler: (...args: T) => Promise<Response>
) {
  return async (...args: T): Promise<Response> => {
    const auth = await checkAuth();

    if (!auth.isAuthenticated) {
      return new Response(
        JSON.stringify({
          error: 'Authentication required',
          message: auth.error || 'Please log in to access this endpoint',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Call the original handler with authentication context
    return handler(...args);
  };
}
