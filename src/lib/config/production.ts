/**
 * Production Configuration
 *
 * Environment-specific configuration for optimal production performance
 * and reliability of the enhanced content processing pipeline.
 *
 * Features:
 * - Environment-specific settings and optimizations
 * - Timeout and retry policy configuration
 * - Performance optimization parameters
 * - Resource limits and monitoring thresholds
 * - Configuration validation and error handling
 * - Caching strategies and connection pooling
 *
 * <AUTHOR> Blick Development Team
 * @created 2025-06-29
 * @sprint Sprint 6: Monitoring & Production Readiness
 */

/**
 * Production configuration interface
 */
export interface ProductionConfig {
  // Environment settings
  environment: 'development' | 'staging' | 'production';
  nodeEnv: string;

  // Processing settings
  processing: {
    enabled: boolean;
    timeout: number;
    maxConcurrent: number;
    retryAttempts: number;
    retryDelay: number;
  };

  // Monitoring settings
  monitoring: {
    enabled: boolean;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    healthCheckInterval: number;
    performanceTracking: boolean;
    errorTracking: boolean;
  };

  // Firecrawl optimization
  firecrawl: {
    cacheTimeout: number;
    maxRetries: number;
    stealthMode: 'auto' | 'always' | 'never';
    timeout: number;
    rateLimit: {
      requests: number;
      window: number;
    };
  };

  // Caching configuration
  cache: {
    enabled: boolean;
    ttl: number;
    maxSize: number;
    cleanupInterval: number;
  };

  // Resource limits
  resources: {
    maxMemoryUsage: number;
    maxProcessingTime: number;
    maxConcurrentRequests: number;
    gcInterval: number;
  };

  // Alert thresholds
  alerts: {
    errorRate: number;
    processingTime: number;
    memoryUsage: number;
    consecutiveErrors: number;
  };

  // Database settings
  database: {
    connectionTimeout: number;
    queryTimeout: number;
    poolSize: number;
    retryAttempts: number;
  };

  // Security settings
  security: {
    apiKeyRequired: boolean;
    rateLimiting: boolean;
    corsEnabled: boolean;
    allowedOrigins: string[];
  };
}

/**
 * Default production configuration
 */
const defaultConfig: ProductionConfig = {
  environment: 'production',
  nodeEnv: process.env.NODE_ENV || 'production',

  processing: {
    enabled: true,
    timeout: 30000, // 30 seconds
    maxConcurrent: 5,
    retryAttempts: 3,
    retryDelay: 2000, // 2 seconds
  },

  monitoring: {
    enabled: true,
    logLevel: 'info',
    healthCheckInterval: 60000, // 1 minute
    performanceTracking: true,
    errorTracking: true,
  },

  firecrawl: {
    cacheTimeout: 300000, // 5 minutes
    maxRetries: 3,
    stealthMode: 'auto',
    timeout: 45000, // 45 seconds
    rateLimit: {
      requests: 100,
      window: 60000, // 1 minute
    },
  },

  cache: {
    enabled: true,
    ttl: 1800000, // 30 minutes
    maxSize: 1000,
    cleanupInterval: 300000, // 5 minutes
  },

  resources: {
    maxMemoryUsage: 500 * 1024 * 1024, // 500MB
    maxProcessingTime: 60000, // 1 minute
    maxConcurrentRequests: 10,
    gcInterval: 300000, // 5 minutes
  },

  alerts: {
    errorRate: 0.1, // 10%
    processingTime: 15000, // 15 seconds
    memoryUsage: 400 * 1024 * 1024, // 400MB
    consecutiveErrors: 5,
  },

  database: {
    connectionTimeout: 10000, // 10 seconds
    queryTimeout: 30000, // 30 seconds
    poolSize: 10,
    retryAttempts: 3,
  },

  security: {
    apiKeyRequired: true,
    rateLimiting: true,
    corsEnabled: true,
    allowedOrigins: ['https://borsenblick.com', 'https://www.borsenblick.com'],
  },
};

/**
 * Environment-specific configuration overrides
 */
const environmentConfigs: Record<string, Partial<ProductionConfig>> = {
  development: {
    monitoring: {
      enabled: true,
      logLevel: 'debug',
      healthCheckInterval: 30000, // 30 seconds
      performanceTracking: true,
      errorTracking: true,
    },
    processing: {
      enabled: true,
      timeout: 60000, // 1 minute (longer for debugging)
      maxConcurrent: 2,
      retryAttempts: 1,
      retryDelay: 1000,
    },
    firecrawl: {
      cacheTimeout: 300000, // 5 minutes
      maxRetries: 2,
      stealthMode: 'auto',
      timeout: 60000, // 1 minute
      rateLimit: {
        requests: 50,
        window: 60000, // 1 minute
      },
    },
    security: {
      apiKeyRequired: false,
      rateLimiting: false,
      corsEnabled: true,
      allowedOrigins: ['http://localhost:3000', 'http://localhost:3002'],
    },
  },

  staging: {
    monitoring: {
      enabled: true,
      logLevel: 'info',
      healthCheckInterval: 45000, // 45 seconds
      performanceTracking: true,
      errorTracking: true,
    },
    processing: {
      enabled: true,
      timeout: 45000, // 45 seconds
      maxConcurrent: 3,
      retryAttempts: 2,
      retryDelay: 1500,
    },
    security: {
      apiKeyRequired: true,
      rateLimiting: true,
      corsEnabled: true,
      allowedOrigins: ['https://staging.borsenblick.com'],
    },
  },

  production: {
    monitoring: {
      enabled: true,
      logLevel: 'warn',
      healthCheckInterval: 60000, // 1 minute
      performanceTracking: true,
      errorTracking: true,
    },
    processing: {
      enabled: true,
      timeout: 30000, // 30 seconds
      maxConcurrent: 5,
      retryAttempts: 3,
      retryDelay: 2000,
    },
    resources: {
      maxMemoryUsage: 400 * 1024 * 1024, // 400MB (stricter in production)
      maxProcessingTime: 45000, // 45 seconds
      maxConcurrentRequests: 8,
      gcInterval: 180000, // 3 minutes
    },
    security: {
      apiKeyRequired: true,
      rateLimiting: true,
      corsEnabled: true,
      allowedOrigins: [
        'https://borsenblick.com',
        'https://www.borsenblick.com',
      ],
    },
  },
};

/**
 * Load and validate production configuration
 */
function loadProductionConfig(): ProductionConfig {
  const environment = (process.env.NODE_ENV ||
    'production') as keyof typeof environmentConfigs;

  // Start with default config
  let config = { ...defaultConfig };

  // Apply environment-specific overrides
  if (environmentConfigs[environment]) {
    config = mergeConfig(config, environmentConfigs[environment]);
  }

  // Apply environment variable overrides
  config = applyEnvironmentVariables(config);

  // Validate configuration
  validateConfiguration(config);

  console.log(
    `🔧 Production configuration loaded for environment: ${environment}`
  );

  return config;
}

/**
 * Merge configuration objects deeply
 */
function mergeConfig(
  base: ProductionConfig,
  override: Partial<ProductionConfig>
): ProductionConfig {
  const merged = { ...base };

  Object.keys(override).forEach(key => {
    const typedKey = key as keyof ProductionConfig;
    const overrideValue = override[typedKey];
    const baseValue = base[typedKey];

    if (overrideValue !== undefined) {
      if (
        typeof overrideValue === 'object' &&
        overrideValue !== null &&
        typeof baseValue === 'object' &&
        baseValue !== null
      ) {
        merged[typedKey] = { ...baseValue, ...overrideValue } as any;
      } else {
        merged[typedKey] = overrideValue as any;
      }
    }
  });

  return merged;
}

/**
 * Apply environment variable overrides
 */
function applyEnvironmentVariables(config: ProductionConfig): ProductionConfig {
  const envConfig = { ...config };

  // Processing settings
  if (process.env.ENHANCED_PROCESSING_ENABLED !== undefined) {
    envConfig.processing.enabled =
      process.env.ENHANCED_PROCESSING_ENABLED === 'true';
  }
  if (process.env.PROCESSING_TIMEOUT) {
    envConfig.processing.timeout = parseInt(process.env.PROCESSING_TIMEOUT, 10);
  }
  if (process.env.MAX_CONCURRENT_PROCESSING) {
    envConfig.processing.maxConcurrent = parseInt(
      process.env.MAX_CONCURRENT_PROCESSING,
      10
    );
  }

  // Monitoring settings
  if (process.env.MONITORING_ENABLED !== undefined) {
    envConfig.monitoring.enabled = process.env.MONITORING_ENABLED === 'true';
  }
  if (process.env.LOG_LEVEL) {
    envConfig.monitoring.logLevel = process.env.LOG_LEVEL as any;
  }
  if (process.env.HEALTH_CHECK_INTERVAL) {
    envConfig.monitoring.healthCheckInterval = parseInt(
      process.env.HEALTH_CHECK_INTERVAL,
      10
    );
  }

  // Firecrawl settings
  if (process.env.FIRECRAWL_CACHE_TTL) {
    envConfig.firecrawl.cacheTimeout = parseInt(
      process.env.FIRECRAWL_CACHE_TTL,
      10
    );
  }
  if (process.env.FIRECRAWL_MAX_RETRIES) {
    envConfig.firecrawl.maxRetries = parseInt(
      process.env.FIRECRAWL_MAX_RETRIES,
      10
    );
  }
  if (process.env.FIRECRAWL_STEALTH_MODE) {
    envConfig.firecrawl.stealthMode = process.env.FIRECRAWL_STEALTH_MODE as any;
  }

  // Resource limits
  if (process.env.MAX_MEMORY_USAGE) {
    envConfig.resources.maxMemoryUsage = parseInt(
      process.env.MAX_MEMORY_USAGE,
      10
    );
  }
  if (process.env.MAX_PROCESSING_TIME) {
    envConfig.resources.maxProcessingTime = parseInt(
      process.env.MAX_PROCESSING_TIME,
      10
    );
  }

  return envConfig;
}

/**
 * Validate configuration values
 */
function validateConfiguration(config: ProductionConfig): void {
  const errors: string[] = [];

  // Validate processing settings
  if (config.processing.timeout < 1000) {
    errors.push('Processing timeout must be at least 1000ms');
  }
  if (config.processing.maxConcurrent < 1) {
    errors.push('Max concurrent processing must be at least 1');
  }

  // Validate monitoring settings
  if (
    !['debug', 'info', 'warn', 'error'].includes(config.monitoring.logLevel)
  ) {
    errors.push('Invalid log level specified');
  }
  if (config.monitoring.healthCheckInterval < 10000) {
    errors.push('Health check interval must be at least 10 seconds');
  }

  // Validate resource limits
  if (config.resources.maxMemoryUsage < 100 * 1024 * 1024) {
    errors.push('Max memory usage must be at least 100MB');
  }
  if (config.resources.maxProcessingTime < 5000) {
    errors.push('Max processing time must be at least 5 seconds');
  }

  // Validate Firecrawl settings
  if (config.firecrawl.timeout < 5000) {
    errors.push('Firecrawl timeout must be at least 5 seconds');
  }
  if (config.firecrawl.maxRetries < 0) {
    errors.push('Firecrawl max retries cannot be negative');
  }

  // Check required environment variables
  if (config.environment === 'production') {
    if (!process.env.FIRECRAWL_API_KEY) {
      errors.push('FIRECRAWL_API_KEY is required in production');
    }
    if (!process.env.DATABASE_URL) {
      errors.push('DATABASE_URL is required in production');
    }
  }

  if (errors.length > 0) {
    console.error('❌ Configuration validation failed:');
    errors.forEach(error => console.error(`  - ${error}`));
    throw new Error(`Configuration validation failed: ${errors.join(', ')}`);
  }

  console.log('✅ Configuration validation passed');
}

/**
 * Get current production configuration
 */
let cachedConfig: ProductionConfig | null = null;

export function getProductionConfig(): ProductionConfig {
  if (!cachedConfig) {
    cachedConfig = loadProductionConfig();
  }
  return cachedConfig;
}

/**
 * Reload configuration (useful for testing or configuration updates)
 */
export function reloadProductionConfig(): ProductionConfig {
  cachedConfig = null;
  return getProductionConfig();
}

/**
 * Check if feature is enabled in current environment
 */
export function isFeatureEnabled(feature: keyof ProductionConfig): boolean {
  const config = getProductionConfig();
  const featureConfig = config[feature];

  if (
    typeof featureConfig === 'object' &&
    featureConfig !== null &&
    'enabled' in featureConfig
  ) {
    return (featureConfig as any).enabled;
  }

  return true; // Default to enabled if no explicit enabled flag
}

/**
 * Get environment-specific setting
 */
export function getConfigValue<T>(path: string): T | undefined {
  const config = getProductionConfig();
  const keys = path.split('.');
  let value: any = config;

  for (const key of keys) {
    if (value && typeof value === 'object' && key in value) {
      value = value[key];
    } else {
      return undefined;
    }
  }

  return value as T;
}

/**
 * Export configuration for external use
 */
export default getProductionConfig;
