/**
 * Relative Time Utility
 * Displays human-readable relative time (e.g., "2 days ago", "vor 3 Std.")
 * with full German/English localization support
 */

import type { DateInput, SupportedLocale } from './date-types';
import { toValidDate, DateValidationError } from './date-types';

interface RelativeTimeTranslations {
  justNow: string;
  minutesAgo: (n: number) => string;
  hoursAgo: (n: number) => string;
  daysAgo: (n: number) => string;
  weeksAgo: (n: number) => string;
  monthsAgo: (n: number) => string;
  yearsAgo: (n: number) => string;
}

const RELATIVE_TIME_TRANSLATIONS: Record<
  SupportedLocale,
  RelativeTimeTranslations
> = {
  en: {
    justNow: 'just now',
    minutesAgo: (n: number) => `${n} min ago`,
    hoursAgo: (n: number) => `${n} hour${n > 1 ? 's' : ''} ago`,
    daysAgo: (n: number) => `${n} day${n > 1 ? 's' : ''} ago`,
    weeksAgo: (n: number) => `${n} week${n > 1 ? 's' : ''} ago`,
    monthsAgo: (n: number) => `${n} month${n > 1 ? 's' : ''} ago`,
    yearsAgo: (n: number) => `${n} year${n > 1 ? 's' : ''} ago`,
  },
  de: {
    justNow: 'gerade eben',
    minutesAgo: (n: number) => `vor ${n} Min.`,
    hoursAgo: (n: number) => `vor ${n} Std.`,
    daysAgo: (n: number) => `vor ${n} Tag${n > 1 ? 'en' : ''}`,
    weeksAgo: (n: number) => `vor ${n} Woche${n > 1 ? 'n' : ''}`,
    monthsAgo: (n: number) => `vor ${n} Monat${n > 1 ? 'en' : ''}`,
    yearsAgo: (n: number) => `vor ${n} Jahr${n > 1 ? 'en' : ''}`,
  },
};

/**
 * Convert a date to relative time string
 * @param date Date string or Date object
 * @param locale Language locale ('en' or 'de')
 * @returns Human-readable relative time string
 */
export function getRelativeTime(
  date: DateInput,
  locale: SupportedLocale = 'en'
): string {
  try {
    const now = new Date();
    const past = new Date(date);

    // Handle invalid dates
    if (isNaN(past.getTime())) {
      return locale === 'de' ? 'unbekannt' : 'unknown';
    }

    // Handle future dates (shouldn't happen but just in case)
    if (past.getTime() > now.getTime()) {
      return locale === 'de' ? 'gerade eben' : 'just now';
    }

    const diffInSeconds = Math.floor((now.getTime() - past.getTime()) / 1000);
    const translations = RELATIVE_TIME_TRANSLATIONS[locale];

    // Less than 1 minute
    if (diffInSeconds < 60) {
      return translations.justNow;
    }

    // Less than 1 hour
    if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return translations.minutesAgo(minutes);
    }

    // Less than 1 day
    if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return translations.hoursAgo(hours);
    }

    // Less than 1 week
    if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return translations.daysAgo(days);
    }

    // Less than 1 month (approximately 4 weeks)
    if (diffInSeconds < 2592000) {
      const weeks = Math.floor(diffInSeconds / 604800);
      return translations.weeksAgo(weeks);
    }

    // Less than 1 year (approximately 12 months)
    if (diffInSeconds < 31536000) {
      const months = Math.floor(diffInSeconds / 2592000);
      return translations.monthsAgo(months);
    }

    // 1 year or more
    const years = Math.floor(diffInSeconds / 31536000);
    return translations.yearsAgo(years);
  } catch (error) {
    console.error('Error calculating relative time:', error);
    return locale === 'de' ? 'unbekannt' : 'unknown';
  }
}

/**
 * Check if a date is recent (within last 24 hours)
 * @param date Date string or Date object
 * @returns Boolean indicating if date is recent
 */
export function isRecentDate(date: DateInput): boolean {
  try {
    const now = new Date();
    const past = new Date(date);
    const diffInHours = (now.getTime() - past.getTime()) / (1000 * 60 * 60);
    return diffInHours <= 24;
  } catch {
    return false;
  }
}

/**
 * Memoized version of getRelativeTime for performance
 */
const relativeTimeCache = new Map<
  string,
  { result: string; timestamp: number }
>();
const CACHE_DURATION = 60000; // 1 minute

export function getRelativeTimeMemoized(
  date: DateInput,
  locale: SupportedLocale = 'en'
): string {
  const dateString = typeof date === 'string' ? date : date.toISOString();
  const cacheKey = `${dateString}-${locale}`;
  const now = Date.now();

  // Check cache
  const cached = relativeTimeCache.get(cacheKey);
  if (cached && now - cached.timestamp < CACHE_DURATION) {
    return cached.result;
  }

  // Calculate and cache result
  const result = getRelativeTime(date, locale);
  relativeTimeCache.set(cacheKey, { result, timestamp: now });

  // Clean up old cache entries periodically
  if (relativeTimeCache.size > 100) {
    const oldEntries = Array.from(relativeTimeCache.entries()).filter(
      ([_, value]) => now - value.timestamp > CACHE_DURATION
    );
    oldEntries.forEach(([key]) => relativeTimeCache.delete(key));
  }

  return result;
}
