/**
 * Client-safe Lexical text extraction utility
 * No server imports - safe for client components
 */

/**
 * Extract plain text from Lexical editor JSO<PERSON> data
 * This is a pure function with no dependencies on Payload config
 */
export function lexicalToText(lexicalData: unknown): string {
  if (!lexicalData || typeof lexicalData !== 'object' || lexicalData === null) {
    return '';
  }

  const data = lexicalData as { root?: { children?: unknown[] } };
  if (!data.root || !data.root.children) {
    return '';
  }

  const extractTextFromNode = (node: unknown): string => {
    if (!node || typeof node !== 'object') {
      return '';
    }

    const nodeObj = node as {
      type?: string;
      text?: string;
      children?: unknown[];
    };

    if (nodeObj.type === 'text') {
      return nodeObj.text || '';
    }

    if (nodeObj.children && Array.isArray(nodeObj.children)) {
      return nodeObj.children.map(extractTextFromNode).join('');
    }

    return '';
  };

  const paragraphs = data.root.children
    .map((child: unknown) => extractTextFromNode(child))
    .filter((text: string) => text.trim().length > 0);

  return paragraphs.join('\n\n');
}

/**
 * Validate if content is already in Lexical format
 */
export function isLexicalFormat(content: unknown): boolean {
  if (!content || typeof content !== 'object' || content === null) {
    return false;
  }

  const data = content as { root?: { children?: unknown[] } };
  return !!(
    data.root &&
    Array.isArray(data.root.children) &&
    data.root.children.length > 0
  );
}
