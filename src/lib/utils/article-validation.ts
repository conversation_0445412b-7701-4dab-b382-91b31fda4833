import type { PayloadRequest } from 'payload';

export interface ArticleValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ArticleValidationContext {
  data: any;
  operation: string;
  req: PayloadRequest | null; // Allow null for testing
  originalDoc?: any;
}

/**
 * Validates that an article has at least one category
 */
export function validateCategories(data: any): ArticleValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (
    !data.categories ||
    !Array.isArray(data.categories) ||
    data.categories.length === 0
  ) {
    errors.push(
      'At least one category is required for publication. Please assign the article to a category.'
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Validates that an article has a tier placement set
 */
export function validatePlacement(data: any): ArticleValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!data.placement || !data.placement.trim()) {
    errors.push(
      'Article tier placement is required for publication. Please select a tier (Tier 1, Tier 2, or Tier 3).'
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Validates that an article has a featured image
 */
export function validateFeaturedImage(data: any): ArticleValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!data.featuredImage) {
    errors.push(
      'Featured image is required for publication. Please upload an image or use the auto-generate feature.'
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Validates SEO fields using the SEO plugin's built-in validation
 */
export function validateSEOFields(data: any): ArticleValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check if meta object exists
  if (!data.meta) {
    errors.push(
      'SEO fields are required for publication. Please fill in the SEO tab.'
    );
    return { isValid: false, errors, warnings };
  }

  // Validate SEO title with plugin-compatible length requirements
  if (!data.meta.title || !data.meta.title.trim()) {
    errors.push(
      'SEO title is required for publication. Please fill in the SEO title or use the auto-generate feature.'
    );
  } else {
    const titleLength = data.meta.title.trim().length;
    if (titleLength < 30) {
      errors.push(
        `SEO title is too short (${titleLength} characters). Should be 30-60 characters for optimal SEO.`
      );
    } else if (titleLength > 60) {
      errors.push(
        `SEO title is too long (${titleLength} characters). Should be 30-60 characters for optimal SEO.`
      );
    }
  }

  // Validate SEO description with plugin-compatible length requirements
  if (!data.meta.description || !data.meta.description.trim()) {
    errors.push(
      'SEO description is required for publication. Please fill in the SEO description or use the auto-generate feature.'
    );
  } else {
    const descLength = data.meta.description.trim().length;
    if (descLength < 100) {
      errors.push(
        `SEO description is too short (${descLength} characters). Should be 100-160 characters for optimal SEO.`
      );
    } else if (descLength > 160) {
      errors.push(
        `SEO description is too long (${descLength} characters). Should be 100-160 characters for optimal SEO.`
      );
    }
  }

  // Validate SEO meta image
  if (!data.meta.image) {
    errors.push(
      'SEO meta image is required for publication. Please upload an image or use the auto-generate feature.'
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Validates German content for publication
 */
export function validateGermanContent(data: any): ArticleValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check if German translation flag is enabled
  if (!data.hasGermanTranslation) {
    errors.push(
      'German translation is required for publication. Please enable the German translation flag and complete the German content.'
    );
    return { isValid: false, errors, warnings };
  }

  // Check if German tab exists
  if (!data.germanTab) {
    errors.push(
      'German content tab is missing. Please complete the German translation.'
    );
    return { isValid: false, errors, warnings };
  }

  // Validate German title
  if (!data.germanTab.germanTitle || !data.germanTab.germanTitle.trim()) {
    errors.push(
      'German title is required for publication. Please provide a German title.'
    );
  }

  // Validate German summary
  if (!data.germanTab.germanSummary || !data.germanTab.germanSummary.trim()) {
    errors.push(
      'German summary is required for publication. Please provide a German summary.'
    );
  }

  // Validate German content (Lexical format)
  const germanContent = data.germanTab.germanContent;
  if (!germanContent) {
    errors.push(
      'German content is required for publication. Please provide German content.'
    );
  } else {
    // Check if Lexical content has actual content (not just empty structure)
    const hasContent = hasLexicalContent(germanContent);
    if (!hasContent) {
      errors.push(
        'German content appears to be empty. Please provide meaningful German content.'
      );
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Helper function to check if Lexical content has actual content
 */
function hasLexicalContent(lexicalData: any): boolean {
  if (!lexicalData) return false;

  // Handle both string and object formats
  let content = lexicalData;
  if (typeof lexicalData === 'string') {
    try {
      content = JSON.parse(lexicalData);
    } catch {
      return lexicalData.trim().length > 0;
    }
  }

  // Check if content has root and children
  if (!content.root || !content.root.children) return false;

  // Recursively check if there's actual text content
  function hasTextContent(node: any): boolean {
    if (!node) return false;

    // If node has text, check if it's not empty
    if (node.text && node.text.trim().length > 0) {
      return true;
    }

    // If node has children, check them recursively
    if (node.children && Array.isArray(node.children)) {
      return node.children.some(hasTextContent);
    }

    return false;
  }

  return content.root.children.some(hasTextContent);
}

/**
 * Main validation function for article publication
 */
export function validateArticleForPublication(
  context: ArticleValidationContext
): ArticleValidationResult {
  const { data, operation, originalDoc } = context;

  // Validate when transitioning to published status via native PayloadCMS system
  // With simplified workflow, only native _status controls publication
  const isNativePublishing =
    data._status === 'published' &&
    (operation === 'create' || originalDoc?._status !== 'published');

  // Only validate when transitioning to published status
  const isTransitioningToPublished = isNativePublishing;

  // If not transitioning to published, skip validation
  if (!isTransitioningToPublished) {
    return { isValid: true, errors: [], warnings: [] };
  }

  const allErrors: string[] = [];
  const allWarnings: string[] = [];

  // Validate featured image
  const featuredImageResult = validateFeaturedImage(data);
  allErrors.push(...featuredImageResult.errors);
  allWarnings.push(...featuredImageResult.warnings);

  // Validate SEO fields
  const seoResult = validateSEOFields(data);
  allErrors.push(...seoResult.errors);
  allWarnings.push(...seoResult.warnings);

  // Validate German content
  const germanResult = validateGermanContent(data);
  allErrors.push(...germanResult.errors);
  allWarnings.push(...germanResult.warnings);

  // Validate categories
  const categoriesResult = validateCategories(data);
  allErrors.push(...categoriesResult.errors);
  allWarnings.push(...categoriesResult.warnings);

  // Validate placement (tier)
  const placementResult = validatePlacement(data);
  allErrors.push(...placementResult.errors);
  allWarnings.push(...placementResult.warnings);

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings,
  };
}

/**
 * Helper function to format validation errors for PayloadCMS
 * Creates a proper PayloadCMS ValidationError that displays in admin UI
 */
export function formatValidationError(result: ArticleValidationResult): Error {
  const errorMessage = [
    '🚫 Cannot Publish Article',
    '',
    'The following requirements must be met:',
    '',
    ...result.errors.map(error => `• ${error}`),
    '',
    '💡 Please resolve these issues before publishing.',
  ].join('\n');

  // Create a validation error with proper formatting for PayloadCMS admin UI
  const error = new Error(errorMessage);
  error.name = 'ValidationError';

  // Add additional properties that PayloadCMS might use for better error display
  (error as any).status = 400;
  (error as any).isOperational = true;

  return error;
}

/**
 * Helper function to log validation warnings
 */
export function logValidationWarnings(
  result: ArticleValidationResult,
  articleTitle?: string
): void {
  if (result.warnings.length > 0) {
    console.warn(
      `Article validation warnings for "${articleTitle || 'Unknown'}":`,
      result.warnings
    );
  }
}
