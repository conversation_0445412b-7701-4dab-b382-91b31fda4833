/**
 * Type-safe date handling with branded types
 * Provides compile-time safety for date operations
 */

// Branded types for different date formats
export type ValidDateString = string & { __brand: 'ValidDateString' };
export type ISODateString = string & { __brand: 'ISODateString' };
export type TimestampString = string & { __brand: 'TimestampString' };
export type ValidDate = Date & { __brand: 'ValidDate' };

// Union type for all valid date inputs
export type DateInput =
  | ValidDateString
  | ISODateString
  | TimestampString
  | ValidDate
  | Date
  | string; // TODO: Replace with more specific date string types for better type safety

// Locale types
export type SupportedLocale = 'en' | 'de';

// Time unit types for relative time
export type TimeUnit =
  | 'seconds'
  | 'minutes'
  | 'hours'
  | 'days'
  | 'weeks'
  | 'months'
  | 'years';

// Relative time configuration
export interface RelativeTimeConfig {
  readonly locale: SupportedLocale;
  readonly maxUnit?: TimeUnit;
  readonly precise?: boolean;
}

// Date formatting options
export interface DateFormatOptions {
  readonly locale: SupportedLocale;
  readonly includeTime?: boolean;
  readonly includeSeconds?: boolean;
  readonly format?: 'full' | 'long' | 'medium' | 'short';
}

// Type guards for date validation
export function isValidDateString(value: unknown): value is ValidDateString {
  if (typeof value !== 'string') return false;
  const date = new Date(value);
  return !isNaN(date.getTime()) && date.toString() !== 'Invalid Date';
}

export function isISODateString(value: unknown): value is ISODateString {
  if (typeof value !== 'string') return false;
  // Check for ISO 8601 format (YYYY-MM-DDTHH:mm:ss.sssZ or YYYY-MM-DD)
  const isoRegex =
    /^(\d{4})-(\d{2})-(\d{2})(?:T(\d{2}):(\d{2}):(\d{2})(?:\.(\d{3}))?(?:Z|[+-]\d{2}:\d{2})?)?$/;
  return isoRegex.test(value) && isValidDateString(value);
}

export function isTimestampString(value: unknown): value is TimestampString {
  if (typeof value !== 'string') return false;
  const timestamp = parseInt(value, 10);
  return !isNaN(timestamp) && timestamp > 0;
}

export function isValidDate(value: unknown): value is ValidDate {
  return value instanceof Date && !isNaN(value.getTime());
}

export function isDateInput(value: unknown): value is DateInput {
  return (
    isValidDateString(value) ||
    isISODateString(value) ||
    isTimestampString(value) ||
    isValidDate(value) ||
    value instanceof Date
  );
}

// Type conversion functions
export function toValidDateString(value: string): ValidDateString | null {
  return isValidDateString(value) ? value : null;
}

export function toISODateString(value: string): ISODateString | null {
  return isISODateString(value) ? value : null;
}

export function toValidDate(value: DateInput): ValidDate | null {
  try {
    let date: Date;

    if (isValidDate(value)) {
      return value;
    }

    if (value instanceof Date) {
      date = value;
    } else if (isTimestampString(value)) {
      date = new Date(parseInt(value, 10));
    } else {
      date = new Date(value);
    }

    if (isNaN(date.getTime())) {
      return null;
    }

    return date as ValidDate;
  } catch {
    return null;
  }
}

// Safe date parsing with error handling
export function parseDateSafely(value: unknown): {
  success: boolean;
  date: ValidDate | null;
  error?: string;
} {
  try {
    if (!isDateInput(value)) {
      return {
        success: false,
        date: null,
        error: 'Invalid date input type',
      };
    }

    const validDate = toValidDate(value);
    if (!validDate) {
      return {
        success: false,
        date: null,
        error: 'Invalid date value',
      };
    }

    return {
      success: true,
      date: validDate,
    };
  } catch (error) {
    return {
      success: false,
      date: null,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Branded type creators (for when you know the value is valid)
export function createValidDateString(value: string): ValidDateString {
  if (!isValidDateString(value)) {
    throw new Error('Invalid date string');
  }
  return value;
}

export function createISODateString(value: string): ISODateString {
  if (!isISODateString(value)) {
    throw new Error('Invalid ISO date string');
  }
  return value;
}

export function createValidDate(value: DateInput): ValidDate {
  const validDate = toValidDate(value);
  if (!validDate) {
    throw new Error('Invalid date');
  }
  return validDate;
}

// Utility types for function signatures
export type RelativeTimeFunction<T extends DateInput = DateInput> = (
  date: T,
  locale?: SupportedLocale
) => string;

export type DateFormatFunction<T extends DateInput = DateInput> = (
  date: T,
  options?: DateFormatOptions
) => string;

// Error types
export class DateValidationError extends Error {
  constructor(
    message: string,
    public readonly input: unknown
  ) {
    super(message);
    this.name = 'DateValidationError';
  }
}

export class DateFormatError extends Error {
  constructor(
    message: string,
    public readonly date: DateInput
  ) {
    super(message);
    this.name = 'DateFormatError';
  }
}

// Constants for common date operations
export const DATE_CONSTANTS = {
  MS_PER_SECOND: 1000,
  MS_PER_MINUTE: 60 * 1000,
  MS_PER_HOUR: 60 * 60 * 1000,
  MS_PER_DAY: 24 * 60 * 60 * 1000,
  MS_PER_WEEK: 7 * 24 * 60 * 60 * 1000,
  MS_PER_MONTH: 30 * 24 * 60 * 60 * 1000, // Approximate
  MS_PER_YEAR: 365 * 24 * 60 * 60 * 1000, // Approximate
} as const;

// Utility to get time difference in specific units
export function getTimeDifference(
  from: DateInput,
  to: DateInput,
  unit: TimeUnit
): number {
  const fromDate = toValidDate(from);
  const toDate = toValidDate(to);

  if (!fromDate || !toDate) {
    throw new DateValidationError('Invalid date input', { from, to });
  }

  const diffMs = toDate.getTime() - fromDate.getTime();

  switch (unit) {
    case 'seconds':
      return Math.floor(diffMs / DATE_CONSTANTS.MS_PER_SECOND);
    case 'minutes':
      return Math.floor(diffMs / DATE_CONSTANTS.MS_PER_MINUTE);
    case 'hours':
      return Math.floor(diffMs / DATE_CONSTANTS.MS_PER_HOUR);
    case 'days':
      return Math.floor(diffMs / DATE_CONSTANTS.MS_PER_DAY);
    case 'weeks':
      return Math.floor(diffMs / DATE_CONSTANTS.MS_PER_WEEK);
    case 'months':
      return Math.floor(diffMs / DATE_CONSTANTS.MS_PER_MONTH);
    case 'years':
      return Math.floor(diffMs / DATE_CONSTANTS.MS_PER_YEAR);
    default:
      throw new Error(`Unsupported time unit: ${unit}`);
  }
}
