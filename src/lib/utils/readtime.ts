/**
 * Reading Time Calculation Utility
 *
 * Provides consistent reading time calculation for pre-computing
 * the readTimeMinutes field to eliminate expensive on-the-fly calculations
 */

// Standard reading speed: 200 words per minute (average adult)
const WORDS_PER_MINUTE = 200;

/**
 * Extract text content from Lexical JSON structure
 */
function extractTextFromLexical(lexicalContent: any): string {
  if (!lexicalContent) return '';

  try {
    // Handle root structure
    if (lexicalContent.root?.children) {
      return extractTextFromNodes(lexicalContent.root.children);
    }

    // Handle direct children array
    if (Array.isArray(lexicalContent.children)) {
      return extractTextFromNodes(lexicalContent.children);
    }

    // Handle single node
    if (lexicalContent.type === 'text') {
      return lexicalContent.text || '';
    }

    return '';
  } catch (error) {
    console.warn('Error extracting text from Lexical content:', error);
    return '';
  }
}

/**
 * Recursively extract text from array of Lexical nodes
 */
function extractTextFromNodes(nodes: any[]): string {
  if (!Array.isArray(nodes)) return '';

  return nodes
    .map((node: any) => {
      if (node.type === 'text') {
        return node.text || '';
      }

      if (node.children && Array.isArray(node.children)) {
        return extractTextFromNodes(node.children);
      }

      return '';
    })
    .join(' ');
}

/**
 * Extract text from HTML content
 */
function extractTextFromHTML(htmlContent: string): string {
  if (!htmlContent) return '';

  try {
    // Remove HTML tags and decode entities
    return htmlContent
      .replace(/<[^>]*>/g, ' ') // Remove HTML tags
      .replace(/&[a-zA-Z0-9#]+;/g, ' ') // Remove HTML entities
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  } catch (error) {
    console.warn('Error extracting text from HTML:', error);
    return '';
  }
}

/**
 * Calculate reading time from various content formats
 *
 * @param content Content in various formats (Lexical, HTML, or plain text)
 * @returns Reading time in minutes (minimum 1)
 */
export function calculateReadingTime(content: any): number {
  if (!content) return 1;

  let textContent = '';

  try {
    // Handle different content types
    if (typeof content === 'string') {
      // Assume HTML or plain text
      if (content.includes('<')) {
        textContent = extractTextFromHTML(content);
      } else {
        textContent = content;
      }
    } else if (typeof content === 'object') {
      // Assume Lexical JSON format
      textContent = extractTextFromLexical(content);
    } else {
      return 1; // Default fallback
    }

    // Count words (split by whitespace and filter empty strings)
    const wordCount = textContent
      .split(/\s+/)
      .filter(word => word.length > 0).length;

    // Calculate reading time (minimum 1 minute)
    const readingTimeMinutes = Math.max(
      1,
      Math.ceil(wordCount / WORDS_PER_MINUTE)
    );

    return readingTimeMinutes;
  } catch (error) {
    console.error('Error calculating reading time:', error);
    return 1; // Default fallback
  }
}

/**
 * Calculate reading time from article content tabs
 * Prioritizes German content, then English content
 *
 * @param germanContent German content (Lexical format)
 * @param englishContent English content (Lexical format)
 * @returns Reading time in minutes
 */
export function calculateArticleReadingTime(
  germanContent?: any,
  englishContent?: any
): number {
  // Prioritise German content if available
  if (germanContent) {
    return calculateReadingTime(germanContent);
  }

  // Fallback to English content
  if (englishContent) {
    return calculateReadingTime(englishContent);
  }

  // Default if no content
  return 1;
}

/**
 * Format reading time for display
 *
 * @param minutes Reading time in minutes
 * @param locale Language locale for formatting
 * @returns Formatted reading time string
 */
export function formatReadingTime(
  minutes: number,
  locale: 'de' | 'en' = 'de'
): string {
  if (locale === 'de') {
    return `${minutes} Min. Lesezeit`;
  } else {
    return minutes === 1 ? '1 min read' : `${minutes} min read`;
  }
}
