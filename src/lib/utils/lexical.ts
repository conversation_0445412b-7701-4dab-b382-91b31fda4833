/**
 * Utility functions for converting content to Lexical editor format
 * Based on PayloadCMS Lexical documentation and official structure
 */

import {
  convertMarkdownToLexical,
  editorConfigFactory,
} from '@payloadcms/richtext-lexical';
import config from '../../payload.config';

/**
 * Convert plain text to Lexical editor state JSON
 * This creates a basic Lexical document with paragraphs following PayloadCMS structure
 * Enhanced to handle structured content with better paragraph detection
 */
export function textToLexical(text: string): object {
  if (!text || typeof text !== 'string') {
    return {
      root: {
        children: [
          {
            children: [],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'paragraph',
            textFormat: 0,
            version: 1,
          },
        ],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'root',
        version: 1,
      },
    };
  }

  // Enhanced content structure detection
  const structuredContent = detectAndStructureContent(text);

  const children = structuredContent.map(item => {
    if (item.type === 'heading') {
      return {
        children: [
          {
            detail: 0,
            format: 0,
            mode: 'normal',
            style: '',
            text: item.text,
            type: 'text',
            version: 1,
          },
        ],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'heading',
        tag: item.level || 'h3',
        version: 1,
      };
    } else {
      return {
        children: [
          {
            detail: 0,
            format: 0,
            mode: 'normal',
            style: '',
            text: item.text,
            type: 'text',
            version: 1,
          },
        ],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'paragraph',
        textFormat: 0,
        version: 1,
      };
    }
  });

  return {
    root: {
      children,
      direction: 'ltr',
      format: '',
      indent: 0,
      type: 'root',
      version: 1,
    },
  };
}

/**
 * Detect and structure content to identify headings and paragraphs
 * Handles financial news content with multiple announcements
 * Enhanced to handle concatenated content without proper line breaks
 */
function detectAndStructureContent(
  text: string
): Array<{ type: 'heading' | 'paragraph'; text: string; level?: string }> {
  // First, try to split content that's been concatenated without proper line breaks
  const preprocessedText = preprocessConcatenatedContent(text);

  const lines = preprocessedText
    .split(/\n+/)
    .map(line => line.trim())
    .filter(line => line.length > 0);
  const structured: Array<{
    type: 'heading' | 'paragraph';
    text: string;
    level?: string;
  }> = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // Detect headings based on common patterns
    if (isLikelyHeading(line)) {
      structured.push({
        type: 'heading',
        text: line,
        level: 'h3',
      });
    } else {
      // Check if this line should be combined with the next lines to form a paragraph
      let paragraphText = line;
      let j = i + 1;

      // Combine lines until we hit a heading or a natural break
      while (
        j < lines.length &&
        !isLikelyHeading(lines[j]) &&
        shouldCombineLines(lines[j - 1], lines[j])
      ) {
        paragraphText += ` ${lines[j]}`;
        j++;
      }

      structured.push({
        type: 'paragraph',
        text: paragraphText,
      });

      // Skip the lines we've already processed
      i = j - 1;
    }
  }

  return structured;
}

/**
 * Preprocess concatenated content to add line breaks where they should be
 * This handles content that has been improperly concatenated
 */
function preprocessConcatenatedContent(text: string): string {
  let processed = text;

  // Add line breaks before markdown headers that are concatenated
  processed = processed.replace(/([^#])###\s*([A-Z])/g, '$1\n\n### $2');

  // Add line breaks before likely section starts
  processed = processed.replace(
    /(Financial News Overview|Push Notifications|Index Radar)/g,
    '\n\n$1'
  );

  // Add line breaks before URLs that start new sections
  processed = processed.replace(
    /(\]\([^)]+\))([A-Z][^.!?]*[A-Z])/g,
    '$1\n\n$2'
  );

  // Add line breaks before company announcements that start with timestamps
  processed = processed.replace(
    /(\w+)\s*-\s*(\d{1,2}:\d{2})\s*\[([A-Z\s]+)\]/g,
    '\n\n$1 - $2 [$3]'
  );

  // Add line breaks before "The recent rise" and similar paragraph starters
  processed = processed.replace(
    /(The recent rise|The financial world|However, Morningstar)/g,
    '\n\n$1'
  );

  // Clean up orphaned markdown headers
  processed = processed.replace(/^\s*###\s*$/gm, '');

  // Clean up excessive line breaks
  processed = processed.replace(/\n{3,}/g, '\n\n');

  return processed.trim();
}

/**
 * Determine if a line is likely a heading
 */
function isLikelyHeading(line: string): boolean {
  // Skip orphaned markdown headers
  if (line.trim() === '###' || line.trim() === '##' || line.trim() === '#') {
    return false;
  }

  // Common heading patterns in financial news
  const headingPatterns = [
    /^Ad Hoc Announcements?/i,
    /^Financial News/i,
    /^Market Update/i,
    /^Breaking News/i,
    /^Company Announcements?/i,
    /^Press Release/i,
    /^Earnings Report/i,
    /^Stock Alert/i,
    /^Push Notifications?/i,
    /^Index Radar/i,
    // Markdown headers with content
    /^#{1,3}\s+.+/,
  ];

  // Check for heading patterns
  if (headingPatterns.some(pattern => pattern.test(line))) {
    return true;
  }

  // Short lines without URLs that end with a colon might be headings
  if (
    line.length < 80 &&
    !line.includes('http') &&
    line.endsWith(':') &&
    line.split(' ').length <= 5
  ) {
    return true;
  }

  return false;
}

/**
 * Determine if two lines should be combined into the same paragraph
 */
function shouldCombineLines(line1: string, line2: string): boolean {
  // Don't combine if the second line looks like a new announcement or URL
  if (line2.includes('http') && line2.includes('[') && line2.includes(']')) {
    return false;
  }

  // Don't combine if the second line starts with a capital and looks like a new sentence
  if (/^[A-Z]/.test(line2) && line1.endsWith('.')) {
    return false;
  }

  // Don't combine if lines are very different in length (might be separate items)
  if (Math.abs(line1.length - line2.length) > 200) {
    return false;
  }

  return true;
}

/**
 * Convert Lexical editor state to plain text
 * Useful for extracting text content from Lexical JSON
 */
export function lexicalToText(lexicalData: unknown): string {
  if (!lexicalData || typeof lexicalData !== 'object' || lexicalData === null) {
    return '';
  }

  const data = lexicalData as { root?: { children?: unknown[] } };
  if (!data.root || !data.root.children) {
    return '';
  }

  const extractTextFromNode = (node: unknown): string => {
    if (!node || typeof node !== 'object') {
      return '';
    }

    const nodeObj = node as {
      type?: string;
      text?: string;
      children?: unknown[];
    };

    if (nodeObj.type === 'text') {
      return nodeObj.text || '';
    }

    if (nodeObj.children && Array.isArray(nodeObj.children)) {
      return nodeObj.children.map(extractTextFromNode).join('');
    }

    return '';
  };

  const paragraphs = data.root.children
    .map((child: unknown) => extractTextFromNode(child))
    .filter((text: string) => text.trim().length > 0);

  return paragraphs.join('\n\n');
}

/**
 * Validate if content is already in Lexical format
 */
export function isLexicalFormat(content: unknown): boolean {
  if (!content || typeof content !== 'object' || content === null) {
    return false;
  }

  const data = content as { root?: { children?: unknown[] } };
  return !!(
    data.root &&
    typeof data.root === 'object' &&
    Array.isArray(data.root.children)
  );
}

/**
 * Convert Markdown to Lexical editor state using PayloadCMS built-in conversion
 * This preserves all Markdown formatting (headers, bold, italic, lists, etc.)
 */
export async function markdownToLexical(markdown: string): Promise<object> {
  if (!markdown || typeof markdown !== 'string') {
    return textToLexical('');
  }

  try {
    // Get the resolved config
    const resolvedConfig = await config;

    // Create editor config with the same features as defaultLexical
    const {
      BoldFeature,
      ItalicFeature,
      ParagraphFeature,
      UnderlineFeature,
      HeadingFeature,
    } = await import('@payloadcms/richtext-lexical');

    const editorConfig = await editorConfigFactory.fromFeatures({
      config: resolvedConfig,
      features: [
        ParagraphFeature(),
        HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3'] }),
        BoldFeature(),
        ItalicFeature(),
        UnderlineFeature(),
      ],
    });

    // Convert markdown to Lexical using PayloadCMS built-in function
    const lexicalResult = convertMarkdownToLexical({
      editorConfig,
      markdown,
    });

    return lexicalResult;
  } catch (error) {
    console.error('Error converting markdown to Lexical:', error);
    // Fallback to plain text conversion
    return textToLexical(markdown);
  }
}

/**
 * Enhanced markdown to Lexical conversion with AI assistance
 * Handles complex formatting that basic conversion might miss
 */
export async function markdownToLexicalWithAI(
  markdown: string
): Promise<object> {
  if (!markdown || typeof markdown !== 'string') {
    return textToLexical('');
  }

  try {
    // First try the standard PayloadCMS conversion
    const standardResult = await markdownToLexical(markdown);

    // For now, return the standard result
    // In the future, we could add AI enhancement here if needed
    return standardResult;
  } catch (error) {
    console.error(
      'Error in AI-enhanced markdown to Lexical conversion:',
      error
    );
    // Fallback to plain text conversion
    return textToLexical(markdown);
  }
}

/**
 * Ensure content is in Lexical format, converting if necessary
 * Enhanced version that detects markdown and uses appropriate conversion
 */
export async function ensureLexicalFormat(content: unknown): Promise<object> {
  if (isLexicalFormat(content)) {
    return content as object;
  }

  if (typeof content === 'string') {
    // Check if content looks like Markdown (has headers, bold, etc.)
    const hasMarkdownSyntax = /[#*_`[\]]/g.test(content);

    if (hasMarkdownSyntax) {
      // Use AI-enhanced conversion for complex markdown
      return await markdownToLexicalWithAI(content);
    } else {
      return textToLexical(content);
    }
  }

  // If it's neither string nor valid Lexical, return empty Lexical document
  return textToLexical('');
}

/**
 * Convert Lexical editor state to HTML while preserving all structure
 * This maintains lists, tables, headings, and formatting for translation
 */
export function lexicalToHTML(lexicalData: unknown): string {
  if (!lexicalData || typeof lexicalData !== 'object' || lexicalData === null) {
    return '';
  }

  const data = lexicalData as { root?: { children?: unknown[] } };
  if (!data.root || !data.root.children) {
    return '';
  }

  const convertNodeToHTML = (node: unknown): string => {
    if (!node || typeof node !== 'object') {
      return '';
    }

    const nodeObj = node as {
      type?: string;
      text?: string;
      children?: unknown[];
      tag?: string;
      format?: number;
      listType?: string;
      textFormat?: number;
      start?: number;
    };

    switch (nodeObj.type) {
      case 'text':
        let text = nodeObj.text || '';

        // Apply text formatting based on format flags
        if (nodeObj.format && typeof nodeObj.format === 'number') {
          if (nodeObj.format & 1) text = `<strong>${text}</strong>`;
          if (nodeObj.format & 2) text = `<em>${text}</em>`;
          if (nodeObj.format & 4) text = `<u>${text}</u>`;
          if (nodeObj.format & 8) text = `<del>${text}</del>`;
        }

        return text;

      case 'paragraph':
        const paragraphContent = nodeObj.children
          ? nodeObj.children.map(convertNodeToHTML).join('')
          : '';
        return paragraphContent ? `<p>${paragraphContent}</p>` : '';

      case 'heading':
        const headingTag = nodeObj.tag || 'h2';
        const headingContent = nodeObj.children
          ? nodeObj.children.map(convertNodeToHTML).join('')
          : '';
        return headingContent
          ? `<${headingTag}>${headingContent}</${headingTag}>`
          : '';

      case 'list':
        const listTag = nodeObj.listType === 'number' ? 'ol' : 'ul';
        const listContent = nodeObj.children
          ? nodeObj.children.map(convertNodeToHTML).join('')
          : '';
        const startAttr =
          nodeObj.listType === 'number' && nodeObj.start
            ? ` start="${nodeObj.start}"`
            : '';
        return listContent
          ? `<${listTag}${startAttr}>${listContent}</${listTag}>`
          : '';

      case 'listitem':
        const listItemContent = nodeObj.children
          ? nodeObj.children.map(convertNodeToHTML).join('')
          : '';
        return listItemContent ? `<li>${listItemContent}</li>` : '';

      case 'table':
        const tableContent = nodeObj.children
          ? nodeObj.children.map(convertNodeToHTML).join('')
          : '';
        return tableContent ? `<table>${tableContent}</table>` : '';

      case 'tablerow':
        const rowContent = nodeObj.children
          ? nodeObj.children.map(convertNodeToHTML).join('')
          : '';
        return rowContent ? `<tr>${rowContent}</tr>` : '';

      case 'tablecell':
        const cellContent = nodeObj.children
          ? nodeObj.children.map(convertNodeToHTML).join('')
          : '';
        return cellContent ? `<td>${cellContent}</td>` : '';

      case 'tableheader':
        const headerContent = nodeObj.children
          ? nodeObj.children.map(convertNodeToHTML).join('')
          : '';
        return headerContent ? `<th>${headerContent}</th>` : '';

      case 'blockquote':
        const blockquoteContent = nodeObj.children
          ? nodeObj.children.map(convertNodeToHTML).join('')
          : '';
        return blockquoteContent
          ? `<blockquote>${blockquoteContent}</blockquote>`
          : '';

      case 'horizontalrule':
        return '<hr>';

      case 'linebreak':
        return '<br>';

      case 'link':
        const linkContent = nodeObj.children
          ? nodeObj.children.map(convertNodeToHTML).join('')
          : '';
        const url = (nodeObj as any).url || '#';
        const rel = (nodeObj as any).rel || '';
        const relAttr = rel ? ` rel="${rel}"` : '';
        return linkContent
          ? `<a href="${url}"${relAttr}>${linkContent}</a>`
          : '';

      case 'upload':
        // Handle media uploads - convert to img tag with alt text
        const uploadData = nodeObj as any;
        const src = uploadData.value?.url || uploadData.src || '';
        const alt = uploadData.value?.alt || uploadData.alt || 'Image';
        const width = uploadData.value?.width
          ? ` width="${uploadData.value.width}"`
          : '';
        const height = uploadData.value?.height
          ? ` height="${uploadData.value.height}"`
          : '';
        return src ? `<img src="${src}" alt="${alt}"${width}${height}>` : '';

      default:
        // For unknown node types, try to process children
        if (nodeObj.children && Array.isArray(nodeObj.children)) {
          const childContent = nodeObj.children.map(convertNodeToHTML).join('');
          return childContent ? `<div>${childContent}</div>` : '';
        }
        return '';
    }
  };

  // Convert all root children to HTML
  const htmlContent = data.root.children
    .map(convertNodeToHTML)
    .filter(html => html.trim().length > 0)
    .join('\n');

  return htmlContent;
}
