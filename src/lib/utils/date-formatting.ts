/**
 * Date Formatting Utility
 * Provides exact date formatting for tooltips and accessibility
 * with German/English localization support
 */

import type { DateInput, SupportedLocale } from './date-types';
import { toValidDate, DateValidationError } from './date-types';

/**
 * Format a date for tooltip display with full date and time
 * @param date Date string or Date object
 * @param locale Language locale ('en' or 'de')
 * @returns Formatted date string for tooltip
 */
export function formatExactDate(
  date: DateInput,
  locale: SupportedLocale = 'en'
): string {
  try {
    const dateObj = new Date(date);

    // Handle invalid dates
    if (isNaN(dateObj.getTime())) {
      return locale === 'de' ? 'Ungültiges Datum' : 'Invalid date';
    }

    // Format options for different locales
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    };

    // Use appropriate locale
    const localeString = locale === 'de' ? 'de-DE' : 'en-GB';
    return dateObj.toLocaleDateString(localeString, options);
  } catch (error) {
    console.error('Error formatting exact date:', error);
    return locale === 'de' ? 'Ungültiges Datum' : 'Invalid date';
  }
}

/**
 * Format a date for short display (just date, no time)
 * @param date Date string or Date object
 * @param locale Language locale ('en' or 'de')
 * @returns Formatted date string
 */
export function formatShortDate(
  date: DateInput,
  locale: SupportedLocale = 'en'
): string {
  try {
    const dateObj = new Date(date);

    // Handle invalid dates
    if (isNaN(dateObj.getTime())) {
      return locale === 'de' ? 'Ungültiges Datum' : 'Invalid date';
    }

    // Format options for short date
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    };

    // Use appropriate locale
    const localeString = locale === 'de' ? 'de-DE' : 'en-GB';
    return dateObj.toLocaleDateString(localeString, options);
  } catch (error) {
    console.error('Error formatting short date:', error);
    return locale === 'de' ? 'Ungültiges Datum' : 'Invalid date';
  }
}

/**
 * Format a date for ISO display (YYYY-MM-DD)
 * @param date Date string or Date object
 * @returns ISO formatted date string
 */
export function formatISODate(date: DateInput): string {
  try {
    const dateObj = new Date(date);

    // Handle invalid dates
    if (isNaN(dateObj.getTime())) {
      return 'Invalid date';
    }

    return dateObj.toISOString().split('T')[0];
  } catch (error) {
    console.error('Error formatting ISO date:', error);
    return 'Invalid date';
  }
}

/**
 * Create an accessible aria-label for published dates
 * @param date Date string or Date object
 * @param locale Language locale ('en' or 'de')
 * @returns Accessible aria-label string
 */
export function getPublishedDateAriaLabel(
  date: DateInput,
  locale: SupportedLocale = 'en'
): string {
  try {
    const dateObj = new Date(date);

    // Handle invalid dates
    if (isNaN(dateObj.getTime())) {
      return locale === 'de'
        ? 'Ungültiges Veröffentlichungsdatum'
        : 'Invalid published date';
    }

    const formattedDate = formatExactDate(date, locale);
    const prefix = locale === 'de' ? 'Veröffentlicht am' : 'Published on';

    return `${prefix} ${formattedDate}`;
  } catch (error) {
    console.error('Error creating aria-label:', error);
    return locale === 'de'
      ? 'Ungültiges Veröffentlichungsdatum'
      : 'Invalid published date';
  }
}

/**
 * Check if a date is today
 * @param date Date string or Date object
 * @returns Boolean indicating if date is today
 */
export function isToday(date: DateInput): boolean {
  try {
    const today = new Date();
    const checkDate = new Date(date);

    return (
      today.getFullYear() === checkDate.getFullYear() &&
      today.getMonth() === checkDate.getMonth() &&
      today.getDate() === checkDate.getDate()
    );
  } catch {
    return false;
  }
}

/**
 * Check if a date is yesterday
 * @param date Date string or Date object
 * @returns Boolean indicating if date is yesterday
 */
export function isYesterday(date: DateInput): boolean {
  try {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const checkDate = new Date(date);

    return (
      yesterday.getFullYear() === checkDate.getFullYear() &&
      yesterday.getMonth() === checkDate.getMonth() &&
      yesterday.getDate() === checkDate.getDate()
    );
  } catch {
    return false;
  }
}

/**
 * Get a human-readable date description
 * @param date Date string or Date object
 * @param locale Language locale ('en' or 'de')
 * @returns Human-readable date description
 */
export function getDateDescription(
  date: DateInput,
  locale: SupportedLocale = 'en'
): string {
  try {
    if (isToday(date)) {
      return locale === 'de' ? 'Heute' : 'Today';
    }

    if (isYesterday(date)) {
      return locale === 'de' ? 'Gestern' : 'Yesterday';
    }

    return formatShortDate(date, locale);
  } catch (error) {
    console.error('Error getting date description:', error);
    return locale === 'de' ? 'Ungültiges Datum' : 'Invalid date';
  }
}
