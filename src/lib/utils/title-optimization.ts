/**
 * Title Optimization & Processing Utilities
 *
 * Comprehensive title cleaning and optimization utilities to remove foreign characters,
 * emojis, and special Unicode from source titles, ensuring direct English translations
 * that are SEO-optimized and clean.
 *
 * Features:
 * - Source title cleaning (emojis, asterisks, foreign characters)
 * - English title optimization (artifacts, capitalization)
 * - SEO quality validation with 100-point scoring system
 * - German financial content optimization
 * - Intelligent title generation from content
 *
 * <AUTHOR> Blick Development Team
 * @created 2025-06-29
 * @sprint Sprint 4: Title Optimization & Processing
 */

/**
 * Quality validation result interface
 */
export interface TitleQualityResult {
  score: number; // 0-100
  isOptimal: boolean;
  issues: TitleIssue[];
  recommendations: string[];
  metadata: {
    length: number;
    wordCount: number;
    hasEmojis: boolean;
    hasForeignChars: boolean;
    hasExcessivePunctuation: boolean;
    isFinancialContent: boolean;
    isGermanContent: boolean;
    seoOptimized: boolean;
  };
}

/**
 * Individual title issue
 */
export interface TitleIssue {
  type:
    | 'length'
    | 'characters'
    | 'punctuation'
    | 'capitalization'
    | 'seo'
    | 'content';
  severity: 'low' | 'medium' | 'high';
  description: string;
  suggestion: string;
  impact: number; // Points deducted from score
}

/**
 * Title generation options
 */
export interface TitleGenerationOptions {
  maxLength?: number;

  preserveGermanTerms?: boolean;
  seoOptimized?: boolean;
  contentType?: 'article' | 'analysis' | 'news' | 'report';
}

/**
 * Clean source title by removing problematic characters
 * Removes emojis, asterisks, excessive punctuation, and special Unicode
 */
export function cleanSourceTitle(title: string): string {
  if (!title || typeof title !== 'string') {
    return '';
  }

  let cleaned = title;

  // Remove emojis and emoji-like characters
  cleaned = cleaned.replace(/[\u{1F600}-\u{1F64F}]/gu, ''); // Emoticons
  cleaned = cleaned.replace(/[\u{1F300}-\u{1F5FF}]/gu, ''); // Misc Symbols
  cleaned = cleaned.replace(/[\u{1F680}-\u{1F6FF}]/gu, ''); // Transport
  cleaned = cleaned.replace(/[\u{1F1E0}-\u{1F1FF}]/gu, ''); // Flags
  cleaned = cleaned.replace(/[\u{2600}-\u{26FF}]/gu, ''); // Misc symbols
  cleaned = cleaned.replace(/[\u{2700}-\u{27BF}]/gu, ''); // Dingbats
  cleaned = cleaned.replace(/[\u{FE00}-\u{FE0F}]/gu, ''); // Variation Selectors
  cleaned = cleaned.replace(/[\u{1F900}-\u{1F9FF}]/gu, ''); // Supplemental Symbols

  // Remove asterisks and excessive formatting
  cleaned = cleaned.replace(/\*{2,}/g, ''); // Multiple asterisks
  cleaned = cleaned.replace(/\*([^*]+)\*/g, '$1'); // Single asterisk wrapping
  cleaned = cleaned.replace(/^\*+|\*+$/g, ''); // Leading/trailing asterisks

  // Remove excessive punctuation
  cleaned = cleaned.replace(/!{2,}/g, '!'); // Multiple exclamation marks
  cleaned = cleaned.replace(/\?{2,}/g, '?'); // Multiple question marks
  cleaned = cleaned.replace(/\.{3,}/g, '...'); // Multiple dots to ellipsis
  cleaned = cleaned.replace(/[.,;:]{2,}/g, '.'); // Multiple punctuation

  // Remove special formatting characters and quotes
  cleaned = cleaned.replace(/[""''„"]/g, ''); // Remove all quote types
  cleaned = cleaned.replace(/[–—-]/g, ''); // Remove dashes
  cleaned = cleaned.replace(/[…]/g, ''); // Remove ellipsis character
  cleaned = cleaned.replace(/\.{3}/g, ''); // Remove triple dots

  // Remove excessive whitespace and normalize
  cleaned = cleaned.replace(/\s+/g, ' '); // Multiple spaces to single
  cleaned = cleaned.trim(); // Remove leading/trailing whitespace

  // Remove common formatting prefixes
  cleaned = cleaned.replace(/^(BREAKING|EILMELDUNG|UPDATE|NEU):\s*/i, '');
  cleaned = cleaned.replace(/^(🚨|⚡|📈|📊|💰|🔥)\s*/, '');

  return cleaned;
}

/**
 * Clean English title by removing translation artifacts and foreign characters
 * Ensures proper capitalization and removes problematic characters
 */
export function cleanEnglishTitle(title: string): string {
  if (!title || typeof title !== 'string') {
    return '';
  }

  let cleaned = title;

  // First apply basic cleaning
  cleaned = cleanSourceTitle(cleaned);

  // Remove foreign characters while preserving important ones
  // Keep: A-Z, a-z, 0-9, space, basic punctuation, German umlauts (for company names)
  cleaned = cleaned.replace(/[^\w\s\-.,;:!?()&%€$£¥äöüÄÖÜß]/g, '');

  // Remove translation artifacts
  cleaned = cleaned.replace(/\s*\([^)]*\)\s*/g, ' '); // Remove parenthetical content
  cleaned = cleaned.replace(/\s*\[[^\]]*\]\s*/g, ' '); // Remove bracketed content
  cleaned = cleaned.replace(/\s*\{[^}]*\}\s*/g, ' '); // Remove braced content

  // Fix capitalization issues
  cleaned = cleaned.replace(/([a-z])([A-Z])/g, '$1 $2'); // Add space before capitals
  cleaned = cleaned.replace(
    /\b(AND|OR|THE|A|AN|IN|ON|AT|TO|FOR|OF|WITH|BY)\b/g,
    match => match.toLowerCase()
  ); // Lowercase articles/prepositions

  // Ensure first letter is capitalized
  cleaned = cleaned.charAt(0).toUpperCase() + cleaned.slice(1);

  // Clean up whitespace again
  cleaned = cleaned.replace(/\s+/g, ' ').trim();

  return cleaned;
}

/**
 * Validate title quality and provide SEO assessment
 * Returns detailed analysis with score and recommendations
 */
export function validateTitleQuality(title: string): TitleQualityResult {
  if (!title || typeof title !== 'string') {
    return createEmptyQualityResult();
  }

  const issues: TitleIssue[] = [];
  let score = 100; // Start with perfect score and deduct points

  // Basic metrics
  const length = title.length;
  const wordCount = title.split(/\s+/).filter(word => word.length > 0).length;
  const hasEmojis = /[\u{1F600}-\u{1F9FF}]/u.test(title);
  const hasForeignChars = /[^\w\s\-.,;:!?()&%€$£¥äöüÄÖÜß]/g.test(title);
  const hasExcessivePunctuation = /[!?]{2,}|[.,;:]{3,}/.test(title);
  const isFinancialContent = detectFinancialContent(title);
  const isGermanContent = detectGermanContent(title);

  // Length validation (optimal: 30-60 characters)
  if (length < 20) {
    issues.push({
      type: 'length',
      severity: 'high',
      description: 'Title is too short for optimal SEO',
      suggestion:
        'Expand title to 30-60 characters for better search visibility',
      impact: 20,
    });
    score -= 20;
  } else if (length > 70) {
    issues.push({
      type: 'length',
      severity: 'medium',
      description: 'Title may be truncated in search results',
      suggestion: 'Shorten title to under 60 characters',
      impact: 15,
    });
    score -= 15;
  } else if (length >= 30 && length <= 60) {
    // Optimal length - bonus points
    score += 5;
  }

  // Character validation
  if (hasEmojis) {
    issues.push({
      type: 'characters',
      severity: 'high',
      description: 'Title contains emojis that may not display properly',
      suggestion: 'Remove emojis and use descriptive text instead',
      impact: 25,
    });
    score -= 25;
  }

  if (hasForeignChars) {
    issues.push({
      type: 'characters',
      severity: 'medium',
      description: 'Title contains special characters that may cause issues',
      suggestion: 'Replace special characters with standard alternatives',
      impact: 10,
    });
    score -= 10;
  }

  // Punctuation validation
  if (hasExcessivePunctuation) {
    issues.push({
      type: 'punctuation',
      severity: 'medium',
      description: 'Excessive punctuation may appear unprofessional',
      suggestion: 'Use single punctuation marks for better readability',
      impact: 10,
    });
    score -= 10;
  }

  // Word count validation
  if (wordCount < 3) {
    issues.push({
      type: 'content',
      severity: 'high',
      description: 'Title is too brief to be descriptive',
      suggestion: 'Add more descriptive words to improve clarity',
      impact: 15,
    });
    score -= 15;
  } else if (wordCount > 12) {
    issues.push({
      type: 'content',
      severity: 'low',
      description: 'Title may be too wordy',
      suggestion: 'Consider condensing to key terms',
      impact: 5,
    });
    score -= 5;
  }

  // SEO optimization checks
  const seoOptimized =
    length >= 30 && length <= 60 && !hasEmojis && !hasExcessivePunctuation;
  if (!seoOptimized) {
    issues.push({
      type: 'seo',
      severity: 'medium',
      description: 'Title is not optimized for search engines',
      suggestion:
        'Follow SEO best practices: 30-60 chars, no emojis, clear language',
      impact: 10,
    });
    score -= 10;
  }

  // Content-specific bonuses
  if (isFinancialContent) {
    score += 5; // Bonus for relevant financial content
  }

  if (isGermanContent && isFinancialContent) {
    score += 5; // Bonus for German financial content
  }

  // Ensure score stays within bounds
  score = Math.max(0, Math.min(100, score));

  const recommendations = generateRecommendations(issues, {
    length,
    wordCount,
    isFinancialContent,
    isGermanContent,
  });

  return {
    score,
    isOptimal:
      score >= 80 && issues.filter(i => i.severity === 'high').length === 0,
    issues,
    recommendations,
    metadata: {
      length,
      wordCount,
      hasEmojis,
      hasForeignChars,
      hasExcessivePunctuation,
      isFinancialContent,
      isGermanContent,
      seoOptimized,
    },
  };
}

/**
 * Detect financial content in title
 */
function detectFinancialContent(title: string): boolean {
  const financialTerms = [
    // German financial terms
    'DAX',
    'MDAX',
    'TecDAX',
    'SDAX',
    'Aktie',
    'Börse',
    'Kurs',
    'Dividende',
    'Gewinn',
    'Verlust',
    'Umsatz',
    'Bilanz',
    'Quartal',
    'Analyst',
    'Prognose',
    // English financial terms
    'stock',
    'share',
    'market',
    'trading',
    'investment',
    'dividend',
    'earnings',
    'revenue',
    'profit',
    'NYSE',
    'NASDAQ',
    'S&P',
    'Dow Jones',
    // Currency and numbers
    'EUR',
    'USD',
    'GBP',
    '€',
    '$',
    '£',
    '%',
    'Mio',
    'Mrd',
    'billion',
    'million',
    // Company indicators
    'AG',
    'GmbH',
    'Inc',
    'Corp',
    'Ltd',
    'SE',
  ];

  const lowerTitle = title.toLowerCase();
  return financialTerms.some(term => lowerTitle.includes(term.toLowerCase()));
}

/**
 * Detect German content in title
 */
function detectGermanContent(title: string): boolean {
  const germanIndicators = [
    'der',
    'die',
    'das',
    'und',
    'oder',
    'aber',
    'mit',
    'von',
    'zu',
    'auf',
    'für',
    'bei',
    'nach',
    'über',
    'unter',
    'ä',
    'ö',
    'ü',
    'ß',
    'auch',
    'noch',
    'schon',
    'nur',
    'mehr',
    'sehr',
    'gut',
    'neue',
    'deutschen',
    'steigt',
    'fällt',
    'erreicht',
    'meldet',
    'berichtet',
    'zeigt',
  ];

  const lowerTitle = title.toLowerCase();
  const matches = germanIndicators.filter(indicator =>
    lowerTitle.includes(indicator.toLowerCase())
  ).length;

  return matches >= 2; // At least 2 German indicators
}

/**
 * Create empty quality result for invalid input
 */
function createEmptyQualityResult(): TitleQualityResult {
  return {
    score: 0,
    isOptimal: false,
    issues: [
      {
        type: 'content',
        severity: 'high',
        description: 'Title is empty or invalid',
        suggestion: 'Provide a valid title string',
        impact: 100,
      },
    ],
    recommendations: ['Provide a valid title string'],
    metadata: {
      length: 0,
      wordCount: 0,
      hasEmojis: false,
      hasForeignChars: false,
      hasExcessivePunctuation: false,
      isFinancialContent: false,
      isGermanContent: false,
      seoOptimized: false,
    },
  };
}

/**
 * Generate actionable recommendations based on issues
 */
function generateRecommendations(
  issues: TitleIssue[],
  metadata: {
    length: number;
    wordCount: number;
    isFinancialContent: boolean;
    isGermanContent: boolean;
  }
): string[] {
  const recommendations: string[] = [];

  // Priority recommendations based on high-severity issues
  const highSeverityIssues = issues.filter(issue => issue.severity === 'high');
  if (highSeverityIssues.length > 0) {
    recommendations.push(
      '🚨 High Priority: ' + highSeverityIssues[0].suggestion
    );
  }

  // Length-specific recommendations
  if (metadata.length < 30) {
    recommendations.push(
      '📏 Expand title with relevant keywords to improve SEO'
    );
  } else if (metadata.length > 60) {
    recommendations.push(
      '✂️ Shorten title to prevent truncation in search results'
    );
  }

  // Content-specific recommendations
  if (metadata.isFinancialContent) {
    recommendations.push(
      '💰 Include specific financial metrics or company names for clarity'
    );
  }

  if (metadata.isGermanContent) {
    recommendations.push(
      '🇩🇪 Ensure German financial terms are preserved and properly formatted'
    );
  }

  // General SEO recommendations
  if (issues.some(issue => issue.type === 'seo')) {
    recommendations.push(
      '🔍 Follow SEO best practices: clear, descriptive, 30-60 characters'
    );
  }

  return recommendations.length > 0
    ? recommendations
    : ['✅ Title appears to be well-optimized'];
}

/**
 * Generate SEO-optimized title from content
 * Extracts key terms and creates optimized title focusing on financial terminology
 */
export function generateSEOTitle(
  content: string,
  options: TitleGenerationOptions = {}
): string {
  if (!content || typeof content !== 'string') {
    return '';
  }

  const {
    maxLength = 60,

    preserveGermanTerms = true,
    seoOptimized = true,
    contentType = 'article',
  } = options;

  // Extract key terms from content
  const keyTerms = extractKeyTerms(
    content,

    preserveGermanTerms
  );

  // Generate title based on content type
  let generatedTitle = '';

  switch (contentType) {
    case 'analysis':
      generatedTitle = generateAnalysisTitle(keyTerms, content);
      break;
    case 'news':
      generatedTitle = generateNewsTitle(keyTerms, content);
      break;
    case 'report':
      generatedTitle = generateReportTitle(keyTerms, content);
      break;
    default:
      generatedTitle = generateArticleTitle(keyTerms, content);
  }

  // Apply SEO optimization if requested
  if (seoOptimized) {
    generatedTitle = optimizeTitleForSEO(generatedTitle, maxLength);
  }

  // Final cleaning
  generatedTitle = cleanSourceTitle(generatedTitle);

  // Ensure length constraints
  if (generatedTitle.length > maxLength) {
    generatedTitle = truncateTitle(generatedTitle, maxLength);
  }

  return generatedTitle;
}

/**
 * Extract key terms from content for title generation
 */
function extractKeyTerms(
  content: string,
  preserveGerman: boolean
): {
  companies: string[];
  keywords: string[];
  german: string[];
} {
  const terms = {
    companies: [] as string[],
    keywords: [] as string[],
    german: [] as string[],
  };

  // Extract company names (words ending with AG, GmbH, Inc, Corp, Ltd, SE)
  const companyPattern = /\b([A-Z][a-zA-Z\s&-]+(?:AG|GmbH|Inc|Corp|Ltd|SE))\b/g;
  const companyMatches = content.match(companyPattern) || [];
  terms.companies = [...new Set(companyMatches)];

  if (preserveGerman) {
    // Extract German financial terms
    const germanPattern =
      /\b(steigt|fällt|erreicht|meldet|berichtet|zeigt|Prozent|Milliarden|Millionen|Euro|Dollar|Quartal|Jahresabschluss|Hauptversammlung|Wachstum|Marktkapitalisierung)\b/gi;
    const germanMatches = content.match(germanPattern) || [];
    terms.german = [...new Set(germanMatches)];
  }

  // Extract general keywords (capitalized words that might be important)
  const keywordPattern = /\b[A-Z][a-zA-Z]{3,}\b/g;
  const keywordMatches = content.match(keywordPattern) || [];
  terms.keywords = [...new Set(keywordMatches)]
    .filter(word => word.length > 3 && word.length < 20)
    .slice(0, 10); // Limit to top 10

  return terms;
}

/**
 * Generate analysis-type title
 */
function generateAnalysisTitle(terms: any, content: string): string {
  const { companies, german } = terms;

  let title = '';

  if (companies.length > 0) {
    title = `${companies[0]} Analyse`;
  } else {
    title = 'Marktanalyse';
  }

  // Add German context if available
  if (german.length > 0) {
    const action = german.find((term: string) =>
      ['steigt', 'fällt', 'erreicht'].includes(term.toLowerCase())
    );
    if (action) {
      title += `: ${action}`;
    }
  }

  return title;
}

/**
 * Generate news-type title
 */
function generateNewsTitle(terms: any, content: string): string {
  const { companies, german } = terms;

  let title = '';

  if (companies.length > 0 && german.length > 0) {
    const action = german.find((term: string) =>
      ['meldet', 'berichtet', 'zeigt'].includes(term.toLowerCase())
    );
    title = `${companies[0]} ${action || 'meldet'} Ergebnisse`;
  } else {
    title = 'Börsen News';
  }

  return title;
}

/**
 * Generate report-type title
 */
function generateReportTitle(terms: any, content: string): string {
  const { companies } = terms;

  let title = '';

  if (companies.length > 0) {
    title = `${companies[0]} Quartalsbericht`;
  } else {
    title = 'Finanzbericht';
  }

  return title;
}

/**
 * Generate article-type title
 */
function generateArticleTitle(terms: any, content: string): string {
  const { companies, keywords, german } = terms;

  let title = '';

  // Priority: Company + Action
  if (companies.length > 0 && german.length > 0) {
    const action = german[0];
    title = `${companies[0]} ${action}`;
  }
  // Fallback: Company + keyword
  else if (companies.length > 0 && keywords.length > 0) {
    title = `${companies[0]} ${keywords[0]}`;
  }
  // Fallback: Just company
  else if (companies.length > 0) {
    title = `${companies[0]} Update`;
  }
  // Last resort: Generic
  else {
    title = 'Finanzmarkt Update';
  }

  return title;
}

/**
 * Optimize title for SEO best practices
 */
function optimizeTitleForSEO(title: string, maxLength: number): string {
  let optimized = title;

  // Ensure proper capitalization
  optimized = optimized
    .split(' ')
    .map((word, index) => {
      // Capitalize first word and important words
      if (index === 0 || word.length > 3) {
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      }
      return word.toLowerCase();
    })
    .join(' ');

  // Remove redundant words
  const redundantWords = [
    'der',
    'die',
    'das',
    'ein',
    'eine',
    'und',
    'oder',
    'aber',
  ];
  optimized = optimized
    .split(' ')
    .filter(word => !redundantWords.includes(word.toLowerCase()))
    .join(' ');

  // Ensure it fits within length constraints
  if (optimized.length > maxLength) {
    optimized = truncateTitle(optimized, maxLength);
  }

  return optimized;
}

/**
 * Truncate title while preserving word boundaries
 */
function truncateTitle(title: string, maxLength: number): string {
  if (title.length <= maxLength) {
    return title;
  }

  // Find the last space before the max length
  const truncated = title.substring(0, maxLength);
  const lastSpace = truncated.lastIndexOf(' ');

  if (lastSpace > maxLength * 0.7) {
    // Only truncate at word boundary if it's not too short
    return truncated.substring(0, lastSpace);
  }

  // If no good word boundary, just truncate and add ellipsis
  return truncated.substring(0, maxLength - 3) + '...';
}

/**
 * Optimize title specifically for German financial content
 * Preserves important German financial terminology and structure
 */
export function optimizeGermanFinancialTitle(title: string): string {
  if (!title || typeof title !== 'string') {
    return '';
  }

  let optimized = cleanSourceTitle(title);

  // Preserve important German financial terms
  const importantTerms = {
    DAX: 'DAX',
    MDAX: 'MDAX',
    TecDAX: 'TecDAX',
    SDAX: 'SDAX',
    Börse: 'Börse',
    Aktie: 'Aktie',
    Aktien: 'Aktien',
    Kurs: 'Kurs',
    Kurse: 'Kurse',
    Dividende: 'Dividende',
    Gewinn: 'Gewinn',
    Verlust: 'Verlust',
    Umsatz: 'Umsatz',
    Bilanz: 'Bilanz',
    Quartal: 'Quartal',
    Prozent: 'Prozent',
    Euro: 'Euro',
    Milliarden: 'Milliarden',
    Millionen: 'Millionen',
  };

  // Ensure proper capitalization of financial terms
  Object.entries(importantTerms).forEach(([term, proper]) => {
    const regex = new RegExp(`\\b${term}\\b`, 'gi');
    optimized = optimized.replace(regex, proper);
  });

  // Handle specific common variations
  optimized = optimized.replace(/\bdax\b/gi, 'DAX');
  optimized = optimized.replace(/\bmdax\b/gi, 'MDAX');
  optimized = optimized.replace(/\btecdax\b/gi, 'TecDAX');
  optimized = optimized.replace(/\bsdax\b/gi, 'SDAX');

  // Handle German number formatting
  optimized = optimized.replace(/(\d+),(\d+)%/g, '$1,$2%'); // Preserve German decimal notation
  optimized = optimized.replace(/(\d+)\s*Prozent/g, '$1%'); // Convert "Prozent" to %

  // Optimize German sentence structure for titles
  optimized = optimized.replace(/^(Der|Die|Das)\s+/i, ''); // Remove articles at beginning
  optimized = optimized.replace(/\s+(steigt|fällt|erreicht)\s+um\s+/gi, ' $1 '); // Simplify action phrases

  // Ensure proper German capitalization
  optimized = optimized
    .split(' ')
    .map((word, index) => {
      // Financial indices should remain in all caps
      const financialIndices = ['DAX', 'MDAX', 'TecDAX', 'SDAX'];
      if (financialIndices.includes(word.toUpperCase())) {
        return word.toUpperCase();
      }

      // German nouns should be capitalized
      if (index === 0 || isGermanNoun(word)) {
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      }
      return word.toLowerCase();
    })
    .join(' ');

  return optimized;
}

/**
 * Check if a word is likely a German noun (simplified heuristic)
 */
function isGermanNoun(word: string): boolean {
  // Financial indices that should remain in all caps
  const financialIndices = ['DAX', 'MDAX', 'TecDAX', 'SDAX'];
  if (financialIndices.includes(word.toUpperCase())) {
    return false; // Don't apply normal capitalization to these
  }

  const germanNouns = [
    'Aktie',
    'Aktien',
    'Börse',
    'Kurs',
    'Kurse',
    'Dividende',
    'Gewinn',
    'Verlust',
    'Umsatz',
    'Bilanz',
    'Quartal',
    'Analyst',
    'Analysten',
    'Prognose',
    'Wachstum',
    'Markt',
    'Märkte',
    'Unternehmen',
    'Konzern',
    'Bank',
    'Banken',
    'Handel',
    'Investment',
    'Investition',
    'Portfolio',
    'Fonds',
    'Index',
    'Indizes',
  ];

  return (
    germanNouns.includes(word) ||
    (word.length > 4 && word.charAt(0) === word.charAt(0).toUpperCase())
  );
}
