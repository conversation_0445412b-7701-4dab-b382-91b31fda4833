/**
 * Comprehensive tests for relative time utility functions
 * Tests both English and German locales with edge cases
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import {
  getRelativeTime,
  isRecentDate,
  getRelativeTimeMemoized,
} from '../relative-time';

describe('Relative Time Utilities', () => {
  const MOCK_NOW = new Date('2025-01-15T12:00:00Z');

  beforeEach(() => {
    // Mock Date.now() to return consistent time
    vi.useFakeTimers();
    vi.setSystemTime(MOCK_NOW);
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('getRelativeTime', () => {
    describe('English locale', () => {
      it('returns "just now" for very recent times', () => {
        const recentTime = new Date('2025-01-15T11:59:30Z'); // 30 seconds ago
        expect(getRelativeTime(recentTime, 'en')).toBe('just now');
      });

      it('returns minutes ago for recent minutes', () => {
        const fiveMinutesAgo = new Date('2025-01-15T11:55:00Z');
        expect(getRelativeTime(fiveMinutesAgo, 'en')).toBe('5 min ago');

        const oneMinuteAgo = new Date('2025-01-15T11:59:00Z');
        expect(getRelativeTime(oneMinuteAgo, 'en')).toBe('1 min ago');
      });

      it('returns hours ago with proper pluralization', () => {
        const oneHourAgo = new Date('2025-01-15T11:00:00Z');
        expect(getRelativeTime(oneHourAgo, 'en')).toBe('1 hour ago');

        const twoHoursAgo = new Date('2025-01-15T10:00:00Z');
        expect(getRelativeTime(twoHoursAgo, 'en')).toBe('2 hours ago');
      });

      it('returns days ago with proper pluralization', () => {
        const oneDayAgo = new Date('2025-01-14T12:00:00Z');
        expect(getRelativeTime(oneDayAgo, 'en')).toBe('1 day ago');

        const twoDaysAgo = new Date('2025-01-13T12:00:00Z');
        expect(getRelativeTime(twoDaysAgo, 'en')).toBe('2 days ago');
      });

      it('returns weeks ago for longer periods', () => {
        const oneWeekAgo = new Date('2025-01-08T12:00:00Z');
        expect(getRelativeTime(oneWeekAgo, 'en')).toBe('1 week ago');

        const twoWeeksAgo = new Date('2025-01-01T12:00:00Z');
        expect(getRelativeTime(twoWeeksAgo, 'en')).toBe('2 weeks ago');
      });

      it('returns months ago for very long periods', () => {
        const oneMonthAgo = new Date('2024-12-15T12:00:00Z');
        expect(getRelativeTime(oneMonthAgo, 'en')).toBe('1 month ago');

        const twoMonthsAgo = new Date('2024-11-15T12:00:00Z');
        expect(getRelativeTime(twoMonthsAgo, 'en')).toBe('2 months ago');
      });

      it('returns years ago for very old dates', () => {
        const oneYearAgo = new Date('2024-01-15T12:00:00Z');
        expect(getRelativeTime(oneYearAgo, 'en')).toBe('1 year ago');

        const twoYearsAgo = new Date('2023-01-15T12:00:00Z');
        expect(getRelativeTime(twoYearsAgo, 'en')).toBe('2 years ago');
      });
    });

    describe('German locale', () => {
      it('returns "gerade eben" for very recent times', () => {
        const recentTime = new Date('2025-01-15T11:59:30Z');
        expect(getRelativeTime(recentTime, 'de')).toBe('gerade eben');
      });

      it('returns German time formats', () => {
        const fiveMinutesAgo = new Date('2025-01-15T11:55:00Z');
        expect(getRelativeTime(fiveMinutesAgo, 'de')).toBe('vor 5 Min.');

        const oneHourAgo = new Date('2025-01-15T11:00:00Z');
        expect(getRelativeTime(oneHourAgo, 'de')).toBe('vor 1 Std.');

        const twoHoursAgo = new Date('2025-01-15T10:00:00Z');
        expect(getRelativeTime(twoHoursAgo, 'de')).toBe('vor 2 Std.');

        const oneDayAgo = new Date('2025-01-14T12:00:00Z');
        expect(getRelativeTime(oneDayAgo, 'de')).toBe('vor 1 Tag');

        const twoDaysAgo = new Date('2025-01-13T12:00:00Z');
        expect(getRelativeTime(twoDaysAgo, 'de')).toBe('vor 2 Tagen');
      });
    });

    describe('Edge cases', () => {
      it('handles invalid dates gracefully', () => {
        expect(getRelativeTime('invalid-date', 'en')).toBe('unknown');
        expect(getRelativeTime(new Date('invalid'), 'de')).toBe('unbekannt');
      });

      it('handles future dates', () => {
        const futureDate = new Date('2025-01-16T12:00:00Z');
        expect(getRelativeTime(futureDate, 'en')).toBe('just now');
      });

      it('handles string dates', () => {
        const stringDate = '2025-01-15T11:55:00Z';
        expect(getRelativeTime(stringDate, 'en')).toBe('5 min ago');
      });

      it('handles timezone boundaries', () => {
        // Test crossing day boundary - 12.5 hours ago
        const yesterdayLateEvening = new Date('2025-01-14T23:30:00Z');
        expect(getRelativeTime(yesterdayLateEvening, 'en')).toBe(
          '12 hours ago'
        );
      });
    });
  });

  describe('isRecentDate', () => {
    it('identifies recent dates correctly', () => {
      const fiveMinutesAgo = new Date('2025-01-15T11:55:00Z');
      expect(isRecentDate(fiveMinutesAgo)).toBe(true);

      const oneHourAgo = new Date('2025-01-15T11:00:00Z');
      expect(isRecentDate(oneHourAgo)).toBe(true);

      const oneDayAgo = new Date('2025-01-14T12:00:00Z'); // Exactly 24 hours ago
      expect(isRecentDate(oneDayAgo)).toBe(true); // Within 24 hours is considered recent

      const twoDaysAgo = new Date('2025-01-13T12:00:00Z'); // 48 hours ago
      expect(isRecentDate(twoDaysAgo)).toBe(false);
    });

    it('handles invalid dates', () => {
      expect(isRecentDate('invalid-date')).toBe(false);
      expect(isRecentDate(new Date('invalid'))).toBe(false);
    });
  });

  describe('getRelativeTimeMemoized', () => {
    it('memoizes results correctly', () => {
      const testDate = new Date('2025-01-15T11:55:00Z');

      // First call
      const result1 = getRelativeTimeMemoized(testDate, 'en');
      expect(result1).toBe('5 min ago');

      // Second call should return cached result
      const result2 = getRelativeTimeMemoized(testDate, 'en');
      expect(result2).toBe('5 min ago');
      expect(result1).toBe(result2);
    });

    it('handles different locales separately', () => {
      const testDate = new Date('2025-01-15T11:55:00Z');

      const englishResult = getRelativeTimeMemoized(testDate, 'en');
      const germanResult = getRelativeTimeMemoized(testDate, 'de');

      expect(englishResult).toBe('5 min ago');
      expect(germanResult).toBe('vor 5 Min.');
    });
  });

  describe('Performance', () => {
    it('handles large numbers of calls efficiently', () => {
      const testDate = new Date('2025-01-15T11:55:00Z');

      const start = performance.now();

      // Run many calls
      for (let i = 0; i < 1000; i++) {
        getRelativeTimeMemoized(testDate, 'en');
      }

      const end = performance.now();

      // Should complete quickly due to memoization
      expect(end - start).toBeLessThan(50); // Less than 50ms
    });
  });
});
