/**
 * Tests for date formatting utility functions
 */

import { describe, it, expect } from 'vitest';
import {
  formatExactDate,
  formatShortDate,
  getPublishedDateAriaLabel,
  formatISODate,
  isToday,
  isYesterday,
  getDateDescription,
} from '../date-formatting';

describe('Date Formatting Utilities', () => {
  const mockDate = new Date('2025-01-15T14:30:00Z');

  describe('formatExactDate', () => {
    it('formats date correctly in English', () => {
      const result = formatExactDate(mockDate, 'en');
      expect(result).toContain('Wednesday');
      expect(result).toContain('January');
      expect(result).toContain('15');
      expect(result).toContain('2025');
      expect(result).toContain('14:30');
    });

    it('formats date correctly in German', () => {
      const result = formatExactDate(mockDate, 'de');
      expect(result).toContain('Mittwoch');
      expect(result).toContain('Januar');
      expect(result).toContain('15');
      expect(result).toContain('2025');
      expect(result).toContain('14:30');
    });

    it('handles string dates', () => {
      const result = formatExactDate('2025-01-15T14:30:00Z', 'en');
      expect(result).toContain('Wednesday');
      expect(result).toContain('January');
    });

    it('handles invalid dates gracefully', () => {
      const result = formatExactDate('invalid-date', 'en');
      expect(result).toBe('Invalid date');

      const resultDe = formatExactDate('invalid-date', 'de');
      expect(resultDe).toBe('Ungültiges Datum');
    });
  });

  describe('formatShortDate', () => {
    it('formats short date correctly in English', () => {
      const result = formatShortDate(mockDate, 'en');
      expect(result).toContain('Jan');
      expect(result).toContain('15');
      expect(result).toContain('2025');
    });

    it('formats short date correctly in German', () => {
      const result = formatShortDate(mockDate, 'de');
      expect(result).toContain('Jan');
      expect(result).toContain('15');
      expect(result).toContain('2025');
    });

    it('handles invalid dates gracefully', () => {
      const result = formatShortDate('invalid-date', 'en');
      expect(result).toBe('Invalid date');
    });
  });

  describe('getPublishedDateAriaLabel', () => {
    it('creates accessible aria label in English', () => {
      const result = getPublishedDateAriaLabel(mockDate, 'en');
      expect(result).toContain('Published on');
      expect(result).toContain('Wednesday');
      expect(result).toContain('January');
    });

    it('creates accessible aria label in German', () => {
      const result = getPublishedDateAriaLabel(mockDate, 'de');
      expect(result).toContain('Veröffentlicht am');
      expect(result).toContain('Mittwoch');
      expect(result).toContain('Januar');
    });

    it('handles invalid dates gracefully', () => {
      const result = getPublishedDateAriaLabel('invalid-date', 'en');
      expect(result).toBe('Invalid published date');

      const resultDe = getPublishedDateAriaLabel('invalid-date', 'de');
      expect(resultDe).toBe('Ungültiges Veröffentlichungsdatum');
    });
  });

  describe('formatISODate', () => {
    it('formats dates to ISO format', () => {
      const result = formatISODate(mockDate);
      expect(result).toBe('2025-01-15'); // formatISODate returns date-only format
    });

    it('handles string dates', () => {
      const result = formatISODate('2025-01-15T14:30:00Z');
      expect(result).toBe('2025-01-15'); // formatISODate returns date-only format
    });

    it('handles invalid dates gracefully', () => {
      const result = formatISODate('invalid-date');
      expect(result).toBe('Invalid date');
    });
  });

  describe('isToday', () => {
    it('correctly identifies today', () => {
      const today = new Date();
      expect(isToday(today)).toBe(true);
    });

    it('correctly identifies non-today dates', () => {
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
      expect(isToday(yesterday)).toBe(false);
    });

    it('handles invalid dates', () => {
      expect(isToday('invalid-date')).toBe(false);
    });
  });

  describe('isYesterday', () => {
    it('correctly identifies yesterday', () => {
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
      expect(isYesterday(yesterday)).toBe(true);
    });

    it('correctly identifies non-yesterday dates', () => {
      const today = new Date();
      expect(isYesterday(today)).toBe(false);
    });

    it('handles invalid dates', () => {
      expect(isYesterday('invalid-date')).toBe(false);
    });
  });

  describe('Edge cases', () => {
    it('handles timezone differences correctly', () => {
      const utcDate = new Date('2025-01-15T00:00:00Z');
      const result = formatExactDate(utcDate, 'en');
      expect(result).toContain('2025');
      expect(result).toContain('00:00');
    });

    it('handles leap year dates', () => {
      const leapYearDate = new Date('2024-02-29T12:00:00Z');
      const result = formatExactDate(leapYearDate, 'en');
      expect(result).toContain('February');
      expect(result).toContain('29');
      expect(result).toContain('2024');
    });

    it('handles year boundaries', () => {
      const newYearDate = new Date('2025-01-01T00:00:00Z');
      const result = formatExactDate(newYearDate, 'en');
      expect(result).toContain('January');
      expect(result).toContain('1');
      expect(result).toContain('2025');
    });
  });
});
