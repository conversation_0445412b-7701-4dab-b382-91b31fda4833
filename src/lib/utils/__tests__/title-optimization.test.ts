/**
 * Title Optimization Tests
 *
 * Comprehensive test suite for title cleaning and optimization utilities
 * with real German financial titles and edge cases.
 *
 * <AUTHOR> Development Team
 * @created 2025-06-29
 * @sprint Sprint 4: Title Optimization & Processing
 */

import {
  cleanEnglishTitle,
  cleanSourceTitle,
  generateSEOTitle,
  optimizeGermanFinancialTitle,
  TitleGenerationOptions,
  TitleQualityResult,
  validateTitleQuality,
} from '../title-optimization';

// Real German financial titles for testing
const GERMAN_FINANCIAL_TITLES = {
  withEmojis: [
    '🚀 DAX steigt um 2,5% - Aktien im Höhenflug! ⭐',
    '***BREAKING*** Volkswagen AG meldet Rekordgewinn 💰',
    'Börse Frankfurt: Neue Höchststände erreicht 📈📊',
    '🔥 Deutsche Bank überrascht Analysten positiv! 🎯',
  ],
  withAsterisks: [
    '***EILMELDUNG*** SAP AG übertrifft Erwartungen',
    '**BMW Aktie** erreicht neues Allzeithoch',
    '*Siemens* meldet starke Quartalszahlen',
    '***UPDATE*** BASF senkt Prognose für 2024',
  ],
  withForeignChars: [
    'Handelsblatt: „Deutsche Bank überrascht Analysten"',
    "Manager Magazin: 'Volkswagen vor Durchbruch'",
    'Börse Online: -DAX erreicht 16.000 Punkte-',
    'Finanzen.net: ...Aktienmarkt im Aufwind...',
  ],
  withExcessivePunctuation: [
    'DAX FÄLLT!!! Was ist los???',
    'Volkswagen AG: Gewinn steigt um 25%!!!',
    'Deutsche Bank meldet Verluste......',
    'BMW Aktie: Neue Höchststände erreicht!!!!!',
  ],
  optimal: [
    'DAX steigt um 2,5% auf neues Jahreshoch',
    'Volkswagen AG meldet Rekordgewinn im Q3',
    'Deutsche Bank übertrifft Analystenschätzungen',
    'BMW Aktie erreicht neues Allzeithoch',
  ],
};

const ENGLISH_TITLES = {
  withArtifacts: [
    'German Stock Market (DAX) Rises 2.5% Today',
    'Volkswagen AG [VW] Reports Record Profits',
    'Deutsche Bank {DB} Beats Analyst Expectations',
    'BMW Stock Reaches New All-Time High (ATH)',
  ],
  withForeignChars: [
    'German Börse Reaches New Heights',
    'Volkswagen AG Übertrifft Expectations',
    'Deutsche Bank Überrascht Analysts',
    'BMW Aktie Shows Strong Performance',
  ],
  optimal: [
    'German Stock Market Rises 2.5% Today',
    'Volkswagen AG Reports Record Profits',
    'Deutsche Bank Beats Analyst Expectations',
    'BMW Stock Reaches New All-Time High',
  ],
};

const CONTENT_SAMPLES = {
  financial: `
		Der DAX ist heute um 2,5% auf 15.850 Punkte gestiegen. 
		Volkswagen AG meldete einen Rekordgewinn von 3,2 Milliarden Euro im dritten Quartal.
		Analysten zeigten sich überrascht von den starken Zahlen des Automobilkonzerns.
		Die Aktie stieg um 8% auf 180 Euro.
	`,
  nonFinancial: `
		Das Wetter in Deutschland wird in den nächsten Tagen sonnig und warm.
		Die Temperaturen steigen auf bis zu 25 Grad Celsius.
		Experten empfehlen, viel Wasser zu trinken und Sonnenschutz zu verwenden.
	`,
  mixed: `
		Die Deutsche Bank AG hat heute ihre Quartalszahlen veröffentlicht.
		Das Unternehmen meldete einen Gewinn von 1,2 Milliarden Euro.
		Gleichzeitig wurde bekannt, dass das Wetter in Frankfurt sonnig ist.
	`,
};

describe('Title Optimization', () => {
  describe('cleanSourceTitle', () => {
    test('should remove emojis from titles', () => {
      GERMAN_FINANCIAL_TITLES.withEmojis.forEach(title => {
        const cleaned = cleanSourceTitle(title);
        expect(cleaned).not.toMatch(/[\u{1F600}-\u{1F9FF}]/u);
        expect(cleaned.length).toBeGreaterThan(0);
      });
    });

    test('should remove asterisks and formatting', () => {
      GERMAN_FINANCIAL_TITLES.withAsterisks.forEach(title => {
        const cleaned = cleanSourceTitle(title);
        expect(cleaned).not.toContain('***');
        expect(cleaned).not.toContain('**');
        expect(cleaned).not.toMatch(/^\*+|\*+$/);
      });
    });

    test('should normalize excessive punctuation', () => {
      GERMAN_FINANCIAL_TITLES.withExcessivePunctuation.forEach(title => {
        const cleaned = cleanSourceTitle(title);
        expect(cleaned).not.toMatch(/!{2,}/);
        expect(cleaned).not.toMatch(/\?{2,}/);
        expect(cleaned).not.toMatch(/\.{4,}/);
      });
    });

    test('should handle special characters', () => {
      GERMAN_FINANCIAL_TITLES.withForeignChars.forEach(title => {
        const cleaned = cleanSourceTitle(title);
        expect(cleaned).not.toContain('„');
        expect(cleaned).not.toContain('"');
        expect(cleaned).not.toContain("'");
        expect(cleaned).not.toContain("'");
        expect(cleaned).not.toContain('-');
        expect(cleaned).not.toContain('...');
      });
    });

    test('should preserve important content', () => {
      const title = '🚀 DAX steigt um 2,5% - Aktien im Höhenflug! ⭐';
      const cleaned = cleanSourceTitle(title);
      expect(cleaned).toContain('DAX');
      expect(cleaned).toContain('2,5%');
      expect(cleaned).toContain('Aktien');
      expect(cleaned).toContain('Höhenflug');
    });

    test('should handle empty and invalid input', () => {
      expect(cleanSourceTitle('')).toBe('');
      expect(cleanSourceTitle(null as any)).toBe('');
      expect(cleanSourceTitle(undefined as any)).toBe('');
    });
  });

  describe('cleanEnglishTitle', () => {
    test('should remove translation artifacts', () => {
      ENGLISH_TITLES.withArtifacts.forEach(title => {
        const cleaned = cleanEnglishTitle(title);
        expect(cleaned).not.toMatch(/\([^)]*\)/);
        expect(cleaned).not.toMatch(/\[[^\]]*\]/);
        expect(cleaned).not.toMatch(/\{[^}]*\}/);
      });
    });

    test('should handle foreign characters in English titles', () => {
      const allCleaned = ENGLISH_TITLES.withForeignChars.map(title =>
        cleanEnglishTitle(title)
      );
      const combinedText = allCleaned.join(' ');

      // Should preserve German company names but clean foreign characters
      expect(combinedText).toContain('Volkswagen AG');
      expect(combinedText).toContain('Deutsche Bank');

      // Should clean foreign characters like umlauts
      allCleaned.forEach(cleaned => {
        expect(cleaned).not.toContain('Ü');
        expect(cleaned).not.toContain('ü');
        expect(cleaned).not.toContain('ä');
        expect(cleaned).not.toContain('ö');
      });
    });

    test('should ensure proper capitalization', () => {
      const title = 'german STOCK market RISES today';
      const cleaned = cleanEnglishTitle(title);
      expect(cleaned.charAt(0)).toBe(cleaned.charAt(0).toUpperCase());
    });

    test('should preserve important financial terms', () => {
      const title = 'BMW Stock (Bayerische Motoren Werke) Reaches ATH';
      const cleaned = cleanEnglishTitle(title);
      expect(cleaned).toContain('BMW');
      expect(cleaned).toContain('Stock');
      expect(cleaned).toContain('Reaches');
    });
  });

  describe('validateTitleQuality', () => {
    test('should score optimal titles highly', () => {
      GERMAN_FINANCIAL_TITLES.optimal.forEach(title => {
        const result = validateTitleQuality(title);
        expect(result.score).toBeGreaterThan(70);
        expect(result.metadata.isFinancialContent).toBe(true);
      });
    });

    test('should penalize titles with emojis', () => {
      const titleWithEmojis = '🚀 DAX steigt um 2,5% ⭐';
      const titleWithoutEmojis = 'DAX steigt um 2,5%';

      const resultWith = validateTitleQuality(titleWithEmojis);
      const resultWithout = validateTitleQuality(titleWithoutEmojis);

      expect(resultWith.score).toBeLessThan(resultWithout.score);
      expect(resultWith.metadata.hasEmojis).toBe(true);
      expect(resultWithout.metadata.hasEmojis).toBe(false);
    });

    test('should detect length issues', () => {
      const tooShort = 'DAX';
      const tooLong =
        'This is a very long title that exceeds the optimal length for SEO and will likely be truncated in search results';
      const optimal = 'DAX steigt um 2,5% auf neues Jahreshoch';

      const shortResult = validateTitleQuality(tooShort);
      const longResult = validateTitleQuality(tooLong);
      const optimalResult = validateTitleQuality(optimal);

      expect(shortResult.score).toBeLessThan(optimalResult.score);
      expect(longResult.score).toBeLessThan(optimalResult.score);
      expect(shortResult.issues.some(issue => issue.type === 'length')).toBe(
        true
      );
      expect(longResult.issues.some(issue => issue.type === 'length')).toBe(
        true
      );
    });

    test('should detect financial and German content', () => {
      const financialGerman = 'DAX steigt um 2,5% - Deutsche Aktien im Aufwind';
      const nonFinancial = 'Wetter wird sonnig und warm';

      const financialResult = validateTitleQuality(financialGerman);
      const nonFinancialResult = validateTitleQuality(nonFinancial);

      expect(financialResult.metadata.isFinancialContent).toBe(true);
      expect(financialResult.metadata.isGermanContent).toBe(true);
      expect(nonFinancialResult.metadata.isFinancialContent).toBe(false);
    });

    test('should provide actionable recommendations', () => {
      const problematicTitle = '🚀***DAX!!!***🚀';
      const result = validateTitleQuality(problematicTitle);

      expect(result.recommendations.length).toBeGreaterThan(0);
      expect(result.issues.length).toBeGreaterThan(0);
      expect(result.score).toBeLessThan(50);
    });

    test('should handle empty input gracefully', () => {
      const result = validateTitleQuality('');
      expect(result.score).toBe(0);
      expect(result.isOptimal).toBe(false);
      expect(result.issues.length).toBeGreaterThan(0);
    });
  });

  describe('generateSEOTitle', () => {
    test('should generate titles from financial content', () => {
      const title = generateSEOTitle(CONTENT_SAMPLES.financial);
      expect(title.length).toBeGreaterThan(0);
      expect(title.length).toBeLessThanOrEqual(60);
      expect(title).toMatch(/DAX|Volkswagen|Aktie/i);
    });

    test('should respect content type options', () => {
      const analysisTitle = generateSEOTitle(CONTENT_SAMPLES.financial, {
        contentType: 'analysis',
      });
      const newsTitle = generateSEOTitle(CONTENT_SAMPLES.financial, {
        contentType: 'news',
      });
      const reportTitle = generateSEOTitle(CONTENT_SAMPLES.financial, {
        contentType: 'report',
      });

      expect(analysisTitle).toContain('Analyse');
      expect(newsTitle).toMatch(/News|meldet|berichtet/i);
      expect(reportTitle).toContain('Bericht');
    });

    test('should respect length constraints', () => {
      const shortTitle = generateSEOTitle(CONTENT_SAMPLES.financial, {
        maxLength: 30,
      });
      const longTitle = generateSEOTitle(CONTENT_SAMPLES.financial, {
        maxLength: 80,
      });

      expect(shortTitle.length).toBeLessThanOrEqual(30);
      expect(longTitle.length).toBeLessThanOrEqual(80);
    });

    test('should handle non-financial content', () => {
      const title = generateSEOTitle(CONTENT_SAMPLES.nonFinancial);
      expect(title.length).toBeGreaterThan(0);
      // Should generate a generic title for non-financial content
    });

    test('should handle empty content', () => {
      const title = generateSEOTitle('');
      expect(title).toBe('');
    });
  });

  describe('optimizeGermanFinancialTitle', () => {
    test('should preserve German financial terms', () => {
      const title = 'dax steigt um 2,5% - aktien im höhenflug';
      const optimized = optimizeGermanFinancialTitle(title);

      expect(optimized).toContain('DAX');
      expect(optimized).toContain('Aktien');
      expect(optimized).toContain('2,5%');
    });

    test('should handle German number formatting', () => {
      const title = 'DAX steigt um 2,5 Prozent';
      const optimized = optimizeGermanFinancialTitle(title);

      expect(optimized).toContain('2,5%');
    });

    test('should remove German articles at beginning', () => {
      const title = 'Der DAX erreicht neue Höchststände';
      const optimized = optimizeGermanFinancialTitle(title);

      expect(optimized).not.toMatch(/^Der\s/);
      expect(optimized).toContain('DAX');
    });

    test('should ensure proper German capitalization', () => {
      const title = 'volkswagen ag meldet gewinn';
      const optimized = optimizeGermanFinancialTitle(title);

      // German nouns should be capitalized
      expect(optimized).toMatch(/Volkswagen/);
      expect(optimized).toMatch(/Gewinn/);
    });

    test('should handle empty input', () => {
      expect(optimizeGermanFinancialTitle('')).toBe('');
      expect(optimizeGermanFinancialTitle(null as any)).toBe('');
    });
  });

  describe('Integration Tests', () => {
    test('should process real German financial titles end-to-end', () => {
      const originalTitle =
        '🚀***BREAKING*** DAX steigt um 2,5%!!! Aktien im Höhenflug! ⭐📈';

      // Step 1: Clean source title
      const cleaned = cleanSourceTitle(originalTitle);
      expect(cleaned).not.toMatch(/[\u{1F600}-\u{1F9FF}]/u); // No emojis
      expect(cleaned).not.toContain('***'); // No asterisks
      expect(cleaned).not.toMatch(/!{2,}/); // No excessive punctuation

      // Step 2: Optimize for German financial content
      const optimized = optimizeGermanFinancialTitle(cleaned);
      expect(optimized).toContain('DAX');
      expect(optimized).toContain('2,5%');

      // Step 3: Validate quality
      const quality = validateTitleQuality(optimized);
      expect(quality.score).toBeGreaterThan(60); // Should be much better than original
      expect(quality.metadata.isFinancialContent).toBe(true);
      expect(quality.metadata.isGermanContent).toBe(true);
    });

    test('should maintain performance for large batches', () => {
      const titles = [
        ...GERMAN_FINANCIAL_TITLES.withEmojis,
        ...GERMAN_FINANCIAL_TITLES.withAsterisks,
        ...GERMAN_FINANCIAL_TITLES.withForeignChars,
        ...GERMAN_FINANCIAL_TITLES.withExcessivePunctuation,
      ];

      const startTime = Date.now();

      titles.forEach(title => {
        const cleaned = cleanSourceTitle(title);
        const optimized = optimizeGermanFinancialTitle(cleaned);
        const quality = validateTitleQuality(optimized);

        expect(cleaned).toBeTruthy();
        expect(optimized).toBeTruthy();
        expect(quality.score).toBeGreaterThanOrEqual(0);
      });

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // Should process all titles in reasonable time (< 100ms total)
      expect(processingTime).toBeLessThan(100);
    });
  });
});
