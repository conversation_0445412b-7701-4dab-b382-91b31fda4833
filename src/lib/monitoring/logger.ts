/**
 * Comprehensive Logging System
 *
 * Structured logging infrastructure for the enhanced content processing pipeline
 * with component-specific loggers, error tracking, and configurable log levels
 * for production debugging and monitoring.
 *
 * Features:
 * - Structured logging with consistent formatting
 * - Component-specific loggers with context
 * - Configurable log levels (debug, info, warn, error)
 * - Error categorization and automatic reporting
 * - Performance-aware logging with minimal overhead
 * - Production-ready log rotation and management
 *
 * <AUTHOR> Blick Development Team
 * @created 2025-06-29
 * @sprint Sprint 6: Monitoring & Production Readiness
 */

/**
 * Log levels in order of severity
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

/**
 * Log entry interface
 */
export interface LogEntry {
  timestamp: Date;
  level: LogLevel;
  component: string;
  message: string;
  context?: Record<string, any>;
  error?: Error;
  url?: string;
  processingId?: string;
  duration?: number;
}

/**
 * Logger configuration
 */
export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableFile: boolean;
  enableStructured: boolean;
  maxLogSize: number;
  rotationInterval: number;
  component: string;
}

/**
 * Error categories for tracking
 */
export enum ErrorCategory {
  EXTRACTION_ERROR = 'extraction_error',
  CONVERSION_ERROR = 'conversion_error',
  TITLE_PROCESSING_ERROR = 'title_processing_error',
  VALIDATION_ERROR = 'validation_error',
  NETWORK_ERROR = 'network_error',
  CONFIGURATION_ERROR = 'configuration_error',
  UNKNOWN_ERROR = 'unknown_error',
}

/**
 * Logger class with component-specific context
 */
class Logger {
  private config: LoggerConfig;
  private logBuffer: LogEntry[] = [];
  private errorCounts: Map<ErrorCategory, number> = new Map();

  constructor(component: string, config?: Partial<LoggerConfig>) {
    this.config = {
      level: LogLevel.INFO,
      enableConsole: true,
      enableFile: false,
      enableStructured: true,
      maxLogSize: 1000,
      rotationInterval: 24 * 60 * 60 * 1000, // 24 hours
      component,
      ...config,
    };

    // Initialize error counts
    Object.values(ErrorCategory).forEach(category => {
      this.errorCounts.set(category, 0);
    });
  }

  /**
   * Debug level logging
   */
  debug(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  /**
   * Info level logging
   */
  info(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, context);
  }

  /**
   * Warning level logging
   */
  warn(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, context);
  }

  /**
   * Error level logging
   */
  error(message: string, error?: Error, context?: Record<string, any>): void {
    const category = this.categorizeError(error);
    this.errorCounts.set(category, (this.errorCounts.get(category) || 0) + 1);

    this.log(
      LogLevel.ERROR,
      message,
      { ...context, errorCategory: category },
      error
    );
  }

  /**
   * Log processing start
   */
  startProcessing(
    url: string,
    processingId: string,
    context?: Record<string, any>
  ): void {
    this.info(`🚀 Processing started`, {
      url,
      processingId,
      ...context,
    });
  }

  /**
   * Log processing completion
   */
  endProcessing(
    url: string,
    processingId: string,
    duration: number,
    success: boolean,
    context?: Record<string, any>
  ): void {
    const emoji = success ? '✅' : '❌';
    const level = success ? LogLevel.INFO : LogLevel.ERROR;

    this.log(level, `${emoji} Processing ${success ? 'completed' : 'failed'}`, {
      url,
      processingId,
      duration,
      success,
      ...context,
    });
  }

  /**
   * Log performance metrics
   */
  performance(
    message: string,
    metrics: Record<string, number>,
    context?: Record<string, any>
  ): void {
    this.info(`📊 ${message}`, {
      metrics,
      ...context,
    });
  }

  /**
   * Log cache operations
   */
  cache(
    operation: 'hit' | 'miss' | 'set' | 'clear',
    key: string,
    context?: Record<string, any>
  ): void {
    const emoji =
      operation === 'hit'
        ? '💾'
        : operation === 'miss'
          ? '🔍'
          : operation === 'set'
            ? '💿'
            : '🧹';
    this.debug(`${emoji} Cache ${operation}`, {
      key,
      operation,
      ...context,
    });
  }

  /**
   * Get error statistics
   */
  getErrorStats(): { total: number; byCategory: Record<string, number> } {
    const total = Array.from(this.errorCounts.values()).reduce(
      (sum, count) => sum + count,
      0
    );
    const byCategory: Record<string, number> = {};

    this.errorCounts.forEach((count, category) => {
      byCategory[category] = count;
    });

    return { total, byCategory };
  }

  /**
   * Get recent log entries
   */
  getRecentLogs(count: number = 50): LogEntry[] {
    return this.logBuffer.slice(-count);
  }

  /**
   * Clear log buffer and error counts
   */
  clear(): void {
    this.logBuffer = [];
    this.errorCounts.clear();
    Object.values(ErrorCategory).forEach(category => {
      this.errorCounts.set(category, 0);
    });
  }

  /**
   * Core logging method
   */
  private log(
    level: LogLevel,
    message: string,
    context?: Record<string, any>,
    error?: Error
  ): void {
    // Check if log level is enabled
    if (level < this.config.level) {
      return;
    }

    const entry: LogEntry = {
      timestamp: new Date(),
      level,
      component: this.config.component,
      message,
      context,
      error,
    };

    // Add to buffer
    this.logBuffer.push(entry);

    // Limit buffer size
    if (this.logBuffer.length > this.config.maxLogSize) {
      this.logBuffer = this.logBuffer.slice(-this.config.maxLogSize);
    }

    // Output to console if enabled
    if (this.config.enableConsole) {
      this.outputToConsole(entry);
    }

    // Output to file if enabled (in production)
    if (this.config.enableFile) {
      this.outputToFile(entry);
    }
  }

  /**
   * Output log entry to console
   */
  private outputToConsole(entry: LogEntry): void {
    const timestamp = entry.timestamp.toISOString();
    const levelName = LogLevel[entry.level];
    const component = `[${entry.component}]`;

    let output = `${timestamp} ${levelName.padEnd(5)} ${component.padEnd(20)} ${entry.message}`;

    // Add context if available
    if (entry.context && Object.keys(entry.context).length > 0) {
      if (this.config.enableStructured) {
        output += ` ${JSON.stringify(entry.context)}`;
      } else {
        const contextStr = Object.entries(entry.context)
          .map(([key, value]) => `${key}=${value}`)
          .join(' ');
        output += ` ${contextStr}`;
      }
    }

    // Choose appropriate console method based on level
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(output);
        break;
      case LogLevel.INFO:
        console.info(output);
        break;
      case LogLevel.WARN:
        console.warn(output);
        if (entry.error) console.warn(entry.error);
        break;
      case LogLevel.ERROR:
        console.error(output);
        if (entry.error) console.error(entry.error);
        break;
    }
  }

  /**
   * Output log entry to file
   */
  private outputToFile(entry: LogEntry): void {
    const logLine = {
      timestamp: entry.timestamp.toISOString(),
      level: LogLevel[entry.level],
      component: entry.component,
      message: entry.message,
      context: entry.context,
      error: entry.error
        ? {
            name: entry.error.name,
            message: entry.error.message,
            stack: entry.error.stack,
          }
        : undefined,
    };

    // Write to file asynchronously (don't block the main thread)
    this.writeToFileAsync(logLine);
  }

  /**
   * Async file writing to avoid blocking
   */
  private async writeToFileAsync(logLine: any): Promise<void> {
    try {
      const fs = await import('fs/promises');
      const path = await import('path');

      // Create logs directory if it doesn't exist
      const logsDir = path.join(process.cwd(), 'logs');
      await fs.mkdir(logsDir, { recursive: true });

      // Create component-specific log file
      const logFileName = `${this.config.component}-${new Date().toISOString().split('T')[0]}.log`;
      const logFilePath = path.join(logsDir, logFileName);

      // Append log entry
      await fs.appendFile(logFilePath, JSON.stringify(logLine) + '\n');
    } catch (error) {
      // Fallback to console if file writing fails
      console.error('Failed to write log to file:', error);
      console.log('📝 [FILE FALLBACK]', JSON.stringify(logLine));
    }
  }

  /**
   * Categorize errors for tracking
   */
  private categorizeError(error?: Error): ErrorCategory {
    if (!error) return ErrorCategory.UNKNOWN_ERROR;

    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    // Network-related errors
    if (
      message.includes('network') ||
      message.includes('timeout') ||
      message.includes('connection') ||
      message.includes('fetch') ||
      message.includes('enotfound') ||
      message.includes('econnrefused')
    ) {
      return ErrorCategory.NETWORK_ERROR;
    }

    // Extraction-related errors
    if (
      message.includes('extraction') ||
      message.includes('firecrawl') ||
      message.includes('scrape') ||
      stack.includes('enhanced-client') ||
      stack.includes('firecrawl')
    ) {
      return ErrorCategory.EXTRACTION_ERROR;
    }

    // Conversion-related errors
    if (
      message.includes('conversion') ||
      message.includes('lexical') ||
      message.includes('html') ||
      stack.includes('html-to-lexical') ||
      stack.includes('conversion')
    ) {
      return ErrorCategory.CONVERSION_ERROR;
    }

    // Title processing errors
    if (
      message.includes('title') ||
      message.includes('optimization') ||
      stack.includes('title-optimization')
    ) {
      return ErrorCategory.TITLE_PROCESSING_ERROR;
    }

    // Validation errors
    if (
      message.includes('validation') ||
      message.includes('invalid') ||
      stack.includes('validation')
    ) {
      return ErrorCategory.VALIDATION_ERROR;
    }

    // Configuration errors
    if (
      message.includes('config') ||
      message.includes('environment') ||
      message.includes('api key') ||
      message.includes('missing') ||
      message.includes('undefined')
    ) {
      return ErrorCategory.CONFIGURATION_ERROR;
    }

    return ErrorCategory.UNKNOWN_ERROR;
  }
}

/**
 * Component-specific loggers
 */
const loggers = new Map<string, Logger>();

/**
 * Get or create a logger for a specific component
 */
export function getLogger(
  component: string,
  config?: Partial<LoggerConfig>
): Logger {
  if (!loggers.has(component)) {
    loggers.set(component, new Logger(component, config));
  }
  return loggers.get(component)!;
}

/**
 * Pre-configured loggers for main components
 */
export const extractionLogger = getLogger('extraction');
export const conversionLogger = getLogger('conversion');
export const titleLogger = getLogger('title-processing');
export const validationLogger = getLogger('validation');
export const processorLogger = getLogger('enhanced-processor');
export const monitoringLogger = getLogger('monitoring');

/**
 * Set global log level for all loggers
 */
export function setGlobalLogLevel(level: LogLevel): void {
  loggers.forEach(logger => {
    (logger as any).config.level = level;
  });
  console.log(`📝 Global log level set to ${LogLevel[level]}`);
}

/**
 * Get aggregated error statistics from all loggers
 */
export function getGlobalErrorStats(): {
  total: number;
  byCategory: Record<string, number>;
  byComponent: Record<string, number>;
} {
  const byCategory: Record<string, number> = {};
  const byComponent: Record<string, number> = {};
  let total = 0;

  loggers.forEach((logger, component) => {
    const stats = logger.getErrorStats();
    total += stats.total;
    byComponent[component] = stats.total;

    Object.entries(stats.byCategory).forEach(([category, count]) => {
      byCategory[category] = (byCategory[category] || 0) + count;
    });
  });

  return { total, byCategory, byComponent };
}

/**
 * Clear all logger buffers and error counts
 */
export function clearAllLogs(): void {
  loggers.forEach(logger => logger.clear());
  console.log('🧹 All logs cleared');
}

/**
 * Get recent logs from all components
 */
export function getAllRecentLogs(
  count: number = 100
): Array<LogEntry & { component: string }> {
  const allLogs: Array<LogEntry & { component: string }> = [];

  loggers.forEach((logger, component) => {
    const recentLogs = logger.getRecentLogs(count);
    recentLogs.forEach(log => {
      allLogs.push({ ...log, component });
    });
  });

  // Sort by timestamp
  allLogs.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

  return allLogs.slice(-count);
}

export { Logger };
