/**
 * Production Pipeline Performance Monitoring & Metrics
 *
 * Comprehensive tracking system for production pipeline performance,
 * cost analysis, and processing metrics. Provides detailed insights
 * into RSS processing efficiency, API usage, and system performance.
 *
 * Features:
 * - Real-time processing time tracking
 * - API cost analysis and estimation
 * - Throughput and success rate monitoring
 * - Feed-specific performance metrics
 * - Historical trend analysis
 *
 * <AUTHOR> Blick Development Team
 * @created 2025-01-07
 * @sprint Pipeline Optimization & Monitoring
 */

/**
 * Pipeline metrics interface for comprehensive tracking
 */
export interface PipelineMetrics {
  // Timing metrics
  timing: {
    startTime: number;
    endTime?: number;
    totalDurationMs: number;
    phases: {
      rssParsingMs: number;
      firecrawlProcessingMs: number;
      openaiProcessingMs: number;
      databaseOperationsMs: number;
    };
  };

  // Processing metrics
  processing: {
    feedsProcessed: number;
    articlesScraped: number;
    articlesAccepted: number;
    articlesRejected: number;
    successRate: number;
    throughputPerMinute: number;
  };

  // API usage metrics
  apiUsage: {
    firecrawl: {
      requestsCount: number;
      successCount: number;
      rateLimitedCount: number;
      errorCount: number;
      averageResponseTimeMs: number;
      estimatedCost: number;
    };
    openai: {
      requestsCount: number;
      tokensUsed: number;
      averageResponseTimeMs: number;
      estimatedCost: number;
      enhancementSuccessRate: number;
    };
  };

  // Feed-specific metrics
  feedMetrics: FeedMetrics[];

  // System performance
  system: {
    memoryUsageMB: number;
    cpuUsagePercent: number;
    parallelProcessingEfficiency: number;
  };

  // Quality metrics
  quality: {
    averageContentQuality: number;
    duplicateFilterEfficiency: number;
    keywordMatchAccuracy: number;
  };
}

/**
 * Individual feed processing metrics
 */
export interface FeedMetrics {
  feedId: string;
  feedName: string;
  priority: 'high' | 'medium' | 'low';
  language: 'en' | 'de';

  timing: {
    startTime: number;
    endTime: number;
    durationMs: number;
  };

  processing: {
    itemsFound: number;
    itemsProcessed: number;
    itemsAccepted: number;
    itemsRejected: number;
    duplicatesFiltered: number;
    keywordRejects: number;
    firecrawlFailures: number;
    openaiFailures: number;
  };

  performance: {
    successRate: number;
    averageProcessingTimePerItem: number;
    costPerAcceptedArticle: number;
  };
}

/**
 * Global metrics collector class
 */
class PipelineMetricsCollector {
  private metrics: PipelineMetrics;
  private feedMetrics: Map<string, FeedMetrics>;
  private startTime: number;

  constructor() {
    this.startTime = Date.now();
    this.feedMetrics = new Map();
    this.metrics = this.initializeMetrics();
  }

  private initializeMetrics(): PipelineMetrics {
    return {
      timing: {
        startTime: this.startTime,
        totalDurationMs: 0,
        phases: {
          rssParsingMs: 0,
          firecrawlProcessingMs: 0,
          openaiProcessingMs: 0,
          databaseOperationsMs: 0,
        },
      },
      processing: {
        feedsProcessed: 0,
        articlesScraped: 0,
        articlesAccepted: 0,
        articlesRejected: 0,
        successRate: 0,
        throughputPerMinute: 0,
      },
      apiUsage: {
        firecrawl: {
          requestsCount: 0,
          successCount: 0,
          rateLimitedCount: 0,
          errorCount: 0,
          averageResponseTimeMs: 0,
          estimatedCost: 0,
        },
        openai: {
          requestsCount: 0,
          tokensUsed: 0,
          averageResponseTimeMs: 0,
          estimatedCost: 0,
          enhancementSuccessRate: 0,
        },
      },
      feedMetrics: [],
      system: {
        memoryUsageMB: 0,
        cpuUsagePercent: 0,
        parallelProcessingEfficiency: 0,
      },
      quality: {
        averageContentQuality: 0,
        duplicateFilterEfficiency: 0,
        keywordMatchAccuracy: 0,
      },
    };
  }

  /**
   * Start tracking a feed's processing
   */
  startFeedProcessing(feed: {
    id: string;
    name: string;
    priority: string;
    language: string;
  }): void {
    const feedMetric: FeedMetrics = {
      feedId: feed.id,
      feedName: feed.name,
      priority: feed.priority as 'high' | 'medium' | 'low',
      language: feed.language as 'en' | 'de',
      timing: {
        startTime: Date.now(),
        endTime: 0,
        durationMs: 0,
      },
      processing: {
        itemsFound: 0,
        itemsProcessed: 0,
        itemsAccepted: 0,
        itemsRejected: 0,
        duplicatesFiltered: 0,
        keywordRejects: 0,
        firecrawlFailures: 0,
        openaiFailures: 0,
      },
      performance: {
        successRate: 0,
        averageProcessingTimePerItem: 0,
        costPerAcceptedArticle: 0,
      },
    };

    this.feedMetrics.set(feed.id, feedMetric);
  }

  /**
   * Update feed processing metrics
   */
  updateFeedMetrics(
    feedId: string,
    updates: Partial<FeedMetrics['processing']>
  ): void {
    const feedMetric = this.feedMetrics.get(feedId);
    if (feedMetric) {
      Object.assign(feedMetric.processing, updates);
    }
  }

  /**
   * Finish tracking a feed's processing
   */
  finishFeedProcessing(feedId: string): void {
    const feedMetric = this.feedMetrics.get(feedId);
    if (feedMetric) {
      feedMetric.timing.endTime = Date.now();
      feedMetric.timing.durationMs =
        feedMetric.timing.endTime - feedMetric.timing.startTime;

      // Calculate performance metrics
      const { processing } = feedMetric;
      feedMetric.performance.successRate =
        processing.itemsProcessed > 0
          ? (processing.itemsAccepted / processing.itemsProcessed) * 100
          : 0;

      feedMetric.performance.averageProcessingTimePerItem =
        processing.itemsProcessed > 0
          ? feedMetric.timing.durationMs / processing.itemsProcessed
          : 0;

      // Update global metrics
      this.metrics.processing.feedsProcessed++;
      this.metrics.processing.articlesScraped += processing.itemsProcessed;
      this.metrics.processing.articlesAccepted += processing.itemsAccepted;
      this.metrics.processing.articlesRejected += processing.itemsRejected;
    }
  }

  /**
   * Track API usage (Firecrawl)
   */
  trackFirecrawlUsage(
    success: boolean,
    responseTimeMs: number,
    rateLimited: boolean = false
  ): void {
    const { firecrawl } = this.metrics.apiUsage;

    firecrawl.requestsCount++;
    if (success) {
      firecrawl.successCount++;
    } else if (rateLimited) {
      firecrawl.rateLimitedCount++;
    } else {
      firecrawl.errorCount++;
    }

    // Update average response time
    firecrawl.averageResponseTimeMs =
      (firecrawl.averageResponseTimeMs * (firecrawl.requestsCount - 1) +
        responseTimeMs) /
      firecrawl.requestsCount;

    // Estimate cost (approx $2 per 1000 successful requests)
    firecrawl.estimatedCost = (firecrawl.successCount / 1000) * 2;
  }

  /**
   * Track API usage (OpenAI)
   */
  trackOpenAIUsage(
    tokensUsed: number,
    responseTimeMs: number,
    success: boolean
  ): void {
    const { openai } = this.metrics.apiUsage;

    openai.requestsCount++;
    openai.tokensUsed += tokensUsed;

    // Update average response time
    openai.averageResponseTimeMs =
      (openai.averageResponseTimeMs * (openai.requestsCount - 1) +
        responseTimeMs) /
      openai.requestsCount;

    // Estimate cost (GPT-4 pricing: ~$0.03 per 1K input tokens, ~$0.06 per 1K output tokens)
    // Simplified estimation assuming 70% input, 30% output
    const inputTokens = tokensUsed * 0.7;
    const outputTokens = tokensUsed * 0.3;
    const cost = (inputTokens / 1000) * 0.03 + (outputTokens / 1000) * 0.06;
    openai.estimatedCost += cost;

    // Track success rate
    openai.enhancementSuccessRate = success
      ? (openai.enhancementSuccessRate * (openai.requestsCount - 1) + 100) /
        openai.requestsCount
      : (openai.enhancementSuccessRate * (openai.requestsCount - 1)) /
        openai.requestsCount;
  }

  /**
   * Track phase timing
   */
  trackPhase(
    phase: keyof PipelineMetrics['timing']['phases'],
    durationMs: number
  ): void {
    this.metrics.timing.phases[phase] += durationMs;
  }

  /**
   * Capture system performance metrics
   */
  updateSystemMetrics(): void {
    const memUsage = process.memoryUsage();
    this.metrics.system.memoryUsageMB = Math.round(
      memUsage.heapUsed / 1024 / 1024
    );

    // CPU usage is more complex to calculate accurately, simplified here
    this.metrics.system.cpuUsagePercent = 0; // Would need proper CPU monitoring

    // Calculate parallel processing efficiency based on timing
    const { phases } = this.metrics.timing;
    const sequentialTime =
      phases.rssParsingMs +
      phases.firecrawlProcessingMs +
      phases.openaiProcessingMs;
    const actualTime = this.metrics.timing.totalDurationMs;
    this.metrics.system.parallelProcessingEfficiency =
      actualTime > 0 ? (sequentialTime / actualTime) * 100 : 0;
  }

  /**
   * Finalize metrics collection
   */
  finalize(): PipelineMetrics {
    this.metrics.timing.endTime = Date.now();
    this.metrics.timing.totalDurationMs =
      this.metrics.timing.endTime - this.metrics.timing.startTime;

    // Calculate overall success rate
    const totalProcessed = this.metrics.processing.articlesScraped;
    this.metrics.processing.successRate =
      totalProcessed > 0
        ? (this.metrics.processing.articlesAccepted / totalProcessed) * 100
        : 0;

    // Calculate throughput (articles per minute)
    const durationMinutes = this.metrics.timing.totalDurationMs / (1000 * 60);
    this.metrics.processing.throughputPerMinute =
      durationMinutes > 0
        ? this.metrics.processing.articlesAccepted / durationMinutes
        : 0;

    // Update system metrics
    this.updateSystemMetrics();

    // Convert feed metrics to array
    this.metrics.feedMetrics = Array.from(this.feedMetrics.values());

    // Calculate quality metrics
    this.calculateQualityMetrics();

    return this.metrics;
  }

  /**
   * Calculate quality metrics
   */
  private calculateQualityMetrics(): void {
    const totalFeeds = this.metrics.feedMetrics.length;
    if (totalFeeds === 0) return;

    let totalDuplicates = 0;
    let totalFound = 0;
    let totalKeywordRejects = 0;

    this.metrics.feedMetrics.forEach(feed => {
      totalDuplicates += feed.processing.duplicatesFiltered;
      totalFound += feed.processing.itemsFound;
      totalKeywordRejects += feed.processing.keywordRejects;
    });

    // Duplicate filter efficiency
    this.metrics.quality.duplicateFilterEfficiency =
      totalFound > 0 ? (totalDuplicates / totalFound) * 100 : 0;

    // Keyword match accuracy (inverse of keyword rejects)
    this.metrics.quality.keywordMatchAccuracy =
      totalFound > 0
        ? ((totalFound - totalKeywordRejects) / totalFound) * 100
        : 0;

    // Average content quality (simplified based on success rates)
    const avgSuccessRate =
      this.metrics.feedMetrics.reduce(
        (sum, feed) => sum + feed.performance.successRate,
        0
      ) / totalFeeds;
    this.metrics.quality.averageContentQuality = avgSuccessRate;
  }

  /**
   * Get current metrics snapshot
   */
  getSnapshot(): PipelineMetrics {
    const snapshot = { ...this.metrics };
    snapshot.timing.totalDurationMs = Date.now() - this.startTime;
    return snapshot;
  }
}

/**
 * Create a new metrics collector instance
 */
export function createMetricsCollector(): PipelineMetricsCollector {
  return new PipelineMetricsCollector();
}

/**
 * Format metrics for logging
 */
export function formatMetricsForLogging(metrics: PipelineMetrics): string {
  const duration = (metrics.timing.totalDurationMs / 1000).toFixed(2);
  const successRate = metrics.processing.successRate.toFixed(1);
  const throughput = metrics.processing.throughputPerMinute.toFixed(1);
  const totalCost = (
    metrics.apiUsage.firecrawl.estimatedCost +
    metrics.apiUsage.openai.estimatedCost
  ).toFixed(4);

  return `
📊 PIPELINE PERFORMANCE METRICS
════════════════════════════════
⏱️  Duration: ${duration}s
📈 Success Rate: ${successRate}%
🔄 Throughput: ${throughput} articles/min
💰 Total Cost: $${totalCost}

📡 FEEDS PROCESSED: ${metrics.processing.feedsProcessed}
📄 Articles Scraped: ${metrics.processing.articlesScraped}
✅ Articles Accepted: ${metrics.processing.articlesAccepted}
❌ Articles Rejected: ${metrics.processing.articlesRejected}

🌐 FIRECRAWL API:
   Requests: ${metrics.apiUsage.firecrawl.requestsCount}
   Success: ${metrics.apiUsage.firecrawl.successCount}
   Rate Limited: ${metrics.apiUsage.firecrawl.rateLimitedCount}
   Avg Response: ${metrics.apiUsage.firecrawl.averageResponseTimeMs.toFixed(0)}ms
   Cost: $${metrics.apiUsage.firecrawl.estimatedCost.toFixed(4)}

🤖 OPENAI API:
   Requests: ${metrics.apiUsage.openai.requestsCount}
   Tokens: ${metrics.apiUsage.openai.tokensUsed.toLocaleString()}
   Success Rate: ${metrics.apiUsage.openai.enhancementSuccessRate.toFixed(1)}%
   Avg Response: ${metrics.apiUsage.openai.averageResponseTimeMs.toFixed(0)}ms
   Cost: $${metrics.apiUsage.openai.estimatedCost.toFixed(4)}

💾 SYSTEM:
   Memory: ${metrics.system.memoryUsageMB}MB
   Parallel Efficiency: ${metrics.system.parallelProcessingEfficiency.toFixed(1)}%

🎯 QUALITY:
   Content Quality: ${metrics.quality.averageContentQuality.toFixed(1)}%
   Duplicate Filter: ${metrics.quality.duplicateFilterEfficiency.toFixed(1)}%
   Keyword Accuracy: ${metrics.quality.keywordMatchAccuracy.toFixed(1)}%
════════════════════════════════
`;
}

/**
 * Export metrics to JSON for analysis
 */
export function exportMetricsToJSON(metrics: PipelineMetrics): string {
  return JSON.stringify(metrics, null, 2);
}

/**
 * Save metrics to file for historical analysis
 */
export async function saveMetricsToFile(
  metrics: PipelineMetrics,
  filename?: string
): Promise<string> {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filepath = filename || `pipeline-metrics-${timestamp}.json`;

  try {
    const fs = await import('fs/promises');
    const path = await import('path');

    const metricsDir = path.join(process.cwd(), 'logs', 'metrics');
    await fs.mkdir(metricsDir, { recursive: true });

    const fullPath = path.join(metricsDir, filepath);
    await fs.writeFile(fullPath, exportMetricsToJSON(metrics));

    return fullPath;
  } catch (error) {
    console.error('Failed to save metrics to file:', error);
    throw error;
  }
}
