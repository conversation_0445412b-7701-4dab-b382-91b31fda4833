# Site-wide Unified Caching Strategy

## 🎯 Overview

This directory contains the **unified caching implementation** for the Börsen Blick application, ensuring **consistent performance** and **data integrity** across all page types.

## 📁 File Structure

```
src/lib/cache/
├── constants.ts        # Unified cache configuration
├── categories.ts       # Category page caching functions
├── articles.ts         # Article page caching functions
├── invalidation.ts     # Cross-page cache invalidation
└── README.md           # This documentation
```

## 🏗️ Architecture Principles

### **1. Unified Standards**

- **Same patterns** across homepage, articles, and category pages
- **Consistent cache durations** (300s for dynamic content, 3600s for static)
- **Standardised tag naming** for cross-page invalidation

### **2. Smart Field Selection**

- **Minimal field sets** for list views (categories, homepage tiers)
- **Hero field sets** for featured content
- **No heavy content fields** in list queries (prevents 2MB cache limits)

### **3. Cross-page Invalidation**

- **Article updates** → invalidate homepage + affected category pages
- **Category updates** → invalidate affected pages
- **Automatic hooks** in PayloadCMS collections

## 📊 Field Selection Strategy

### **LIST_MINIMAL** - For Category Lists, Homepage Tiers

```typescript
{
  id: true,
  title: true,
  slug: true,
  publishedAt: true,
  workflowStage: true,
  placement: true,
  trending: true,
  readTimeMinutes: true,     // ✅ Pre-computed field
  categories: true,          // PayloadCMS handles relations
  // ❌ NO content fields (germanContent, englishContent)
}
```

**Cache Impact**: ~50KB per category page (vs 2.77MB before)

### **HERO_FEATURED** - For Homepage Hero, Category Hero

```typescript
{
  // All LIST_MINIMAL fields, plus:
  featuredImage: true,
  germanTab: {
    germanTitle: true,
    germanSummary: true,
    // ❌ NO germanContent
  },
  englishTab: {
    enhancedTitle: true,
    enhancedSummary: true,
    // ❌ NO enhancedContent
  }
}
```

**Cache Impact**: ~150KB for hero sections

### **Why This Works**

- **Reading Time**: Pre-computed in `readTimeMinutes` field (no content needed)
- **Summaries**: Available for display without full content
- **Performance**: 95%+ cache size reduction
- **Scalability**: Works with 1000+ articles per category

## 🔄 Cache Invalidation System

### **Article Changes → Invalidates:**

```typescript
// Core article caches
`article-${articleId}`;
CACHE_TAGS.ARTICLES;

// Homepage (if published)
CACHE_TAGS.TIERS.TIER_1;
CACHE_TAGS.TIERS.TIER_2;
CACHE_TAGS.TIERS.TIER_3
// Affected categories
`category-${categorySlug}`;
```

### **Category Changes → Invalidates:**

```typescript
// Category-specific
`category-${categorySlug}`
CACHE_TAGS.CATEGORIES

// Homepage (if affects homepage)
CACHE_TAGS.TIERS.*
```

### **Automatic Hooks**

- **Articles Collection**: `createArticleInvalidationHook()`
- **Categories Collection**: `createCategoryInvalidationHook()`
- **Failure Safe**: Cache errors don't break saves

## ⚡ Performance Impact

### **Before Optimization**

- **Category Cache Size**: 2.77MB per page (exceeded 2MB limit)
- **Cache Failures**: Frequent 502 errors
- **Loading Heavy Content**: For simple readTime calculations
- **Inconsistent Patterns**: Different approaches per page type

### **After Optimization**

- **Category Cache Size**: ~50KB per page (98% reduction)
- **No Cache Failures**: Well under 2MB limit
- **Pre-computed Fields**: readTime calculated once during processing
- **Unified Patterns**: Consistent across all page types

## 📋 Usage Examples

### **Category Pages**

```typescript
// ✅ Correct usage
const data = await getCachedCategoryArticles(categorySlug);
const layout = organizeCategoryLayout(data);
```

### **Manual Cache Invalidation**

```typescript
// Invalidate specific article
await invalidateArticleCaches(articleId, categories, {
  isPublished: true,
  revalidatePaths: true,
});

// Nuclear option (development only)
await invalidateAllCaches();
```

### **Field Selection**

```typescript
// ✅ Use pre-defined field sets
select: FIELD_SETS.LIST_MINIMAL;

// ❌ Don't create custom field selections
select: {
  germanTab: {
    germanContent: true;
  } // This causes 2MB cache issues
}
```

## 🚨 Common Pitfalls & Solutions

### **Problem**: Cache Size Exceeded (2MB)

**Cause**: Including heavy content fields in list queries
**Solution**: Use `FIELD_SETS.LIST_MINIMAL` for lists

### **Problem**: Inconsistent Performance

**Cause**: Different caching patterns across pages
**Solution**: Always use `CACHE_DURATIONS.DEFAULT` and `CACHE_TAGS.*`

### **Problem**: Stale Data After Updates

**Cause**: Missing cache invalidation
**Solution**: Ensure `afterChange` hooks are configured

### **Problem**: Reading Time Calculation Performance

**Cause**: Computing readTime on-the-fly from content
**Solution**: Use pre-computed `readTimeMinutes` field

## 📈 Monitoring & Maintenance

### **Health Checks**

- Monitor cache hit rates (target >90%)
- Watch for cache size warnings
- Check invalidation success rates

### **Performance Metrics**

- Page load times <2s
- Cache generation <5s
- Database query efficiency

### **Regular Maintenance**

- Review field selections quarterly
- Update cache durations based on content patterns
- Monitor cross-page invalidation effectiveness

## 🔧 Development Workflow

### **Adding New Cached Functions**

1. Use `FIELD_SETS` from `constants.ts`
2. Apply consistent cache duration and tags
3. Add appropriate invalidation hooks
4. Test cache size under 2MB limit

### **Modifying Existing Cache Functions**

1. Test field selection changes
2. Verify invalidation still works
3. Check performance impact
4. Update documentation

## 📚 Related Documentation

- **Database Workflow**: `.ai/06-Docs/database-workflow/`
- **Phase 2 Plan**: `.ai/04-Doing/fix-category-plan.md`
- **PayloadCMS Cache Docs**: [Official Documentation](https://payloadcms.com/docs/database/overview)

---

**Last Updated**: July 2025  
**Phase**: 2 - Data Optimization Complete  
**Status**: ✅ Production Ready
