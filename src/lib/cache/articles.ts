import { unstable_cache } from 'next/cache';
import { getPayload } from 'payload';
import config from '@/payload.config';
import type { Article } from '@/payload-types';
import type { PaginatedDocs } from 'payload';

// Type definitions for better type safety
export interface TierArticles extends PaginatedDocs<Article> {}

export interface CachedTierData {
  tier1: TierArticles;
  tier2: TierArticles;
  tier3: TierArticles;
}

// New interface for advanced Tier 1 layout
export interface Tier1Layout {
  prominentPost: Article | null; // Position 1: Spans 2 columns (pinned priority)
  gridPosts: Article[]; // Positions 2-7: 6 posts for 2x3 grid
  horizontalPosts: Article[]; // Remaining: Horizontal layout
}

// Individual tier fetching functions
const fetchTierArticles = async (
  tier: 'tier-1' | 'tier-2' | 'tier-3',
  limit: number
): Promise<TierArticles> => {
  const payload = await getPayload({ config });

  const sortConfig = {
    'tier-1': ['-pinned', '-publishedAt'],
    'tier-2': ['-publishedAt'],
    'tier-3': ['-publishedAt'],
  };

  return await payload.find({
    collection: 'articles',
    where: {
      and: [
        { placement: { equals: tier } },
        { _status: { equals: 'published' } },
      ],
    },
    sort: sortConfig[tier],
    limit,
    depth: 2,
  });
};

// Enhanced tier-1 fetching for advanced layout
const fetchTier1ArticlesForLayout = async (): Promise<TierArticles> => {
  const payload = await getPayload({ config });

  return await payload.find({
    collection: 'articles',
    where: {
      and: [
        { placement: { equals: 'tier-1' } },
        { _status: { equals: 'published' } },
      ],
    },
    sort: ['-pinned', '-publishedAt'], // Pinned first, then newest
    limit: 50, // Increased limit to handle all tier-1 articles
    depth: 2,
  });
};

// Layout organization logic
const organizeTier1Layout = (articles: Article[]): Tier1Layout => {
  // Articles already sorted by PayloadCMS: pinned first, then by publishedAt
  const [prominentPost, ...remainingArticles] = articles;

  const gridPosts = remainingArticles.slice(0, 6); // Next 6 posts for 2x3 grid
  const horizontalPosts = remainingArticles.slice(6); // All remaining posts

  return {
    prominentPost: prominentPost || null,
    gridPosts,
    horizontalPosts,
  };
};

// Single article fetching function
const fetchArticleBySlug = async (slug: string): Promise<Article | null> => {
  const payload = await getPayload({ config });

  const result = await payload.find({
    collection: 'articles',
    where: {
      and: [{ slug: { equals: slug } }, { _status: { equals: 'published' } }],
    },
    limit: 1,
    depth: 2,
  });

  return result.docs[0] || null;
};

// Fetch all published article slugs for static generation
const fetchPublishedArticleSlugs = async (): Promise<{ slug: string }[]> => {
  const payload = await getPayload({ config });

  const result = await payload.find({
    collection: 'articles',
    where: {
      _status: { equals: 'published' },
    },
    limit: 1000, // Reasonable limit for static generation
    depth: 0, // Only need slug field
  });

  return result.docs
    .filter(article => article.slug)
    .map(article => ({ slug: article.slug! }));
};

// Cached tier fetching functions with proper Next.js caching
export const getCachedTier1Articles = unstable_cache(
  async () => fetchTierArticles('tier-1', 3),
  ['tier-1-articles'],
  {
    revalidate: 300, // 5 minutes
    tags: ['articles', 'tier-1'],
  }
);

// New cached tier-1 layout function
export const getCachedTier1Layout = unstable_cache(
  async (): Promise<Tier1Layout> => {
    const tier1Data = await fetchTier1ArticlesForLayout();
    return organizeTier1Layout(tier1Data.docs);
  },
  ['tier-1-layout'],
  {
    revalidate: 300, // 5 minutes
    tags: ['articles', 'tier-1', 'layout'],
  }
);

export const getCachedTier2Articles = unstable_cache(
  async () => fetchTierArticles('tier-2', 10), // Increased for column 4 usage
  ['tier-2-articles'],
  {
    revalidate: 300, // 5 minutes
    tags: ['articles', 'tier-2'],
  }
);

export const getCachedTier3Articles = unstable_cache(
  async () => fetchTierArticles('tier-3', 8),
  ['tier-3-articles'],
  {
    revalidate: 300, // 5 minutes
    tags: ['articles', 'tier-3'],
  }
);

// Cached single article fetching - standardized 5 minute cache for published articles
export const getCachedArticleBySlug = (slug: string) =>
  unstable_cache(async () => fetchArticleBySlug(slug), ['article', slug], {
    revalidate: 300, // 5 minutes (standardized)
    tags: ['articles', `article-${slug}`],
  });

// Cached published article slugs for static generation - standardized cache duration
export const getCachedPublishedArticleSlugs = unstable_cache(
  async () => fetchPublishedArticleSlugs(),
  ['published-article-slugs'],
  {
    revalidate: 300, // 5 minutes (standardized with homepage and other caches)
    tags: ['articles', 'published-slugs'],
  }
);

// Combined cached data fetching
export const getCachedAllTierArticles = unstable_cache(
  async (): Promise<CachedTierData> => {
    const [tier1, tier2, tier3] = await Promise.all([
      fetchTierArticles('tier-1', 3),
      fetchTierArticles('tier-2', 6),
      fetchTierArticles('tier-3', 8),
    ]);

    return { tier1, tier2, tier3 };
  },
  ['all-tier-articles'],
  {
    revalidate: 300, // 5 minutes
    tags: ['articles'],
  }
);
