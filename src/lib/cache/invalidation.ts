/**
 * Site-wide Cache Invalidation System
 *
 * Provides centralized cache invalidation to ensure data consistency
 * across all page types when content is updated.
 */

import { CACHE_TAGS, generateCacheKey } from './constants';
import type { Article, Category } from '@/payload-types';

/**
 * Helper to dynamically import Next.js cache functions (server-only)
 */
const getNextCacheFunctions = async () => {
  const { revalidateTag, revalidatePath } = await import('next/cache');
  return { revalidateTag, revalidatePath };
};

/**
 * Invalidate all caches related to a specific article update
 */
export const invalidateArticleCaches = async (
  articleId: number,
  categories: (Category | number)[] = [],
  options: {
    revalidatePaths?: boolean;
    isNewArticle?: boolean;
    isPublished?: boolean;
  } = {}
) => {
  console.log(`🔄 Invalidating caches for article ${articleId}...`);

  // Dynamic import of server-only functions
  const { revalidateTag, revalidatePath } = await getNextCacheFunctions();

  try {
    // Core article-specific tags
    const tagsToInvalidate = [CACHE_TAGS.ARTICLES, `article-${articleId}`];

    // Homepage tier invalidation (if published)
    if (options.isPublished || options.isNewArticle) {
      tagsToInvalidate.push(
        CACHE_TAGS.TIERS.TIER_1,
        CACHE_TAGS.TIERS.TIER_2,
        CACHE_TAGS.TIERS.TIER_3
      );
    }

    // Category-specific invalidation
    for (const category of categories) {
      const categoryId = typeof category === 'object' ? category.id : category;
      const categorySlug =
        typeof category === 'object' ? category.slug : undefined;

      if (categorySlug) {
        tagsToInvalidate.push(generateCacheKey.category(categorySlug));
      }

      tagsToInvalidate.push(`category-${categoryId}`);
    }

    // Invalidate all tags
    await Promise.all(tagsToInvalidate.map(tag => revalidateTag(tag)));

    // Path revalidation (optional, more expensive)
    if (options.revalidatePaths) {
      const pathsToRevalidate = [
        '/', // Homepage
      ];

      // Add category paths
      for (const category of categories) {
        if (typeof category === 'object' && category.slug) {
          pathsToRevalidate.push(`/categories/${category.slug}`);
        }
      }

      await Promise.all(pathsToRevalidate.map(path => revalidatePath(path)));
    }

    console.log(`✅ Cache invalidation completed for article ${articleId}`);
    console.log(`   - Tags invalidated: ${tagsToInvalidate.length}`);
    console.log(
      `   - Paths revalidated: ${options.revalidatePaths ? 'Yes' : 'No'}`
    );
  } catch (error) {
    console.error('❌ Cache invalidation error:', error);
    throw error;
  }
};

/**
 * Invalidate all caches related to a specific category update
 */
export const invalidateCategoryCaches = async (
  categorySlug: string,
  categoryId?: number,
  options: {
    revalidatePaths?: boolean;
    affectsHomepage?: boolean;
  } = {}
) => {
  console.log(`🔄 Invalidating caches for category ${categorySlug}...`);

  // Dynamic import of server-only functions
  const { revalidateTag, revalidatePath } = await getNextCacheFunctions();

  try {
    const tagsToInvalidate = [
      CACHE_TAGS.CATEGORIES,
      generateCacheKey.category(categorySlug),
    ];

    if (categoryId) {
      tagsToInvalidate.push(`category-${categoryId}`);
    }

    // If category changes affect homepage (e.g., category name change)
    if (options.affectsHomepage) {
      tagsToInvalidate.push(
        CACHE_TAGS.TIERS.TIER_1,
        CACHE_TAGS.TIERS.TIER_2,
        CACHE_TAGS.TIERS.TIER_3
      );
    }

    // Invalidate tags
    await Promise.all(tagsToInvalidate.map(tag => revalidateTag(tag)));

    // Path revalidation
    if (options.revalidatePaths) {
      await Promise.all([
        revalidatePath('/'),
        revalidatePath(`/categories/${categorySlug}`),
      ]);
    }

    console.log(`✅ Cache invalidation completed for category ${categorySlug}`);
  } catch (error) {
    console.error('❌ Category cache invalidation error:', error);
    throw error;
  }
};

/**
 * Nuclear option: Invalidate all caches site-wide
 */
export const invalidateAllCaches = async () => {
  console.log('🚨 Nuclear cache invalidation - clearing all caches...');

  // Dynamic import of server-only functions
  const { revalidateTag, revalidatePath } = await getNextCacheFunctions();

  try {
    const allTags = [
      CACHE_TAGS.ARTICLES,
      CACHE_TAGS.CATEGORIES,
      CACHE_TAGS.TIERS.TIER_1,
      CACHE_TAGS.TIERS.TIER_2,
      CACHE_TAGS.TIERS.TIER_3,
      CACHE_TAGS.NAVIGATION,
    ];

    await Promise.all(allTags.map(tag => revalidateTag(tag)));

    // Revalidate key paths
    await Promise.all([
      revalidatePath('/'),
      revalidatePath('/categories/[slug]', 'page'),
      revalidatePath('/articles/[slug]', 'page'),
    ]);

    console.log('✅ Nuclear cache invalidation completed');
  } catch (error) {
    console.error('❌ Nuclear cache invalidation error:', error);
    throw error;
  }
};

/**
 * Utility: Get cache tags that would be affected by article changes
 */
export const getAffectedCacheTags = (
  article: Partial<Article>,
  categories: (Category | number)[] = []
) => {
  const tags = [CACHE_TAGS.ARTICLES, `article-${article.id}`];

  // Add homepage tags if published
  if ((article as any)._status === 'published') {
    tags.push(
      CACHE_TAGS.TIERS.TIER_1,
      CACHE_TAGS.TIERS.TIER_2,
      CACHE_TAGS.TIERS.TIER_3
    );
  }

  // Add category tags
  for (const category of categories) {
    const categorySlug =
      typeof category === 'object' ? category.slug : undefined;
    if (categorySlug) {
      tags.push(generateCacheKey.category(categorySlug));
    }
  }

  return tags;
};

/**
 * Invalidate all caches related to page updates
 * ✅ Updated to use PayloadCMS utility tag format (pages_slug)
 */
export const invalidatePageCaches = async (
  pageSlug: string,
  options: {
    revalidatePaths?: boolean;
    wasPublished?: boolean;
  } = {}
) => {
  console.log(`🔄 Invalidating caches for page ${pageSlug}...`);

  // Dynamic import of server-only functions
  const { revalidateTag, revalidatePath } = await getNextCacheFunctions();

  try {
    // ✅ Use PayloadCMS utility tag format
    const tagsToInvalidate = [
      CACHE_TAGS.PAGES,
      `pages_${pageSlug}`, // Matches getCachedDocument tag format
      'published-page-slugs', // For static generation
    ];

    // Invalidate tags
    for (const tag of tagsToInvalidate) {
      revalidateTag(tag);
    }

    // Revalidate paths if requested
    if (options.revalidatePaths) {
      const path = pageSlug === 'home' ? '/' : `/${pageSlug}`;
      revalidatePath(path);
    }

    console.log(`✅ Page cache invalidation completed for ${pageSlug}`);
  } catch (error) {
    console.error(`❌ Error invalidating page caches for ${pageSlug}:`, error);
    throw error;
  }
};

/**
 * Hook for PayloadCMS: Auto-invalidate caches on article changes
 */
export const createArticleInvalidationHook = () => {
  return async ({ doc, previousDoc, req, operation }: any) => {
    try {
      // Only invalidate for updates and creates that affect published content
      if (operation === 'create' || operation === 'update') {
        const categories = doc.categories || [];

        await invalidateArticleCaches(doc.id, categories, {
          isNewArticle: operation === 'create',
          isPublished: (doc as any)._status === 'published',
          revalidatePaths: (doc as any)._status === 'published', // Only revalidate paths for published articles
        });
      }

      return doc;
    } catch (error) {
      console.error('Cache invalidation hook error:', error);
      return doc; // Don't fail the operation if cache invalidation fails
    }
  };
};

/**
 * Hook for PayloadCMS: Auto-invalidate caches on category changes
 */
export const createCategoryInvalidationHook = () => {
  return async ({ doc, previousDoc, req, operation }: any) => {
    try {
      if (operation === 'update' || operation === 'create') {
        await invalidateCategoryCaches(doc.slug, doc.id, {
          revalidatePaths: true,
          affectsHomepage:
            operation === 'create' ||
            (previousDoc && previousDoc.title !== doc.title), // Name changed
        });
      }

      return doc;
    } catch (error) {
      console.error('Category cache invalidation hook error:', error);
      return doc;
    }
  };
};

/**
 * Hook for PayloadCMS: Auto-invalidate caches on page changes
 */
export const createPageInvalidationHook = () => {
  return async ({ doc, previousDoc, req, operation }: any) => {
    try {
      // Handle page updates and creates
      if (operation === 'create' || operation === 'update') {
        if (doc._status === 'published') {
          await invalidatePageCaches(doc.slug, {
            revalidatePaths: true,
          });
        }

        // If page was unpublished, also revalidate the old path
        if (
          previousDoc?._status === 'published' &&
          doc._status !== 'published'
        ) {
          await invalidatePageCaches(previousDoc.slug, {
            revalidatePaths: true,
          });
        }
      }

      return doc;
    } catch (error) {
      console.error('Page cache invalidation hook error:', error);
      return doc;
    }
  };
};

/**
 * Hook for PayloadCMS: Auto-invalidate caches on page deletion
 */
export const createPageDeletionHook = () => {
  return async ({ doc, req }: any) => {
    try {
      if (doc?.slug) {
        await invalidatePageCaches(doc.slug, {
          revalidatePaths: true,
        });
      }

      return doc;
    } catch (error) {
      console.error('Page deletion cache invalidation hook error:', error);
      return doc;
    }
  };
};

/**
 * Invalidate global configuration caches
 */
export const invalidateGlobalCaches = async (globalSlug: string) => {
  console.log(`🔄 Invalidating global caches for ${globalSlug}...`);

  // Dynamic import of server-only functions
  const { revalidateTag } = await getNextCacheFunctions();

  try {
    revalidateTag(`global_${globalSlug}`);
    console.log(`✅ Global cache invalidation completed for ${globalSlug}`);
  } catch (error) {
    console.error(
      `❌ Error invalidating global caches for ${globalSlug}:`,
      error
    );
    throw error;
  }
};
