// Re-export PayloadCMS generated types
export type {
  Article,
  ProcessedUrl,
  RssFeed,
  Keyword,
  Category,
} from '@/payload-types';

export * from './api';

export interface CreateCandidateArticleData {
  title: string;
  content: string;
  sourceUrl: string;
  sourceFeed: string;
  publishedDate?: Date;
  author?: string;
}

export interface CreateProcessedUrlData {
  url: string;
  title: string;
  feedId?: string; // Make this optional to match the database schema
  status: 'pending' | 'accepted' | 'rejected' | 'error';
  reason?: string;
  articleId?: string;
  publicationDate?: Date;
}

export interface UpdateRSSFeedStatsData {
  feedId: string;
  itemsProcessed?: number;
  itemsAccepted?: number;
  lastProcessedAt?: Date;
}

export interface OpenAIResponse {
  success: boolean;
  text?: string;
  error?: string;
  tokensUsed?: number;
  errorCode?: number;
  errorType?: string;
  isRefusal?: boolean;
  originalContent?: string;
  characterCleaning?: {
    hasIssues: boolean;
    issues: string[];
    cleanedLength: number;
    originalLength: number;
  };
}

export interface FirecrawlResponse {
  success: boolean;
  content?: string;
  title?: string;
  author?: string;
  publishedDate?: Date;
  error?: string;
  metadata?: {
    wordCount: number;
    language?: string;
    readingTime?: number;
  };
}
