import { z } from 'zod';

/**
 * Simple API input validation schemas
 * Prevents injection attacks and ensures data integrity
 */

// Article enhancement endpoint validation
export const articleEnhanceSchema = z.object({
  articleId: z
    .string()
    .min(1, 'Article ID is required')
    .max(50, 'Article ID too long')
    .regex(/^[a-zA-Z0-9-_]+$/, 'Article ID contains invalid characters'),
});

// Load content endpoint validation (for any future parameters)
export const loadContentSchema = z
  .object({
    force: z.boolean().optional(),
    limit: z.number().min(1).max(1000).optional(),
  })
  .optional();

// Pipeline endpoint validation
export const pipelineSchema = z
  .object({
    maxArticles: z.number().min(1).max(50).optional(),
    feedId: z.string().max(50).optional(),
    testMode: z.boolean().optional(),
  })
  .optional();

// Admin operations validation
export const adminOperationSchema = z.object({
  confirm: z.boolean().optional(),
  dryRun: z.boolean().optional(),
});

// Generic ID validation (reusable)
export const idSchema = z.object({
  id: z
    .string()
    .min(1, 'ID is required')
    .max(50, 'ID too long')
    .regex(/^[a-zA-Z0-9-_]+$/, 'ID contains invalid characters'),
});

// Text input validation (prevents XSS)
export const textInputSchema = z
  .string()
  .max(10000, 'Text too long')
  .refine(text => {
    // Basic XSS prevention - no script tags
    return !/<script/i.test(text);
  }, 'Invalid characters detected');

/**
 * Validation helper function for API routes
 */
export async function validateInput<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): Promise<{ success: true; data: T } | { success: false; error: string }> {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors
        .map(err => `${err.path.join('.')}: ${err.message}`)
        .join(', ');
      return { success: false, error: `Validation error: ${errorMessage}` };
    }
    return { success: false, error: 'Invalid input format' };
  }
}
