import { unstable_cache } from 'next/cache';
import { getPayload } from 'payload';
import config from '@/payload.config';
import type { Article, Category, Keyword } from '@/payload-types';

interface ScoredArticle {
  article: Article;
  score: number;
  reasons: string[];
}

interface RelatedArticlesOptions {
  limit?: number;
  includeCurrentArticle?: boolean;
  minScore?: number;
}

/**
 * Get related articles based on category, keywords, tier, and recency
 * Scoring algorithm:
 * - Same category articles (weight: 0.4)
 * - Shared keywords match (weight: 0.3)
 * - Same placement tier (weight: 0.2)
 * - Recent publication date (weight: 0.1)
 */
export async function getRelatedArticles(
  currentArticle: Article,
  options: RelatedArticlesOptions = {}
): Promise<Article[]> {
  const { limit = 4, includeCurrentArticle = false, minScore = 0.1 } = options;

  try {
    const payload = await getPayload({ config });

    // Fetch all published articles for comparison
    const allArticles = await payload.find({
      collection: 'articles',
      where: {
        _status: { equals: 'published' },
      },
      limit: 100, // Reasonable limit for scoring
      depth: 2, // Include related data
      sort: '-publishedAt',
    });

    // Filter out current article if not included
    const candidateArticles = allArticles.docs.filter(
      article => includeCurrentArticle || article.id !== currentArticle.id
    );

    // Score each article
    const scoredArticles: ScoredArticle[] = candidateArticles.map(article => {
      const scoring = calculateRelatedScore(currentArticle, article);
      return {
        article,
        score: scoring.totalScore,
        reasons: scoring.reasons,
      };
    });

    // Sort by score (highest first) and filter by minimum score
    const rankedArticles = scoredArticles
      .filter(scored => scored.score >= minScore)
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(scored => scored.article);

    return rankedArticles;
  } catch (error) {
    console.error('Error fetching related articles:', error);
    return [];
  }
}

/**
 * Cached version of getRelatedArticles with 5-minute cache (matches homepage)
 */
export const getCachedRelatedArticles = (articleId: string | number) =>
  unstable_cache(
    async (currentArticle: Article, options: RelatedArticlesOptions = {}) =>
      getRelatedArticles(currentArticle, options),
    ['related-articles', String(articleId)],
    {
      revalidate: 300, // 5 minutes (standardized with homepage)
      tags: ['articles', 'related-articles', `article-${articleId}`],
    }
  );

/**
 * Calculate relatedness score between two articles
 */
function calculateRelatedScore(
  currentArticle: Article,
  candidateArticle: Article
): {
  totalScore: number;
  reasons: string[];
} {
  let totalScore = 0;
  const reasons: string[] = [];

  // 1. Same category match (weight: 0.4)
  const categoryScore = calculateCategoryScore(
    currentArticle,
    candidateArticle
  );
  if (categoryScore > 0) {
    totalScore += categoryScore * 0.4;
    reasons.push(`Category match (${(categoryScore * 0.4).toFixed(2)})`);
  }

  // 2. Shared keywords match (weight: 0.3)
  const keywordScore = calculateKeywordScore(currentArticle, candidateArticle);
  if (keywordScore > 0) {
    totalScore += keywordScore * 0.3;
    reasons.push(`Keyword match (${(keywordScore * 0.3).toFixed(2)})`);
  }

  // 3. Same placement tier (weight: 0.2)
  const tierScore = calculateTierScore(currentArticle, candidateArticle);
  if (tierScore > 0) {
    totalScore += tierScore * 0.2;
    reasons.push(`Same tier (${(tierScore * 0.2).toFixed(2)})`);
  }

  // 4. Recent publication date (weight: 0.1)
  const recencyScore = calculateRecencyScore(candidateArticle);
  totalScore += recencyScore * 0.1;
  if (recencyScore > 0.5) {
    reasons.push(`Recent (${(recencyScore * 0.1).toFixed(2)})`);
  }

  return {
    totalScore: Math.min(totalScore, 1), // Cap at 1.0
    reasons,
  };
}

/**
 * Calculate category matching score (0-1)
 */
function calculateCategoryScore(
  currentArticle: Article,
  candidateArticle: Article
): number {
  const currentCategories = extractCategoryIds(currentArticle.categories);
  const candidateCategories = extractCategoryIds(candidateArticle.categories);

  if (currentCategories.length === 0 || candidateCategories.length === 0) {
    return 0;
  }

  // Find intersection of categories
  const commonCategories = currentCategories.filter(catId =>
    candidateCategories.includes(catId)
  );

  // Score based on ratio of common categories
  const maxCategories = Math.max(
    currentCategories.length,
    candidateCategories.length
  );
  return commonCategories.length / maxCategories;
}

/**
 * Extract keyword text from article keywords arrays
 */
function extractKeywordText(keywords: any[]): string[] {
  if (!keywords || !Array.isArray(keywords)) {
    return [];
  }

  return keywords
    .map(item => item?.keyword)
    .filter(keyword => typeof keyword === 'string' && keyword.trim().length > 0)
    .map(keyword => keyword.toLowerCase().trim());
}

/**
 * Calculate keyword matching score (0-1)
 */
function calculateKeywordScore(
  currentArticle: Article,
  candidateArticle: Article
): number {
  // Extract keywords from both English and German tabs
  const currentEnglishKeywords = extractKeywordText(
    (currentArticle as any).englishTab?.keywords || []
  );
  const currentGermanKeywords = extractKeywordText(
    (currentArticle as any).germanTab?.germanKeywords || []
  );
  const currentAllKeywords = [
    ...currentEnglishKeywords,
    ...currentGermanKeywords,
  ];

  const candidateEnglishKeywords = extractKeywordText(
    (candidateArticle as any).englishTab?.keywords || []
  );
  const candidateGermanKeywords = extractKeywordText(
    (candidateArticle as any).germanTab?.germanKeywords || []
  );
  const candidateAllKeywords = [
    ...candidateEnglishKeywords,
    ...candidateGermanKeywords,
  ];

  if (currentAllKeywords.length === 0 || candidateAllKeywords.length === 0) {
    return 0;
  }

  // Find intersection of keywords (case insensitive)
  const commonKeywords = currentAllKeywords.filter(keyword =>
    candidateAllKeywords.includes(keyword)
  );

  // Score based on ratio of common keywords
  const maxKeywords = Math.max(
    currentAllKeywords.length,
    candidateAllKeywords.length
  );
  return commonKeywords.length / maxKeywords;
}

/**
 * Calculate tier matching score (0-1)
 */
function calculateTierScore(
  currentArticle: Article,
  candidateArticle: Article
): number {
  if (!currentArticle.placement || !candidateArticle.placement) {
    return 0;
  }

  return currentArticle.placement === candidateArticle.placement ? 1 : 0;
}

/**
 * Calculate recency score based on publication date (0-1)
 */
function calculateRecencyScore(article: Article): number {
  const publishedAt = new Date(article.publishedAt || article.createdAt);
  const now = new Date();
  const ageInDays =
    (now.getTime() - publishedAt.getTime()) / (1000 * 60 * 60 * 24);

  // Articles published within the last 7 days get full score
  // Score decreases linearly over 30 days, then approaches 0
  if (ageInDays <= 7) {
    return 1;
  } else if (ageInDays <= 30) {
    return Math.max(0, 1 - (ageInDays - 7) / 23); // Linear decrease from 1 to 0 over 23 days
  } else {
    return Math.max(0, 0.1 - (ageInDays - 30) / 365); // Very slow decrease after 30 days
  }
}

/**
 * Extract category IDs from categories field (handles both ID references and populated objects)
 */
function extractCategoryIds(categories: any): string[] {
  if (!Array.isArray(categories)) {
    return [];
  }

  return categories
    .map(cat => {
      if (typeof cat === 'string' || typeof cat === 'number') {
        return String(cat);
      }
      if (typeof cat === 'object' && cat?.id) {
        return String(cat.id);
      }
      return null;
    })
    .filter((id): id is string => id !== null);
}
