import { type NextRequest, NextResponse } from 'next/server';
import { getPayload } from 'payload';
import config from '@/payload.config';

/**
 * API endpoint to diagnose article data corruption
 * Specifically targets the "Cannot create property 'id' on string" error
 */
export async function GET(request: NextRequest) {
  try {
    // Temporarily return early to prevent TypeScript compilation errors
    return NextResponse.json(
      {
        success: false,
        message:
          'Route temporarily disabled due to TypeScript compilation errors',
        status: 'disabled',
      },
      { status: 503 }
    );

    /* 
    // TODO: Fix TypeScript errors before re-enabling this diagnostic code
    console.log('🔍 Diagnosing article data corruption...');

    const payload = await getPayload({ config });
    const { searchParams } = new URL(request.url);
    const articleId = searchParams.get('id') || '265';

    // First, check the specific article causing the error
    console.log(`\n📄 Checking article ${articleId} specifically...`);

    let article;
    try {
      article = await payload.findByID({
        collection: 'articles',
        id: articleId,
        depth: 0, // Get raw data without relationships
      });

      console.log('✅ Article found in database');
      console.log('📋 Basic info:', {
        id: article.id,
        title: article.title,
        workflowStage: article.workflowStage,
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Error fetching article ${articleId}:`, errorMessage);
      return NextResponse.json({
        success: false,
        error: `Could not fetch article ${articleId}: ${errorMessage}`,
      });
    }

    const diagnostics = {
      articleId: article.id,
      title: article.title,
      corruption: {
        englishKeywords: null,
        germanKeywords: null,
        categories: null,
        relatedCompanies: null,
        keywordsMatched: null,
      },
      rawData: {},
    };

    // Check keywords in English tab
    if (article.englishTab?.keywords) {
      const keywordsInfo = {
        type: typeof article.englishTab.keywords,
        isArray: Array.isArray(article.englishTab.keywords),
        length: article.englishTab.keywords?.length || 0,
        items: [] as Array<{
          index: number;
          type: string;
          value: any;
          hasKeywordProperty: boolean;
          isCorrupted: boolean;
        }>,
        hasCorruption: false,
      };

      if (Array.isArray(article.englishTab.keywords)) {
        article.englishTab.keywords.forEach((item, index) => {
          const itemInfo = {
            index,
            type: typeof item,
            value: item,
            hasKeywordProperty: typeof item === 'object' && 'keyword' in item,
            isCorrupted: typeof item === 'string',
          };
          keywordsInfo.items.push(itemInfo);
          if (itemInfo.isCorrupted) {
            keywordsInfo.hasCorruption = true;
          }
        });
      }

      diagnostics.corruption.englishKeywords = keywordsInfo;
      diagnostics.rawData.englishKeywords = article.englishTab.keywords;
    }

    // Check keywords in German tab
    if (article.germanTab?.germanKeywords) {
      const keywordsInfo = {
        type: typeof article.germanTab.germanKeywords,
        isArray: Array.isArray(article.germanTab.germanKeywords),
        length: article.germanTab.germanKeywords?.length || 0,
        items: [],
        hasCorruption: false,
      };

      if (Array.isArray(article.germanTab.germanKeywords)) {
        article.germanTab.germanKeywords.forEach((item, index) => {
          const itemInfo = {
            index,
            type: typeof item,
            value: item,
            hasKeywordProperty: typeof item === 'object' && 'keyword' in item,
            isCorrupted: typeof item === 'string',
          };
          keywordsInfo.items.push(itemInfo);
          if (itemInfo.isCorrupted) {
            keywordsInfo.hasCorruption = true;
          }
        });
      }

      diagnostics.corruption.germanKeywords = keywordsInfo;
      diagnostics.rawData.germanKeywords = article.germanTab.germanKeywords;
    }

    // Check categories relationship
    if (article.categories) {
      const categoriesInfo = {
        type: typeof article.categories,
        isArray: Array.isArray(article.categories),
        length: article.categories?.length || 0,
        items: [],
        hasCorruption: false,
      };

      if (Array.isArray(article.categories)) {
        article.categories.forEach((item, index) => {
          const itemInfo = {
            index,
            type: typeof item,
            value: item,
            hasIdProperty: typeof item === 'object' && 'id' in item,
            isCorrupted: typeof item === 'string',
          };
          categoriesInfo.items.push(itemInfo);
          if (itemInfo.isCorrupted) {
            categoriesInfo.hasCorruption = true;
          }
        });
      }

      diagnostics.corruption.categories = categoriesInfo;
      diagnostics.rawData.categories = article.categories;
    }

    // Check related companies
    if (article.relatedCompanies) {
      const companiesInfo = {
        type: typeof article.relatedCompanies,
        isArray: Array.isArray(article.relatedCompanies),
        length: article.relatedCompanies?.length || 0,
        items: [],
        hasCorruption: false,
      };

      if (Array.isArray(article.relatedCompanies)) {
        article.relatedCompanies.forEach((item, index) => {
          const itemInfo = {
            index,
            type: typeof item,
            value: typeof item === 'string' ? item : 'object',
            hasRequiredFields: typeof item === 'object' && 'name' in item,
            isCorrupted: typeof item === 'string',
          };
          companiesInfo.items.push(itemInfo);
          if (itemInfo.isCorrupted) {
            companiesInfo.hasCorruption = true;
          }
        });
      }

      diagnostics.corruption.relatedCompanies = companiesInfo;
      diagnostics.rawData.relatedCompanies = article.relatedCompanies;
    }

    // Check keywordsMatched relationship
    if (article.keywordsMatched) {
      const keywordsMatchedInfo = {
        type: typeof article.keywordsMatched,
        isArray: Array.isArray(article.keywordsMatched),
        length: article.keywordsMatched?.length || 0,
        items: [],
        hasCorruption: false,
      };

      if (Array.isArray(article.keywordsMatched)) {
        article.keywordsMatched.forEach((item, index) => {
          const itemInfo = {
            index,
            type: typeof item,
            value: item,
            hasIdProperty: typeof item === 'object' && 'id' in item,
            isCorrupted: typeof item === 'string',
          };
          keywordsMatchedInfo.items.push(itemInfo);
          if (itemInfo.isCorrupted) {
            keywordsMatchedInfo.hasCorruption = true;
          }
        });
      }

      diagnostics.corruption.keywordsMatched = keywordsMatchedInfo;
      diagnostics.rawData.keywordsMatched = article.keywordsMatched;
    }

    // Determine overall corruption status
    const hasAnyCorruption = Object.values(diagnostics.corruption).some(
      field => field?.hasCorruption === true
    );

    console.log('📊 Diagnostics completed');
    console.log('Has corruption:', hasAnyCorruption);

    return NextResponse.json({
      success: true,
      hasCorruption: hasAnyCorruption,
      diagnostics,
      message: hasAnyCorruption
        ? 'Data corruption detected - see diagnostics for details'
        : 'No data corruption found in this article',
    });
    */
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    console.error('❌ Error during diagnosis:', error);

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        message: 'Failed to diagnose article corruption',
      },
      { status: 500 }
    );
  }
}
