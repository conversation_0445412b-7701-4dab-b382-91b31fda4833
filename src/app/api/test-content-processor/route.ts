import { NextResponse } from 'next/server';
import { englishOnlyContentEnhancement } from '@/lib/integrations/openai/unified-enhancement';
import { htmlToLexical } from '@/lib/utils/html-to-lexical';
import { lexicalToText } from '@/lib/utils/lexical';
import { withAuth } from '@/lib/auth/simple-auth';

/**
 * Test endpoint for the current English-only content processing pipeline
 * Tests the unified enhancement system with German to English transformation
 */
async function _POST() {
  try {
    console.log('🧪 Testing English-Only Content Processor...');

    // Sample German financial content with structure (HTML format)
    const sampleGermanContent = `<h1>Deutsche Bank steigt um 4,2% nach starken Quartalszahlen</h1>

<p>Die Deutsche Bank AG hat heute ihre Quartalsergebnisse veröffentlicht und die Erwartungen der Analysten übertroffen. Die Aktie reagierte positiv auf die Nachrichten.</p>

<h2>Wichtige Kennzahlen</h2>

<p>Das Finanzinstitut meldete einen Nettogewinn von 1,2 Milliarden Euro für das dritte Quartal, verglichen mit 1,0 Milliarden Euro im Vorjahresquartal. Die Erträge stiegen um 8% auf 7,4 Milliarden Euro.</p>

<h3>Ausblick für Investoren</h3>

<p>Experten sehen weiteres Potenzial für deutsche Bankaktien, warnen jedoch vor möglicher Volatilität. Die kommenden Wochen werden zeigen, ob sich dieser Trend fortsetzt.</p>

<p><strong>Wichtige Punkte:</strong></p>
<ul>
<li>Nettogewinn: +20% gegenüber Vorjahr</li>
<li>Erträge: +8% auf 7,4 Mrd. Euro</li>
<li>Aktienperformance: +4,2% im frühen Handel</li>
</ul>`;

    console.log('📝 Input German content length:', sampleGermanContent.length);

    // Test the English-only enhancement system
    const startTime = Date.now();
    const result = await englishOnlyContentEnhancement(
      'Test Title',
      sampleGermanContent
    );
    const processingTime = Date.now() - startTime;

    console.log('✅ English-only enhancement completed');
    console.log('📊 Result summary:');
    console.log('   Success:', result.success);
    console.log('   Processing time:', `${processingTime}ms`);
    console.log(
      '   Enhanced title:',
      result.data?.enhancedContent?.title || 'No title'
    );
    console.log(
      '   Enhanced content length:',
      result.data?.enhancedContent?.content?.length || 0
    );
    console.log(
      '   Summary length:',
      result.data?.enhancedContent?.summary?.length || 0
    );

    // Convert enhanced content to Lexical format for testing
    let lexicalResult: object | null = null;
    let lexicalConversionSuccess = false;

    try {
      if (result.data?.enhancedContent?.content) {
        lexicalResult = await htmlToLexical(
          result.data.enhancedContent.content
        );
        lexicalConversionSuccess = true;
        console.log('📄 Lexical conversion: Success');
      }
    } catch (error) {
      console.error('❌ Lexical conversion failed:', error);
    }

    // Test lexical-to-text conversion
    let textPreview = '';
    try {
      if (lexicalResult) {
        textPreview = lexicalToText(lexicalResult).substring(0, 200) + '...';
        console.log('📝 Text extraction: Success');
      }
    } catch (error) {
      console.error('❌ Text extraction failed:', error);
    }

    // Validate the enhanced content structure
    const hasValidStructure =
      lexicalResult &&
      typeof lexicalResult === 'object' &&
      'root' in lexicalResult;

    return NextResponse.json({
      success: true,
      message: 'English-only content processor test completed',
      processingTime: `${processingTime}ms`,
      input: {
        germanContent: sampleGermanContent,
        contentLength: sampleGermanContent.length,
      },
      enhancement: {
        success: result.success,
        enhancedTitle: result.data?.enhancedContent?.title || null,
        enhancedContent: result.data?.enhancedContent?.content || null,
        summary: result.data?.enhancedContent?.summary || null,
        enhancedContentLength:
          result.data?.enhancedContent?.content?.length || 0,
        summaryLength: result.data?.enhancedContent?.summary?.length || 0,
        qualityScore: result.data?.quality?.contentScore || 0,
        relevanceScore: result.data?.quality?.relevanceScore || 0,
        enhancementQuality: result.data?.quality?.enhancementQuality || null,
        processingMetadata: result.data?.processing || null,
      },
      lexical: {
        conversionSuccess: lexicalConversionSuccess,
        hasValidStructure,
        structureType: hasValidStructure
          ? 'Valid Lexical format'
          : 'Invalid Lexical format',
        textPreview,
      },
      validation: {
        titlePresent: !!result.data?.enhancedContent?.title,
        contentPresent: !!result.data?.enhancedContent?.content,
        summaryPresent: !!result.data?.enhancedContent?.summary,
        qualityScoreValid:
          typeof result.data?.quality?.contentScore === 'number' &&
          result.data.quality.contentScore >= 0 &&
          result.data.quality.contentScore <= 100,
        relevanceScoreValid:
          typeof result.data?.quality?.relevanceScore === 'number' &&
          result.data.quality.relevanceScore >= 0 &&
          result.data.quality.relevanceScore <= 100,
      },
      // Include a sample of the Lexical JSON for inspection (first 500 chars)
      lexicalSample: lexicalResult
        ? JSON.stringify(lexicalResult).substring(0, 500) + '...'
        : 'No Lexical data',
    });
  } catch (error) {
    console.error('❌ Error testing English-only content processor:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint for simple testing without POST data
 */
async function _GET() {
  return NextResponse.json({
    message: 'English-Only Content Processor Test Endpoint',
    description: 'Use POST to run the test with sample German content',
    currentSystem: 'English-only enhancement (German → Enhanced English)',
    features: [
      'Direct German to English transformation',
      'Enhanced content generation (600-750 words)',
      'Quality and relevance scoring',
      'Lexical rich text format conversion',
      'Single API call processing (~70% cost reduction)',
    ],
    endpoints: {
      test: 'POST /api/test-content-processor',
    },
  });
}

// Export protected routes (authentication required for content processing tests)
export const GET = withAuth(_GET);
export const POST = withAuth(_POST);
