import { NextRequest, NextResponse } from 'next/server';
import { getPayload } from 'payload';
import config from '@/payload.config';
import type { Article } from '@/payload-types';
import { FIELD_SETS } from '@/lib/cache/constants';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  const { slug } = await params;
  const searchParams = request.nextUrl.searchParams;

  // Pagination parameters
  const page = parseInt(searchParams.get('page') || '1', 10);
  const limit = parseInt(searchParams.get('limit') || '12', 10);
  const tier = searchParams.get('tier'); // optional tier filter
  const trending = searchParams.get('trending') === 'true'; // trending filter
  const loadMore = searchParams.get('loadMore') === 'true'; // for progressive loading

  try {
    const payload = await getPayload({ config });

    // First, get the category
    const categoryResult = await payload.find({
      collection: 'categories',
      where: { slug: { equals: slug } },
      limit: 1,
      depth: 1,
    });

    if (categoryResult.docs.length === 0) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    const category = categoryResult.docs[0];

    // Build query conditions
    const whereConditions: any = {
      and: [
        { categories: { in: [category.id] } },
        { _status: { equals: 'published' } },
      ],
    };

    // Add tier filter if specified
    if (tier) {
      whereConditions.and.push({ placement: { equals: tier } });
    }

    // Add trending filter if specified
    if (trending) {
      whereConditions.and.push({ trending: { equals: true } });
    }

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Fetch articles with pagination
    const articlesResult = await payload.find({
      collection: 'articles',
      where: whereConditions,
      sort: ['-pinned', '-publishedAt'],
      limit,
      page,
      depth: 1,
      select:
        tier === 'tier-1' ? FIELD_SETS.HERO_FEATURED : FIELD_SETS.LIST_MINIMAL,
    });

    // If this is a load-more request, also get tier statistics
    let tierStats = null;
    if (loadMore) {
      const [tier1Count, tier2Count, tier3Count, trendingCount] =
        await Promise.all([
          payload.count({
            collection: 'articles',
            where: {
              and: [
                { categories: { in: [category.id] } },
                { placement: { equals: 'tier-1' } },
                { _status: { equals: 'published' } },
              ],
            },
          }),
          payload.count({
            collection: 'articles',
            where: {
              and: [
                { categories: { in: [category.id] } },
                { placement: { equals: 'tier-2' } },
                { _status: { equals: 'published' } },
              ],
            },
          }),
          payload.count({
            collection: 'articles',
            where: {
              and: [
                { categories: { in: [category.id] } },
                { placement: { equals: 'tier-3' } },
                { _status: { equals: 'published' } },
              ],
            },
          }),
          payload.count({
            collection: 'articles',
            where: {
              and: [
                { categories: { in: [category.id] } },
                { trending: { equals: true } },
                { _status: { equals: 'published' } },
              ],
            },
          }),
        ]);

      tierStats = {
        tier1: tier1Count,
        tier2: tier2Count,
        tier3: tier3Count,
        trending: trendingCount.totalDocs,
      };
    }

    return NextResponse.json({
      articles: articlesResult.docs,
      pagination: {
        currentPage: page,
        totalPages: articlesResult.totalPages,
        totalDocs: articlesResult.totalDocs,
        hasNextPage: articlesResult.hasNextPage,
        hasPrevPage: articlesResult.hasPrevPage,
        limit,
      },
      category: {
        id: category.id,
        title: category.title,
        slug: category.slug,
      },
      tierStats,
      requestInfo: {
        tier,
        trending,
        loadMore,
      },
    });
  } catch (error) {
    console.error('Error fetching category articles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch articles' },
      { status: 500 }
    );
  }
}
