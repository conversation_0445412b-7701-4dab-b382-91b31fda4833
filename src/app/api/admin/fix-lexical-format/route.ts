/**
 * API Route to Fix Lexical JSON Format Issue
 *
 * The originalContentEnglish field is storing Lexical data as JSON strings
 * instead of proper JSON objects, causing the admin interface to show
 * raw JSON instead of rendered rich text.
 */

import { type NextRequest, NextResponse } from 'next/server';
import { getPayload } from 'payload';
import config from '@/payload.config';
import type { APIError, LexicalContent } from '@/lib/types';
import { withAuth } from '@/lib/auth/simple-auth';

async function _POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config });

    console.log('🔍 Diagnosing originalContentEnglish data format...');

    // First, diagnose the current state
    const articles = await payload.find({
      collection: 'articles',
      where: {
        'sourcesTab.originalContent': {
          exists: true,
        },
      },
      limit: 100, // Process in batches
      depth: 0,
    });

    const diagnosis = {
      total: 0,
      stringified: 0,
      properJson: 0,
      invalid: 0,
      examples: [] as Array<{
        id: string;
        type: 'string' | 'object' | 'invalid';
        preview: string;
      }>,
    };

    // Analyze current data format
    for (const article of articles.docs) {
      const content = article.sourcesTab?.originalContent as
        | LexicalContent
        | string
        | undefined;
      if (!content) continue;

      diagnosis.total++;

      let type: 'string' | 'object' | 'invalid';
      let preview: string;

      if (typeof content === 'string') {
        // Check if it's a stringified JSON
        try {
          const parsed = JSON.parse(content);
          if (parsed && typeof parsed === 'object' && parsed.root) {
            type = 'string';
            diagnosis.stringified++;
            preview = `String: ${content.substring(0, 100)}...`;
          } else {
            type = 'invalid';
            diagnosis.invalid++;
            preview = `Invalid JSON string: ${content.substring(0, 50)}...`;
          }
        } catch {
          type = 'invalid';
          diagnosis.invalid++;
          preview = `Non-JSON string: ${content.substring(0, 50)}...`;
        }
      } else if (typeof content === 'object' && content !== null) {
        type = 'object';
        diagnosis.properJson++;
        preview = `Object: ${JSON.stringify(content).substring(0, 100)}...`;
      } else {
        type = 'invalid';
        diagnosis.invalid++;
        preview = `Invalid type: ${typeof content}`;
      }

      if (diagnosis.examples.length < 3) {
        diagnosis.examples.push({
          id: String(article.id),
          type,
          preview,
        });
      }
    }

    // Check if the Lexical JSON structure is valid
    console.log('🔍 Checking Lexical JSON structure validity...');

    for (const article of articles.docs.slice(0, 2)) {
      const content = article.sourcesTab?.originalContent;
      if (content && typeof content === 'object') {
        console.log(`📋 Article ${article.id} Lexical structure:`, {
          hasRoot: !!content.root,
          rootType: content.root?.type,
          rootChildren: Array.isArray(content.root?.children),
          childrenCount: content.root?.children?.length || 0,
          firstChildType: content.root?.children?.[0]?.type,
          sample: JSON.stringify(content).substring(0, 200) + '...',
        });
      }
    }

    // If we found stringified JSON, fix it
    let fixResults = null;
    if (diagnosis.stringified > 0) {
      console.log(
        `🔧 Found ${diagnosis.stringified} articles with stringified JSON. Fixing...`
      );

      let fixed = 0;
      let errors = 0;
      const errorDetails: string[] = [];

      for (const article of articles.docs) {
        const content = article.sourcesTab?.originalContent;

        if (typeof content === 'string') {
          try {
            // Try to parse the stringified JSON
            const parsed = JSON.parse(content);

            // Validate it's a proper Lexical object
            if (parsed && typeof parsed === 'object' && parsed.root) {
              console.log(`   📝 Fixing article ${article.id}...`);

              await payload.update({
                collection: 'articles',
                id: article.id,
                data: {
                  sourcesTab: {
                    ...article.sourcesTab,
                    originalContent: parsed, // Store as object, not string
                  },
                },
              });

              fixed++;
            }
          } catch (error: unknown) {
            const apiError = error as APIError;
            console.error(
              `   ❌ Failed to fix article ${article.id}:`,
              apiError
            );
            errors++;
            errorDetails.push(
              `Article ${article.id}: ${apiError.message || 'Unknown error'}`
            );
          }
        }
      }

      fixResults = {
        fixed,
        errors,
        errorDetails,
      };
    }

    return NextResponse.json({
      success: true,
      diagnosis,
      fixResults,
      message:
        diagnosis.stringified > 0
          ? `Fixed ${fixResults?.fixed || 0} articles with stringified JSON`
          : 'No stringified JSON found. Data is already in correct format.',
    });
  } catch (error: unknown) {
    const apiError = error as APIError;
    console.error('❌ Error during fix:', apiError);
    return NextResponse.json(
      {
        success: false,
        error: apiError.message || 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Use POST to run the Lexical format fix',
    description:
      'This endpoint fixes originalContentEnglish fields that are stored as JSON strings instead of proper JSON objects',
  });
}

// Export protected POST route (authentication required for admin operations)
export const POST = withAuth(_POST);
