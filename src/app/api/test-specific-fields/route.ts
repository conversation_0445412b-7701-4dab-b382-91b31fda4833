import config from '@payload-config';
import { NextResponse } from 'next/server';
import { getPayload } from 'payload';

/**
 * Test Specific RSS Feed Fields
 *
 * Tests that customTimeout and enableStealth fields are working correctly
 */
export async function POST() {
  const startTime = Date.now();

  try {
    console.log('🧪 Testing Specific RSS Feed Fields...');

    const payload = await getPayload({ config });

    // Get all active RSS feeds
    const feeds = await payload.find({
      collection: 'rss-feeds',
      where: {
        isActive: { equals: true },
      },
      limit: 100,
    });

    console.log(`📋 Found ${feeds.docs.length} active RSS feeds`);

    // Test each feed's specific settings
    const fieldTests = [];

    for (const feed of feeds.docs) {
      const feedTest = {
        name: feed.name,
        url: feed.url,
        customTimeout: null as any,
        enableStealth: null as any,
        firecrawlOptions: null as any,
        testResults: {
          timeoutWorking: false,
          stealthWorking: false,
          optionsParsed: false,
        },
      };

      // Test processingOptions parsing
      if ((feed as any).processingOptions) {
        const procOpts = (feed as any).processingOptions;
        feedTest.customTimeout = procOpts.customTimeout;
        feedTest.enableStealth = procOpts.enableStealth;
        feedTest.testResults.optionsParsed = true;

        console.log(`🔍 Testing ${feed.name}:`);
        console.log(`   ⏱️  Custom Timeout: ${procOpts.customTimeout}s`);
        console.log(
          `   🥷 Stealth Mode: ${procOpts.enableStealth ? 'enabled' : 'disabled'}`
        );

        // Test timeout conversion logic
        if (procOpts.customTimeout) {
          const timeoutMs = procOpts.customTimeout * 1000;
          console.log(`   ✅ Timeout would be set to: ${timeoutMs}ms`);
          feedTest.testResults.timeoutWorking = true;
        }

        // Test stealth mode logic
        if (procOpts.enableStealth) {
          console.log(`   ✅ Stealth mode would be enabled`);
          feedTest.testResults.stealthWorking = true;
        }
      } else {
        console.log(`⚠️  ${feed.name}: No processing options found`);
      }

      // Test firecrawl options parsing
      if ((feed as any).firecrawlOptions) {
        const firecrawlOpts = (feed as any).firecrawlOptions;
        feedTest.firecrawlOptions = {
          removeBase64Images: firecrawlOpts.removeBase64Images,
          blockAds: firecrawlOpts.blockAds,
          excludeTagsCount: firecrawlOpts.excludeTags?.length || 0,
          includeTagsCount: firecrawlOpts.includeTags?.length || 0,
        };

        console.log(`   🎛️  Firecrawl Options:`);
        console.log(
          `      - Remove Base64 Images: ${firecrawlOpts.removeBase64Images}`
        );
        console.log(`      - Block Ads: ${firecrawlOpts.blockAds}`);
        console.log(
          `      - Exclude Tags: ${firecrawlOpts.excludeTags?.length || 0} configured`
        );
        console.log(
          `      - Include Tags: ${firecrawlOpts.includeTags?.length || 0} configured`
        );
      }

      fieldTests.push(feedTest);
    }

    // Create summary
    const summary = {
      totalFeeds: feeds.docs.length,
      feedsWithCustomTimeout: fieldTests.filter(f => f.customTimeout).length,
      feedsWithStealth: fieldTests.filter(f => f.enableStealth).length,
      feedsWithFirecrawlOptions: fieldTests.filter(f => f.firecrawlOptions)
        .length,
      timeoutRange: fieldTests
        .filter(f => f.customTimeout)
        .map(f => f.customTimeout)
        .sort((a, b) => a - b),
      stealthFeeds: fieldTests.filter(f => f.enableStealth).map(f => f.name),
    };

    console.log('\n📊 Test Summary:');
    console.log(`   • Total feeds: ${summary.totalFeeds}`);
    console.log(
      `   • Feeds with custom timeout: ${summary.feedsWithCustomTimeout}`
    );
    console.log(`   • Feeds with stealth mode: ${summary.feedsWithStealth}`);
    console.log(
      `   • Feeds with firecrawl options: ${summary.feedsWithFirecrawlOptions}`
    );
    console.log(
      `   • Timeout range: ${summary.timeoutRange.join(', ')} seconds`
    );
    console.log(`   • Stealth feeds: ${summary.stealthFeeds.join(', ')}`);

    // Now test the actual buildFeedSpecificOptions function
    console.log('\n🧪 Testing buildFeedSpecificOptions function...');

    // Import the RSS processing service
    const { RSSProcessingService } = await import(
      '@/utilities/RSSProcessingService'
    );
    const rssService = new RSSProcessingService();

    // Test with a feed that has custom settings
    const testFeed = feeds.docs.find(
      f => (f as any).processingOptions?.customTimeout
    );
    if (testFeed) {
      console.log(
        `\n🔍 Testing buildFeedSpecificOptions with: ${testFeed.name}`
      );

      // This will call buildFeedSpecificOptions internally and show the console output
      try {
        const feedOptions = (rssService as any).buildFeedSpecificOptions(
          testFeed
        );
        console.log('✅ buildFeedSpecificOptions successful');
        console.log('🎛️  Generated options:', feedOptions);
      } catch (error: any) {
        console.log('❌ buildFeedSpecificOptions failed:', error.message);
      }
    }

    const processingTime = Date.now() - startTime;

    return NextResponse.json({
      success: true,
      message: 'Specific RSS Feed Fields Test Completed',
      timing: {
        totalTime: processingTime,
        completedAt: new Date().toISOString(),
      },
      results: {
        fieldTests,
        summary,
        testsPassed: {
          timeoutFieldsWorking: fieldTests.filter(
            f => f.testResults.timeoutWorking
          ).length,
          stealthFieldsWorking: fieldTests.filter(
            f => f.testResults.stealthWorking
          ).length,
          optionsParsingWorking: fieldTests.filter(
            f => f.testResults.optionsParsed
          ).length,
        },
      },
    });
  } catch (error: any) {
    console.error('❌ Test failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: error.message,
        message: 'Specific RSS Feed Fields Test Failed',
      },
      { status: 500 }
    );
  }
}
