import { type NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';
import { withAuth } from '@/lib/auth/simple-auth';

/**
 * API endpoint to check OpenAI quota status and recent errors
 * GET /api/openai-status
 */
async function _GET(request: NextRequest) {
  try {
    const logPath = join(process.cwd(), 'logs', 'openai-quota-errors.json');

    let recentErrors: any[] = [];
    let lastError: any = null;

    try {
      const logContent = await readFile(logPath, 'utf-8');
      recentErrors = JSON.parse(logContent);

      // Get the most recent error
      if (recentErrors.length > 0) {
        lastError = recentErrors[recentErrors.length - 1];
      }
    } catch {
      // No log file exists yet
    }

    // Check if there are recent quota errors (within last hour)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const recentQuotaErrors = recentErrors.filter(
      error =>
        new Date(error.timestamp) > oneHourAgo && error.error.status === 429
    );

    const status = {
      hasRecentQuotaErrors: recentQuotaErrors.length > 0,
      recentQuotaErrorCount: recentQuotaErrors.length,
      lastError,
      totalErrorsLogged: recentErrors.length,
      checkTime: new Date().toISOString(),
      recommendations: [] as string[],
    };

    // Add recommendations based on status
    if (status.hasRecentQuotaErrors) {
      status.recommendations.push(
        '🚨 URGENT: OpenAI API quota exceeded',
        'Check billing and usage at https://platform.openai.com/usage',
        'Consider upgrading your OpenAI plan or adding credits',
        'RSS processing may be creating articles without English translations'
      );
    } else {
      status.recommendations.push(
        '✅ No recent quota issues detected',
        'OpenAI API appears to be functioning normally'
      );
    }

    return NextResponse.json({
      success: true,
      status,
      timestamp: new Date().toISOString(),
    });
  } catch (error: any) {
    console.error('Failed to check OpenAI status:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to check OpenAI status',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

/**
 * Clear the quota error log
 * DELETE /api/openai-status
 */
async function _DELETE(request: NextRequest) {
  try {
    const logPath = join(process.cwd(), 'logs', 'openai-quota-errors.json');

    // Write empty array to clear the log
    const { writeFile } = await import('fs/promises');
    await writeFile(logPath, JSON.stringify([], null, 2));

    return NextResponse.json({
      success: true,
      message: 'OpenAI quota error log cleared',
      timestamp: new Date().toISOString(),
    });
  } catch (error: any) {
    console.error('Failed to clear OpenAI error log:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to clear error log',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// Export protected routes (authentication required for OpenAI system monitoring)
export const GET = withAuth(_GET);
export const DELETE = withAuth(_DELETE);
