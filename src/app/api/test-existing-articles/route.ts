/**
 * Test HTML-to-Lexical Conversion with Existing Articles
 *
 * API endpoint to test the conversion system with real articles from the database.
 * Useful for validating the conversion quality with actual content.
 */

import { type NextRequest, NextResponse } from 'next/server';
import { getPayload } from 'payload';
import { ContentProcessorIntegration } from '@/lib/server/content-processor-integration';
import { lexicalToText } from '@/lib/utils/lexical';
import config from '@/payload.config';
import { withAuth } from '@/lib/auth/simple-auth';

async function _GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '5');
    const testConversion = searchParams.get('test') === 'true';

    console.log('🔍 Testing with existing articles:', {
      limit,
      testConversion,
    });

    // Get PayloadCMS instance
    const payload = await getPayload({ config });

    // Fetch recent articles
    const articles = await payload.find({
      collection: 'articles',
      limit,
      sort: '-createdAt',
      where: {
        'sourcesTab.originalContent': {
          not_equals: null,
        },
      },
    });

    if (articles.docs.length === 0) {
      return NextResponse.json({
        message: 'No articles found with original content',
        count: 0,
        articles: [],
      });
    }

    const results = [];

    for (const article of articles.docs) {
      const originalContentText = article.sourcesTab?.originalContent
        ? lexicalToText(article.sourcesTab.originalContent)
        : '';

      const articleResult: any = {
        id: article.id,
        title: article.title,
        originalContentLength: originalContentText.length,
        hasEnhancedEnglishContent: !!article.englishTab?.enhancedContent,
      };

      // If testing is requested, run conversion
      if (testConversion && originalContentText) {
        try {
          const processor = ContentProcessorIntegration.getInstance();
          const conversionResult = await processor.processContent(
            originalContentText,
            'html'
          );

          articleResult.conversionTest = {
            strategy: conversionResult.strategy,
            success: conversionResult.metrics.success,
            processingTime: conversionResult.metrics.processingTime,
            qualityScore: conversionResult.metrics.qualityScore,
            fallbackUsed: conversionResult.metrics.fallbackUsed,
            metadata: conversionResult.metadata,
            warnings: conversionResult.warnings,
            errors: conversionResult.errors,
          };

          console.log(`✅ Tested article ${article.id}:`, {
            strategy: conversionResult.strategy,
            success: conversionResult.metrics.success,
            time: conversionResult.metrics.processingTime,
          });
        } catch (error) {
          articleResult.conversionTest = {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          };

          console.error(`❌ Failed to test article ${article.id}:`, error);
        }
      }

      results.push(articleResult);
    }

    // Calculate summary statistics if testing was performed
    let summary = null;
    if (testConversion) {
      const successful = results.filter(r => r.conversionTest?.success).length;
      const avgTime =
        results
          .filter(r => r.conversionTest?.processingTime)
          .reduce((sum, r) => sum + r.conversionTest.processingTime, 0) /
        successful;

      const strategies = results
        .filter(r => r.conversionTest?.strategy)
        .reduce(
          (acc, r) => {
            acc[r.conversionTest.strategy] =
              (acc[r.conversionTest.strategy] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>
        );

      summary = {
        totalTested: results.length,
        successful,
        successRate: (successful / results.length) * 100,
        averageProcessingTime: Math.round(avgTime),
        strategiesUsed: strategies,
      };
    }

    return NextResponse.json({
      message: `Found ${articles.docs.length} articles`,
      count: articles.docs.length,
      testingEnabled: testConversion,
      summary,
      articles: results,
    });
  } catch (error) {
    console.error('❌ Failed to test existing articles:', error);

    return NextResponse.json(
      {
        error: 'Failed to test existing articles',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

async function _POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { articleId } = body;

    if (!articleId) {
      return NextResponse.json(
        { error: 'Article ID is required' },
        { status: 400 }
      );
    }

    console.log('🧪 Testing specific article:', articleId);

    // Get PayloadCMS instance
    const payload = await getPayload({ config });

    // Fetch the specific article
    const article = await payload.findByID({
      collection: 'articles',
      id: articleId,
    });

    if (!article.sourcesTab?.originalContent) {
      return NextResponse.json(
        { error: 'Article has no original content to test' },
        { status: 400 }
      );
    }

    // Test conversion
    const originalContentText = lexicalToText(
      article.sourcesTab.originalContent
    );
    const processor = ContentProcessorIntegration.getInstance();
    const result = await processor.processContent(originalContentText, 'html');

    console.log(`✅ Tested article ${articleId}:`, {
      strategy: result.strategy,
      success: result.metrics.success,
      time: result.metrics.processingTime,
    });

    return NextResponse.json({
      article: {
        id: article.id,
        title: article.title,
        originalContentLength: originalContentText.length,
      },
      conversionResult: result,
    });
  } catch (error) {
    console.error('❌ Failed to test specific article:', error);

    return NextResponse.json(
      {
        error: 'Failed to test article',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Export protected routes (authentication required for article testing)
export const GET = withAuth(_GET);
export const POST = withAuth(_POST);
