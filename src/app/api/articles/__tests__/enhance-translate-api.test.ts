import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { NextRequest } from 'next/server';

// Mock payload
const mockPayload = {
  findByID: vi.fn(),
  update: vi.fn(),
};

// Mock the payload config import
vi.mock('@payload-config', () => ({
  default: {},
}));

// Mock getPayload
vi.mock('payload', () => ({
  getPayload: vi.fn().mockResolvedValue(mockPayload),
}));

// Mock the utilities
vi.mock('../../../../lib/utils/html-to-lexical', () => ({
  htmlToLexical: vi.fn(),
}));

vi.mock('../../../../lib/utils/lexical', () => ({
  lexicalToText: vi.fn(),
  lexicalToHTML: vi.fn(),
}));

// Mock OpenAI integration
vi.mock('../../../../lib/integrations/openai/german-translation', () => ({
  translateToGerman: vi.fn(),
}));

// Import mocked functions
import { translateToGerman } from '../../../../lib/integrations/openai/german-translation';
import { htmlToLexical } from '../../../../lib/utils/html-to-lexical';
import { lexicalToHTML } from '../../../../lib/utils/lexical';

const mockTranslateToGerman = translateToGerman as any;
const mockHtmlToLexical = htmlToLexical as any;
const mockLexicalToHTML = lexicalToHTML as any;

describe('API Endpoints - Sprint 6: Comprehensive Testing', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Default successful responses
    mockHtmlToLexical.mockResolvedValue({
      result: {
        root: {
          children: [
            {
              type: 'paragraph',
              children: [{ type: 'text', text: 'Converted content' }],
            },
          ],
        },
      },
      metrics: { success: true },
    });

    mockLexicalToHTML.mockReturnValue('<p>HTML content</p>');
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('✅ POST /api/articles/translate', () => {
    // Dynamically import the route handler
    let translateHandler: any;

    beforeEach(async () => {
      const module = await import('../translate/route');
      translateHandler = module.POST;
    });

    it('should successfully translate a generated article', async () => {
      // Mock article data
      const mockArticle = {
        id: 'test-article-id',
        title: 'Test Article',
        articleType: 'generated',
        workflowStage: 'enhanced-draft',
        englishTab: {
          enhancedTitle: 'Enhanced title with enough characters for validation',
          enhancedSummary:
            'Enhanced summary with enough characters for validation requirements',
          enhancedContent: {
            root: {
              children: [
                {
                  type: 'paragraph',
                  children: [{ type: 'text', text: 'Enhanced content' }],
                },
              ],
            },
          },
          enhancedKeyInsights: [{ insight: 'Key insight 1' }],
          keywords: [{ keyword: 'keyword1' }],
        },
      };

      const mockTranslationResult = {
        success: true,
        data: {
          germanTitle: 'German Title',
          germanSummary: 'German Summary',
          germanContent: '<p>German content</p>',
          germanKeyInsights: ['German insight'],
          germanKeywords: ['deutsches_keyword'],
          quality: {
            linguisticAccuracy: 85,
            culturalAdaptation: 80,
          },
        },
        metrics: {
          processingTime: 2500,
        },
      };

      mockPayload.findByID.mockResolvedValue(mockArticle);
      mockTranslateToGerman.mockResolvedValue(mockTranslationResult);
      mockPayload.update.mockResolvedValue({
        ...mockArticle,
        hasGermanTranslation: true,
      });

      const request = new NextRequest(
        'http://localhost/api/articles/translate',
        {
          method: 'POST',
          body: JSON.stringify({ articleId: 'test-article-id' }),
          headers: { 'Content-Type': 'application/json' },
        }
      );

      const response = await translateHandler(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.message).toContain('translated successfully');
      expect(result.data.germanTab.germanTitle).toBe('German Title');
      expect(result.metrics.processingTime).toBe(2500);
    });

    it('should successfully translate a curated article', async () => {
      const mockArticle = {
        id: 'test-article-id',
        articleType: 'curated',
        workflowStage: 'curated-draft',
        englishTab: {
          enhancedTitle: 'Valid curated title with enough characters',
          enhancedSummary: 'Valid curated summary with enough characters',
          enhancedContent: {
            root: {
              children: [
                {
                  type: 'paragraph',
                  children: [{ type: 'text', text: 'Curated content' }],
                },
              ],
            },
          },
        },
      };

      const mockTranslationResult = {
        success: true,
        data: {
          germanTitle: 'German Curated Title',
          germanSummary: 'German Curated Summary',
          germanContent: '<p>German curated content</p>',
          germanKeyInsights: ['German insight'],
          germanKeywords: ['keyword'],
          quality: { linguisticAccuracy: 90, culturalAdaptation: 85 },
        },
        metrics: { processingTime: 3000 },
      };

      mockPayload.findByID.mockResolvedValue(mockArticle);
      mockTranslateToGerman.mockResolvedValue(mockTranslationResult);
      mockPayload.update.mockResolvedValue(mockArticle);

      const request = new NextRequest(
        'http://localhost/api/articles/translate',
        {
          method: 'POST',
          body: JSON.stringify({ articleId: 'test-article-id' }),
          headers: { 'Content-Type': 'application/json' },
        }
      );

      const response = await translateHandler(request);
      const result = await response.json();

      expect(result.success).toBe(true);
      expect(result.data.germanTab.germanTitle).toBe('German Curated Title');
    });

    it('should handle re-translation scenario', async () => {
      const mockArticle = {
        id: 'test-article-id',
        articleType: 'curated',
        workflowStage: 'translated',
        englishTab: {
          enhancedTitle: 'Enhanced title with enough characters',
          enhancedSummary: 'Enhanced summary with enough characters',
          enhancedContent: {
            root: {
              children: [
                {
                  type: 'paragraph',
                  children: [{ type: 'text', text: 'Content' }],
                },
              ],
            },
          },
        },
        germanTab: {
          germanTitle: 'Previous German Title', // Already has translation
        },
      };

      const mockTranslationResult = {
        success: true,
        data: {
          germanTitle: 'Updated German Title',
          germanSummary: 'Updated German Summary',
          germanContent: '<p>Updated German content</p>',
          germanKeyInsights: ['Updated insight'],
          germanKeywords: ['updated_keyword'],
          quality: { linguisticAccuracy: 92, culturalAdaptation: 88 },
        },
        metrics: { processingTime: 2800 },
      };

      mockPayload.findByID.mockResolvedValue(mockArticle);
      mockTranslateToGerman.mockResolvedValue(mockTranslationResult);
      mockPayload.update.mockResolvedValue(mockArticle);

      const request = new NextRequest(
        'http://localhost/api/articles/translate',
        {
          method: 'POST',
          body: JSON.stringify({ articleId: 'test-article-id' }),
          headers: { 'Content-Type': 'application/json' },
        }
      );

      const response = await translateHandler(request);
      const result = await response.json();

      expect(result.success).toBe(true);
      expect(result.message).toContain('re-translated successfully');
      expect(result.data.germanTab.germanTitle).toBe('Updated German Title');
    });

    it('should return 400 when article ID is missing', async () => {
      const request = new NextRequest(
        'http://localhost/api/articles/translate',
        {
          method: 'POST',
          body: JSON.stringify({}), // Missing articleId
          headers: { 'Content-Type': 'application/json' },
        }
      );

      const response = await translateHandler(request);
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Article ID is required');
    });

    it('should return 404 when article is not found', async () => {
      mockPayload.findByID.mockResolvedValue(null);

      const request = new NextRequest(
        'http://localhost/api/articles/translate',
        {
          method: 'POST',
          body: JSON.stringify({ articleId: 'non-existent-id' }),
          headers: { 'Content-Type': 'application/json' },
        }
      );

      const response = await translateHandler(request);
      const result = await response.json();

      expect(response.status).toBe(404);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Article not found');
    });

    it('should return 400 for ineligible article types', async () => {
      const mockArticle = {
        id: 'test-article-id',
        articleType: 'manual', // Invalid type
        workflowStage: 'draft',
      };

      mockPayload.findByID.mockResolvedValue(mockArticle);

      const request = new NextRequest(
        'http://localhost/api/articles/translate',
        {
          method: 'POST',
          body: JSON.stringify({ articleId: 'test-article-id' }),
          headers: { 'Content-Type': 'application/json' },
        }
      );

      const response = await translateHandler(request);
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.error).toContain(
        'Article type must be generated or curated'
      );
    });

    it('should return 400 when required English content is missing', async () => {
      const mockArticle = {
        id: 'test-article-id',
        articleType: 'curated',
        englishTab: {
          enhancedTitle: '', // Missing title
          enhancedSummary: '', // Missing summary
          enhancedContent: undefined, // Missing content
        },
      };

      mockPayload.findByID.mockResolvedValue(mockArticle);

      const request = new NextRequest(
        'http://localhost/api/articles/translate',
        {
          method: 'POST',
          body: JSON.stringify({ articleId: 'test-article-id' }),
          headers: { 'Content-Type': 'application/json' },
        }
      );

      const response = await translateHandler(request);
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.error).toContain(
        'Article must have content in the English Content tab'
      );
    });

    it('should handle translation service errors', async () => {
      const mockArticle = {
        id: 'test-article-id',
        articleType: 'curated',
        englishTab: {
          enhancedTitle: 'Valid title with enough characters',
          enhancedSummary: 'Valid summary with enough characters',
          enhancedContent: {
            root: {
              children: [
                {
                  type: 'paragraph',
                  children: [{ type: 'text', text: 'Content' }],
                },
              ],
            },
          },
        },
      };

      mockPayload.findByID.mockResolvedValue(mockArticle);
      mockTranslateToGerman.mockResolvedValue({
        success: false,
        error: 'Translation service unavailable',
      });

      const request = new NextRequest(
        'http://localhost/api/articles/translate',
        {
          method: 'POST',
          body: JSON.stringify({ articleId: 'test-article-id' }),
          headers: { 'Content-Type': 'application/json' },
        }
      );

      const response = await translateHandler(request);
      const result = await response.json();

      expect(response.status).toBe(500);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Translation service unavailable');
    });

    it('should handle HTML to Lexical conversion errors', async () => {
      const mockArticle = {
        id: 'test-article-id',
        articleType: 'curated',
        englishTab: {
          enhancedTitle: 'Valid title with enough characters',
          enhancedSummary: 'Valid summary with enough characters',
          enhancedContent: {
            root: {
              children: [
                {
                  type: 'paragraph',
                  children: [{ type: 'text', text: 'Content' }],
                },
              ],
            },
          },
        },
      };

      const mockTranslationResult = {
        success: true,
        data: {
          germanTitle: 'German Title',
          germanSummary: 'German Summary',
          germanContent: '<p>German content</p>',
          germanKeyInsights: ['Insight'],
          germanKeywords: ['keyword'],
        },
        metrics: { processingTime: 2000 },
      };

      mockPayload.findByID.mockResolvedValue(mockArticle);
      mockTranslateToGerman.mockResolvedValue(mockTranslationResult);
      mockHtmlToLexical.mockResolvedValue({
        result: null,
        metrics: { success: false, error: 'Conversion failed' },
      });

      const request = new NextRequest(
        'http://localhost/api/articles/translate',
        {
          method: 'POST',
          body: JSON.stringify({ articleId: 'test-article-id' }),
          headers: { 'Content-Type': 'application/json' },
        }
      );

      const response = await translateHandler(request);
      const result = await response.json();

      expect(response.status).toBe(500);
      expect(result.success).toBe(false);
      expect(result.error).toBe('HTML to Lexical conversion failed');
    });
  });

  describe('✅ API Response Format Standardization (Sprint 3)', () => {
    let translateHandler: any;

    beforeEach(async () => {
      const module = await import('../translate/route');
      translateHandler = module.POST;
    });

    it('should return standardized API response format', async () => {
      const mockArticle = {
        id: 'test-article-id',
        articleType: 'curated',
        workflowStage: 'curated-draft',
        englishTab: {
          enhancedTitle: 'Valid title with enough characters',
          enhancedSummary: 'Valid summary with enough characters',
          enhancedContent: {
            root: {
              children: [
                {
                  type: 'paragraph',
                  children: [{ type: 'text', text: 'Content' }],
                },
              ],
            },
          },
        },
      };

      const mockTranslationResult = {
        success: true,
        data: {
          germanTitle: 'German Title',
          germanSummary: 'German Summary',
          germanContent: '<p>German content</p>',
          germanKeyInsights: ['Insight'],
          germanKeywords: ['keyword'],
          quality: { linguisticAccuracy: 85, culturalAdaptation: 80 },
        },
        metrics: { processingTime: 2500 },
      };

      mockPayload.findByID.mockResolvedValue(mockArticle);
      mockTranslateToGerman.mockResolvedValue(mockTranslationResult);
      mockPayload.update.mockResolvedValue(mockArticle);

      const request = new NextRequest(
        'http://localhost/api/articles/translate',
        {
          method: 'POST',
          body: JSON.stringify({ articleId: 'test-article-id' }),
          headers: { 'Content-Type': 'application/json' },
        }
      );

      const response = await translateHandler(request);
      const result = await response.json();

      // Verify standardized response structure
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('message');
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('metrics');

      // Verify data structure matches form field structure
      expect(result.data).toHaveProperty('germanTab');
      expect(result.data.germanTab).toHaveProperty('germanTitle');
      expect(result.data.germanTab).toHaveProperty('germanSummary');
      expect(result.data.germanTab).toHaveProperty('germanContent');
      expect(result.data.germanTab).toHaveProperty('germanKeyInsights');
      expect(result.data.germanTab).toHaveProperty('germanKeywords');

      // Verify main field updates
      expect(result.data).toHaveProperty('workflowStage');
      expect(result.data).toHaveProperty('hasGermanTranslation', true);

      // Verify metrics
      expect(result.metrics).toHaveProperty('processingTime');
      expect(result.metrics).toHaveProperty('linguisticAccuracy');
      expect(result.metrics).toHaveProperty('culturalAdaptation');
    });

    it('should return standardized error response format', async () => {
      const request = new NextRequest(
        'http://localhost/api/articles/translate',
        {
          method: 'POST',
          body: JSON.stringify({ articleId: '' }), // Invalid ID
          headers: { 'Content-Type': 'application/json' },
        }
      );

      const response = await translateHandler(request);
      const result = await response.json();

      // Verify standardized error structure
      expect(result).toHaveProperty('success', false);
      expect(result).toHaveProperty('error');
      expect(typeof result.error).toBe('string');
    });
  });

  describe('✅ Workflow Stage Transitions', () => {
    let translateHandler: any;

    beforeEach(async () => {
      const module = await import('../translate/route');
      translateHandler = module.POST;
    });

    it('should update workflow stage from candidate-article to translated', async () => {
      const mockArticle = {
        id: 'test-article-id',
        articleType: 'generated',
        workflowStage: 'candidate-article', // Should transition to translated
        englishTab: {
          enhancedTitle: 'Enhanced title with enough characters',
          enhancedSummary: 'Enhanced summary with enough characters',
          enhancedContent: {
            root: {
              children: [
                {
                  type: 'paragraph',
                  children: [{ type: 'text', text: 'Content' }],
                },
              ],
            },
          },
        },
      };

      const mockTranslationResult = {
        success: true,
        data: {
          germanTitle: 'German Title',
          germanSummary: 'German Summary',
          germanContent: '<p>German content</p>',
          germanKeyInsights: ['Insight'],
          germanKeywords: ['keyword'],
        },
        metrics: { processingTime: 2000 },
      };

      mockPayload.findByID.mockResolvedValue(mockArticle);
      mockTranslateToGerman.mockResolvedValue(mockTranslationResult);
      mockPayload.update.mockResolvedValue(mockArticle);

      const request = new NextRequest(
        'http://localhost/api/articles/translate',
        {
          method: 'POST',
          body: JSON.stringify({ articleId: 'test-article-id' }),
          headers: { 'Content-Type': 'application/json' },
        }
      );

      const response = await translateHandler(request);
      const result = await response.json();

      expect(result.success).toBe(true);
      expect(result.data.workflowStage).toBe('translated');
    });

    it('should preserve existing workflow stage when not candidate-article', async () => {
      const mockArticle = {
        id: 'test-article-id',
        articleType: 'curated',
        workflowStage: 'enhanced-draft', // Should remain unchanged
        englishTab: {
          enhancedTitle: 'Enhanced title with enough characters',
          enhancedSummary: 'Enhanced summary with enough characters',
          enhancedContent: {
            root: {
              children: [
                {
                  type: 'paragraph',
                  children: [{ type: 'text', text: 'Content' }],
                },
              ],
            },
          },
        },
      };

      const mockTranslationResult = {
        success: true,
        data: {
          germanTitle: 'German Title',
          germanSummary: 'German Summary',
          germanContent: '<p>German content</p>',
          germanKeyInsights: ['Insight'],
          germanKeywords: ['keyword'],
        },
        metrics: { processingTime: 2000 },
      };

      mockPayload.findByID.mockResolvedValue(mockArticle);
      mockTranslateToGerman.mockResolvedValue(mockTranslationResult);
      mockPayload.update.mockResolvedValue(mockArticle);

      const request = new NextRequest(
        'http://localhost/api/articles/translate',
        {
          method: 'POST',
          body: JSON.stringify({ articleId: 'test-article-id' }),
          headers: { 'Content-Type': 'application/json' },
        }
      );

      const response = await translateHandler(request);
      const result = await response.json();

      expect(result.success).toBe(true);
      expect(result.data.workflowStage).toBe('enhanced-draft');
    });
  });
});
