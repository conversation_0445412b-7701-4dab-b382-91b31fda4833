/**
 * Test suite for category pagination API
 *
 * Tests the /api/categories/[slug]/articles endpoint
 * for proper pagination and tier-based filtering
 */

import { describe, it, expect, beforeAll } from 'vitest';

const API_BASE = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

describe('Category Pagination API', () => {
  // Test basic pagination
  it('should return paginated articles for a category', async () => {
    const response = await fetch(
      `${API_BASE}/api/categories/international/articles?page=1&limit=6`
    );
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toHaveProperty('articles');
    expect(data).toHaveProperty('pagination');
    expect(data).toHaveProperty('category');

    expect(Array.isArray(data.articles)).toBe(true);
    expect(data.pagination).toHaveProperty('currentPage');
    expect(data.pagination).toHaveProperty('totalPages');
    expect(data.pagination).toHaveProperty('hasNextPage');
    expect(data.pagination).toHaveProperty('hasPrevPage');
  });

  // Test tier filtering
  it('should filter articles by tier when tier parameter is provided', async () => {
    const response = await fetch(
      `${API_BASE}/api/categories/international/articles?tier=tier-1&limit=10`
    );
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.requestInfo.tier).toBe('tier-1');

    // All returned articles should be tier-1
    data.articles.forEach((article: any) => {
      expect(article.placement).toBe('tier-1');
    });
  });

  // Test tier statistics in load-more mode
  it('should return tier statistics when loadMore=true', async () => {
    const response = await fetch(
      `${API_BASE}/api/categories/international/articles?loadMore=true&limit=6`
    );
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toHaveProperty('tierStats');
    expect(data.tierStats).toHaveProperty('tier1');
    expect(data.tierStats).toHaveProperty('tier2');
    expect(data.tierStats).toHaveProperty('tier3');

    expect(typeof data.tierStats.tier1).toBe('number');
    expect(typeof data.tierStats.tier2).toBe('number');
    expect(typeof data.tierStats.tier3).toBe('number');
  });

  // Test 404 for non-existent category
  it('should return 404 for non-existent category', async () => {
    const response = await fetch(
      `${API_BASE}/api/categories/non-existent/articles`
    );
    const data = await response.json();

    expect(response.status).toBe(404);
    expect(data).toHaveProperty('error');
    expect(data.error).toBe('Category not found');
  });

  // Test pagination bounds
  it('should handle page numbers correctly', async () => {
    // Test page 1
    const page1Response = await fetch(
      `${API_BASE}/api/categories/international/articles?page=1&limit=3`
    );
    const page1Data = await page1Response.json();

    expect(page1Data.pagination.currentPage).toBe(1);
    expect(page1Data.pagination.hasPrevPage).toBe(false);

    // If there are multiple pages, test page 2
    if (page1Data.pagination.hasNextPage) {
      const page2Response = await fetch(
        `${API_BASE}/api/categories/international/articles?page=2&limit=3`
      );
      const page2Data = await page2Response.json();

      expect(page2Data.pagination.currentPage).toBe(2);
      expect(page2Data.pagination.hasPrevPage).toBe(true);
    }
  });
});

/**
 * Manual testing instructions:
 *
 * 1. Start the dev server: pnpm dev
 * 2. Visit: http://localhost:3000/categories/international
 * 3. Scroll to bottom to see tier-based layout
 * 4. Click "Load More" buttons to test progressive loading
 * 5. Check browser network tab for API calls
 * 6. Test direct API calls:
 *    - /api/categories/international/articles?page=1
 *    - /api/categories/international/articles?tier=tier-1
 *    - /api/categories/international/articles?loadMore=true
 */
