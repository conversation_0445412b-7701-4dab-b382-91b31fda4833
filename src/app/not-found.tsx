import Link from 'next/link';
import { TriangleAlert, Home, ArrowLeft } from 'lucide-react';
import { ThemeProvider } from '@/components/theme-provider';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
// Import the styles directly
import './(frontend)/global.css';

export default function GlobalNotFound() {
  return (
    <html lang="de" className="min-h-dvh" suppressHydrationWarning>
      <head>
        <meta name="robots" content="noindex" />
        <title>404 - Seite nicht gefunden | <PERSON><PERSON><PERSON> Blick</title>
      </head>
      <body className="min-h-dvh">
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <div className="min-h-dvh bg-background flex items-center justify-center p-4">
            <Card className="max-w-lg w-full">
              <CardHeader className="text-center">
                <div className="flex justify-center mb-4">
                  <TriangleAlert className="h-12 w-12 text-amber-500" />
                </div>
                <CardTitle className="text-xl font-serif font-normal text-foreground">
                  404 - Seite nicht gefunden
                </CardTitle>
                <CardDescription className="text-muted-foreground">
                  Die angeforderte Seite existiert nicht oder wurde entfernt.
                </CardDescription>
              </CardHeader>

              <CardContent className="flex flex-col gap-4">
                {/* Possible reasons */}
                <div className="text-center text-sm text-muted-foreground">
                  <p>Mögliche Gründe:</p>
                  <ul className="mt-2 text-xs space-y-1">
                    <li>• Die URL wurde falsch eingegeben</li>
                    <li>• Die Seite wurde verschoben oder gelöscht</li>
                    <li>• Der Link ist veraltet</li>
                  </ul>
                </div>

                {/* Action buttons */}
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button asChild className="flex-1 flex items-center gap-2">
                    <Link href="/">
                      <Home className="h-4 w-4" />
                      Zur Startseite
                    </Link>
                  </Button>

                  <Button
                    asChild
                    variant="outline"
                    className="flex-1 flex items-center gap-2"
                  >
                    <Link href="/categories/economics">
                      <ArrowLeft className="h-4 w-4" />
                      Zu den Nachrichten
                    </Link>
                  </Button>
                </div>

                {/* Popular categories */}
                <div className="text-center text-sm text-muted-foreground border-t pt-4">
                  <p className="mb-2">Beliebte Bereiche:</p>
                  <div className="flex flex-wrap gap-2 justify-center">
                    <Link
                      href="/categories/economics"
                      className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                    >
                      Economics
                    </Link>
                    <Link
                      href="/categories/technology"
                      className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                    >
                      Technology
                    </Link>
                    <Link
                      href="/categories/international"
                      className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                    >
                      International
                    </Link>
                    <Link
                      href="/categories/investment"
                      className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                    >
                      Investment
                    </Link>
                  </div>
                </div>

                {/* Additional help */}
                <div className="text-center text-xs text-muted-foreground">
                  <p>Schauen Sie später noch einmal vorbei</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}
