import Link from 'next/link';
import { TriangleAlert } from 'lucide-react';

export default function ArticleNotFound() {
  return (
    <div className="min-h-dvh bg-background flex items-center justify-center">
      <div className="max-w-[1440px] mx-auto px-4 py-12">
        <div className="text-center space-y-6">
          {/* Error Icon */}
          <div className="text-muted-foreground">
            <TriangleAlert className="size-12 mx-auto mb-4" />
          </div>

          {/* Error Message */}
          <div className="space-y-2">
            <h1 className="text-2xl md:text-3xl font-serif font-normal text-gray-900 dark:text-gray-100">
              Artikel nicht gefunden
            </h1>
            <p className="text-sm text-gray-600 dark:text-gray-400 font-sans">
              Der gesuchte Artikel existiert nicht oder wurde entfernt.
            </p>
          </div>

          {/* Action Button */}
          <div className="pt-4">
            <Link
              href="/"
              className="inline-flex items-center px-4 py-2 text-xs font-medium text-primary hover:text-primary/80 transition-colors"
            >
              Zurück zur Startseite
            </Link>
          </div>

          {/* Additional Info */}
          <div className="pt-6 text-xs text-muted-foreground">
            <p>Schauen Sie später noch einmal vorbei</p>
          </div>
        </div>
      </div>
    </div>
  );
}
