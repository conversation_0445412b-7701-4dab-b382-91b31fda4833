import { SpeedInsights } from '@vercel/speed-insights/next';
import type { Metadata } from 'next';
import { Merriweather, <PERSON><PERSON> } from 'next/font/google';
import './global.css';

import { ThemeProvider } from '@/components/theme-provider';
import HeaderNavigation from '@/components/navigation/header-navigation';
import FooterNavigation from '@/components/footer/FooterNavigation';
import { EnvironmentBanner } from '@/components/dev/EnvironmentBanner';

// Configure fonts with Next.js optimization
const merriweather = Merriweather({
  weight: ['300', '400', '700', '900'],
  style: ['normal', 'italic'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-merriweather',
});

const roboto = Roboto({
  weight: ['100', '300', '400', '500', '700', '900'],
  style: ['normal', 'italic'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-roboto',
});

export const metadata: Metadata = {
  title: '<PERSON><PERSON><PERSON>lick',
  description: 'Deutsche Finanz- und Wirtschaftsnachrichten',
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="de" className={`${merriweather.variable} ${roboto.variable}`}>
      <body>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
          storageKey="theme"
        >
          <div className="min-h-dvh flex flex-col bg-background">
            <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
              <div className="flex h-16 items-center">
                <HeaderNavigation />
              </div>
            </header>
            <main className="flex-1">{children}</main>
            <FooterNavigation />
          </div>
          <EnvironmentBanner />
        </ThemeProvider>
        <SpeedInsights />
      </body>
    </html>
  );
}
