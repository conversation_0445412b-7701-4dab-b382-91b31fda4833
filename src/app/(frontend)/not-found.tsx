import React from 'react';
import Link from 'next/link';
import { TriangleAlert, Home, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { getCachedAllCategories } from '@/lib/cache/categories';

export default async function GlobalNotFound() {
  // Fetch available categories dynamically
  const availableCategories = await getCachedAllCategories();
  // Limit to 4 popular categories
  const popularCategories = availableCategories.slice(0, 4);

  return (
    <div className="min-h-dvh bg-background flex items-center justify-center p-4">
      <Card className="max-w-lg w-full">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <TriangleAlert className="h-12 w-12 text-amber-500" />
          </div>
          <CardTitle className="text-xl font-serif font-normal text-foreground">
            404 - Seite nicht gefunden
          </CardTitle>
          <CardDescription className="text-muted-foreground">
            Die angeforderte Seite existiert nicht oder wurde entfernt.
          </CardDescription>
        </CardHeader>

        <CardContent className="flex flex-col gap-4">
          {/* Possible reasons */}
          <div className="text-center text-sm text-muted-foreground">
            <p>Mögliche Gründe:</p>
            <ul className="mt-2 text-xs space-y-1">
              <li>• Die URL wurde falsch eingegeben</li>
              <li>• Die Seite wurde verschoben oder gelöscht</li>
              <li>• Der Link ist veraltet</li>
            </ul>
          </div>

          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row gap-2">
            <Button asChild className="flex-1 flex items-center gap-2">
              <Link href="/">
                <Home className="h-4 w-4" />
                Zur Startseite
              </Link>
            </Button>

            <Button
              asChild
              variant="outline"
              className="flex-1 flex items-center gap-2"
            >
              <Link href="/">
                <ArrowLeft className="h-4 w-4" />
                Zurück
              </Link>
            </Button>
          </div>

          {/* Dynamic popular categories */}
          {popularCategories.length > 0 && (
            <div className="text-center text-sm text-muted-foreground border-t pt-4">
              <p className="mb-2">Beliebte Bereiche:</p>
              <div className="flex flex-wrap gap-2 justify-center">
                {popularCategories.map(category => (
                  <Link
                    key={category.id}
                    href={`/categories/${category.slug}`}
                    className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                  >
                    {category.title}
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* Additional help */}
          <div className="text-center text-xs text-muted-foreground">
            <p>Schauen Sie später noch einmal vorbei</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
