import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { ArrowLeft, Home, Search } from 'lucide-react';
import { getCachedAllCategories } from '@/lib/cache/categories';

export default async function CategoryNotFound() {
  // Fetch all available categories dynamically
  const categories = await getCachedAllCategories();

  return (
    <div className="min-h-dvh bg-background flex items-center justify-center p-4">
      <Card className="max-w-md w-full">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Search className="h-12 w-12 text-amber-500" />
          </div>
          <CardTitle className="text-xl font-serif font-normal text-foreground">
            Kategorie nicht gefunden
          </CardTitle>
          <CardDescription className="text-muted-foreground">
            Die angeforderte Kategorie existiert nicht oder wurde entfernt.
          </CardDescription>
        </CardHeader>

        <CardContent className="flex flex-col gap-4">
          <div className="text-center text-sm text-muted-foreground">
            <p>Mögliche Gründe:</p>
            <ul className="mt-2 text-xs space-y-1">
              <li>• Die Kategorie wurde umbenannt</li>
              <li>• Der Link ist veraltet</li>
              <li>• Es gab einen Tippfehler in der URL</li>
            </ul>
          </div>

          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              asChild
              className="flex-1 flex items-center gap-2"
              variant="default"
            >
              <Link href="/">
                <Home className="h-4 w-4" />
                Zur Startseite
              </Link>
            </Button>

            <Button
              asChild
              className="flex-1 flex items-center gap-2"
              variant="outline"
            >
              <Link href="/">
                <ArrowLeft className="h-4 w-4" />
                Zurück
              </Link>
            </Button>
          </div>

          {/* Dynamically generated category links */}
          {categories.length > 0 && (
            <div className="text-center text-sm text-muted-foreground border-t pt-4">
              <p className="mb-2">Verfügbare Kategorien:</p>
              <div className="flex flex-wrap gap-2 justify-center">
                {categories.map(category => (
                  <Link
                    key={category.id}
                    href={`/categories/${category.slug}`}
                    className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded hover:bg-gray-200 dark:hover:bg-gray-700"
                  >
                    {category.title}
                  </Link>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
