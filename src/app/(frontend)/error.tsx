'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { AlertCircle, RefreshCw, Home } from 'lucide-react';

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function ErrorPage({ error, reset }: ErrorPageProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Homepage error:', error);
  }, [error]);

  // Check if it's a network/API error
  const isNetworkError =
    error.message.includes('fetch') ||
    error.message.includes('network') ||
    error.message.includes('ECONNREFUSED');

  // Check if it's a PayloadCMS error
  const isPayloadError =
    error.message.includes('payload') ||
    error.message.includes('collection') ||
    error.stack?.includes('payload');

  const getErrorTitle = () => {
    if (isNetworkError) return 'Connection Error';
    if (isPayloadError) return 'Content Loading Error';
    return 'Something went wrong';
  };

  const getErrorDescription = () => {
    if (isNetworkError) {
      return 'Unable to connect to our servers. Please check your internet connection and try again.';
    }
    if (isPayloadError) {
      return "We're having trouble loading the latest articles. Our team has been notified.";
    }
    return 'An unexpected error occurred while loading the page. Please try again.';
  };

  const getErrorIcon = () => {
    if (isNetworkError)
      return <RefreshCw className="h-12 w-12 text-amber-500" />;
    return <AlertCircle className="h-12 w-12 text-red-500" />;
  };

  return (
    <div className="min-h-dvh bg-background flex items-center justify-center p-4">
      <Card className="max-w-md w-full">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">{getErrorIcon()}</div>
          <CardTitle className="text-xl font-semibold text-foreground">
            {getErrorTitle()}
          </CardTitle>
          <CardDescription className="text-muted-foreground">
            {getErrorDescription()}
          </CardDescription>
        </CardHeader>

        <CardContent className="flex flex-col gap-4">
          {/* Error details for development */}
          {process.env.NODE_ENV === 'development' && (
            <details className="bg-gray-50 dark:bg-gray-900 p-3 rounded-md text-sm">
              <summary className="cursor-pointer font-medium text-gray-700 dark:text-gray-300 mb-2">
                Error Details (Development)
              </summary>
              <pre className="text-xs text-gray-600 dark:text-gray-400 overflow-auto">
                {error.message}
                {error.stack && '\n\nStack trace:\n' + error.stack}
              </pre>
            </details>
          )}

          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              onClick={reset}
              className="flex-1 flex items-center gap-2"
              variant="default"
            >
              <RefreshCw className="h-4 w-4" />
              Try Again
            </Button>

            <Button
              onClick={() => (window.location.href = '/')}
              className="flex-1 flex items-center gap-2"
              variant="outline"
            >
              <Home className="h-4 w-4" />
              Go Home
            </Button>
          </div>

          {/* Additional help */}
          <div className="text-center text-sm text-muted-foreground">
            <p>If the problem persists, please contact our support team.</p>
            {error.digest && (
              <p className="mt-1 font-mono text-xs">Error ID: {error.digest}</p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
