import type { Metada<PERSON> } from 'next';
import { Merriwe<PERSON>, <PERSON><PERSON> } from 'next/font/google';

export const metadata: Metadata = {
  metadataBase: new URL(
    process.env.NODE_ENV === 'production'
      ? 'https://borsen-blick.com'
      : 'http://localhost:3001'
  ),
  title: {
    template: '%s | <PERSON><PERSON><PERSON>lick',
    default: '<PERSON><PERSON>rsen Blick',
  },
  description: 'Latest financial news and market analysis in German',
};

const merriweather = Merriweather({
  subsets: ['latin'],
  weight: ['300', '400', '700'],
  variable: '--font-merriweather',
});

const roboto = Roboto({
  subsets: ['latin'],
  weight: ['300', '400', '500', '700'],
  variable: '--font-roboto',
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${merriweather.variable} ${roboto.variable}`}>
      <body>{children}</body>
    </html>
  );
}
