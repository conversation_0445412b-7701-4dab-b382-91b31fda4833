import type {
  CollectionAfterChangeHook,
  CollectionAfterDeleteHook,
} from 'payload';
import { invalidatePageCaches } from '../../../lib/cache/invalidation';

export const revalidatePage: CollectionAfterChangeHook = async ({
  doc,
  previousDoc,
  req: { payload, context },
}) => {
  if (!context?.disableRevalidate) {
    // For published pages, revalidate the path
    if (doc._status === 'published') {
      payload.logger.info(`Revalidating page at path: ${doc.slug}`);

      await invalidatePageCaches(doc.slug, {
        revalidatePaths: true,
      });
    }

    // If the page was previously published but now isn't, revalidate the old path
    if (previousDoc?._status === 'published' && doc._status !== 'published') {
      payload.logger.info(`Revalidating old page at path: ${previousDoc.slug}`);

      await invalidatePageCaches(previousDoc.slug, {
        revalidatePaths: true,
      });
    }
  }

  return doc;
};

export const revalidateDelete: CollectionAfterDeleteHook = async ({
  doc,
  req: { payload, context },
}) => {
  if (!context?.disableRevalidate && doc?.slug) {
    payload.logger.info(`Revalidating deleted page at path: ${doc.slug}`);

    await invalidatePageCaches(doc.slug, {
      revalidatePaths: true,
    });
  }

  return doc;
};
