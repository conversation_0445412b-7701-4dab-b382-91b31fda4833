import type { CollectionConfig } from 'payload';

const ProcessedUrls: CollectionConfig = {
  slug: 'processed-urls',
  admin: {
    useAsTitle: 'url',
    defaultColumns: ['url', 'status', 'publicationDate', 'processedAt'],
    group: 'Configuration',
    description: 'Tracks URLs processed from RSS feeds to prevent duplicates',
  },
  access: {
    read: ({ req }) => !!req.user, // Authenticated users can read
    create: ({ req }) => !!req.user, // Authenticated users can create
    update: ({ req }) => !!req.user, // Authenticated users can update
    delete: ({ req }) => !!req.user, // Authenticated users can delete
  },
  fields: [
    {
      name: 'url',
      label: 'URL',
      type: 'text',
      required: true,
      unique: true,
      index: true,
      admin: {
        readOnly: true,
        description: 'The article URL that was processed',
      },
    },
    {
      name: 'status',
      label: 'Processing Status',
      type: 'select',
      required: true,
      options: [
        { label: 'Pending', value: 'pending' },
        { label: 'Accepted', value: 'accepted' },
        { label: 'Rejected', value: 'rejected' },
        { label: 'Error', value: 'error' },
      ],
      defaultValue: 'pending',
      admin: {
        readOnly: true,
        description: 'Current processing status of this URL',
      },
    },
    {
      name: 'title',
      label: 'Article Title',
      type: 'text',
      admin: {
        readOnly: true,
        description: 'Title of the article from RSS feed',
      },
    },
    {
      name: 'publicationDate',
      label: 'Publication Date',
      type: 'date',
      admin: {
        readOnly: true,
        date: {
          pickerAppearance: 'dayAndTime',
        },
        description:
          'When the article was originally published (from RSS feed)',
      },
    },
    {
      name: 'feedId',
      label: 'Source RSS Feed',
      type: 'relationship',
      relationTo: 'rss-feeds',
      required: false, // Allow null when RSS feed is deleted
      admin: {
        readOnly: true,
        description:
          'RSS feed that provided this URL (null if feed was deleted)',
      },
    },
    {
      name: 'processedAt',
      label: 'Processed At',
      type: 'date',
      admin: {
        readOnly: true,
        date: {
          pickerAppearance: 'dayAndTime',
        },
        description: 'When this URL was last processed',
      },
    },

    {
      name: 'reason',
      label: 'Processing Reason / Error Message',
      type: 'textarea',
      admin: {
        readOnly: true,
        description: 'Details about processing result or error',
      },
    },
    {
      name: 'articleId',
      label: 'Related Article',
      type: 'relationship',
      relationTo: 'articles',
      admin: {
        readOnly: true,
        condition: ({ status }) => status === 'accepted',
        description: 'The article created from this URL (if accepted)',
      },
    },
    {
      name: 'attemptCount',
      label: 'Processing Attempts',
      type: 'number',
      defaultValue: 0,
      admin: {
        readOnly: true,
        description: 'Number of times processing was attempted',
      },
    },
    {
      name: 'lastAttemptAt',
      label: 'Last Attempt At',
      type: 'date',
      admin: {
        readOnly: true,
        date: {
          pickerAppearance: 'dayAndTime',
        },
        description: 'When the last processing attempt was made',
      },
    },
  ],
  timestamps: true,
};

export default ProcessedUrls;
