import type { CollectionConfig } from 'payload';

import { authenticated } from '../access/authenticated';

export const Users: CollectionConfig = {
  slug: 'users',
  access: {
    admin: authenticated,
    create: async args => {
      const { req } = args;
      // Allow creation if no users exist (first user setup)
      if (!req.user) {
        try {
          const users = await req.payload.find({
            collection: 'users',
            limit: 1,
          });
          return users.totalDocs === 0; // Allow if no users exist
        } catch (error) {
          return true; // Allow on error (likely no users table yet)
        }
      }
      return authenticated(args); // Pass full args to authenticated function
    },
    delete: authenticated,
    read: authenticated,
    update: authenticated,
  },
  admin: {
    defaultColumns: ['name', 'email'],
    useAsTitle: 'name',
    group: 'Configuration',
  },
  auth: {
    // Session security configuration
    tokenExpiration: 7200, // 2 hours (reasonable for admin sessions)
    maxLoginAttempts: 5, // Prevent brute force attacks
    lockTime: 600000, // 10 minutes lockout after failed attempts
    useAPIKey: false, // Disable API key auth for security
    cookies: {
      sameSite: 'Strict', // CSRF protection
      secure: process.env.NODE_ENV === 'production', // HTTPS only in production
    },
  },
  fields: [
    {
      name: 'name',
      type: 'text',
    },
  ],
  timestamps: true,
};
