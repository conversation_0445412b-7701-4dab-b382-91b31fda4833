import type { CollectionConfig } from 'payload';

const RSSFeeds: CollectionConfig = {
  slug: 'rss-feeds',
  admin: {
    useAsTitle: 'name',
    defaultColumns: [
      'name',
      'url',
      'isActive',
      'priority',
      'language',
      'lastChecked',
      'itemsAccepted',
    ],
    group: 'Configuration',
    description: 'Manage RSS feed sources for content ingestion',
  },
  access: {
    read: () => true, // Allow all authenticated users to read if needed
    create: ({ req }) => !!req.user, // Authenticated users can create
    update: ({ req }) => !!req.user, // Authenticated users can update
    delete: ({ req }) => !!req.user, // Authenticated users can delete
  },
  fields: [
    {
      name: 'name',
      label: 'Feed Name',
      type: 'text',
      required: true,
      admin: {
        description:
          'A descriptive name for this RSS feed (e.g., "Finanzen.net News")',
      },
    },
    {
      name: 'url',
      label: 'RSS Feed URL',
      type: 'text',
      required: true,
      unique: true,
      admin: {
        description: 'The full URL of the RSS feed',
      },
    },
    {
      name: 'isActive',
      label: 'Is Active?',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description:
          'If checked, this feed will be processed. Uncheck to temporarily disable',
      },
    },
    {
      name: 'language',
      label: 'Language',
      type: 'select',
      options: [
        { label: 'German', value: 'de' },
        { label: 'English', value: 'en' },
      ],
      defaultValue: 'de',
      admin: {
        description: 'Primary language of this feed',
      },
    },
    {
      name: 'lastProcessed',
      label: 'Last Processed',
      type: 'date',
      admin: {
        readOnly: true,
        date: {
          pickerAppearance: 'dayAndTime',
        },
        description: 'Timestamp of the last time this feed was processed',
      },
    },
    {
      name: 'lastChecked',
      label: 'Last Checked',
      type: 'date',
      admin: {
        readOnly: true,
        date: {
          pickerAppearance: 'dayAndTime',
        },
        description:
          'Timestamp of the last time the system attempted to process this feed',
      },
    },
    {
      name: 'lastSuccessfulCheck',
      label: 'Last Successful Check',
      type: 'date',
      admin: {
        readOnly: true,
        date: {
          pickerAppearance: 'dayAndTime',
        },
        description:
          'Timestamp of the last time the system successfully processed this feed and found articles',
      },
    },
    {
      name: 'itemsProcessed',
      label: 'Items Processed',
      type: 'number',
      defaultValue: 0,
      admin: {
        readOnly: true,
        description: 'Total number of items processed from this feed',
      },
    },
    {
      name: 'itemsAccepted',
      label: 'Items Accepted',
      type: 'number',
      defaultValue: 0,
      admin: {
        readOnly: true,
        description: 'Total number of items accepted from this feed',
      },
    },
    {
      name: 'articlesFoundSinceLastSuccessful',
      label: 'Articles Found (Last Check)',
      type: 'number',
      admin: {
        readOnly: true,
        description:
          'Number of new articles found during the last successful check',
      },
    },
    {
      name: 'totalArticlesAccepted',
      label: 'Total Articles Accepted',
      type: 'number',
      admin: {
        readOnly: true,
        description:
          'Total number of articles from this feed that have been accepted into the system',
      },
    },
    {
      name: 'errorCount',
      label: 'Consecutive Error Count',
      type: 'number',
      defaultValue: 0,
      admin: {
        readOnly: true,
        description:
          'Number of consecutive times processing this feed has failed. Resets on success',
      },
    },
    {
      name: 'lastErrorMessage',
      label: 'Last Error Message',
      type: 'textarea',
      admin: {
        readOnly: true,
        condition: ({ errorCount }) => errorCount > 0,
        description:
          'Details of the last error encountered while processing this feed',
      },
    },
    {
      name: 'processingFrequency',
      label: 'Processing Frequency (minutes)',
      type: 'number',
      defaultValue: 60,
      min: 5,
      max: 1440, // Max 24 hours
      admin: {
        description:
          'How often this feed should be checked for new articles (in minutes)',
      },
    },
    {
      name: 'priority',
      label: 'Priority',
      type: 'select',
      options: [
        { label: 'Low', value: 'low' },
        { label: 'Medium', value: 'medium' },
        { label: 'High', value: 'high' },
      ],
      defaultValue: 'medium',
      admin: {
        description: 'Processing priority for this feed',
      },
    },

    // Enhanced Processing Settings
    {
      name: 'firecrawlOptions',
      label: 'Firecrawl Extraction Settings',
      type: 'group',
      admin: {
        description:
          'Override global Firecrawl settings for this specific feed',
      },
      fields: [
        {
          name: 'removeBase64Images',
          label: 'Remove Base64 Images',
          type: 'checkbox',
          defaultValue: true,
          admin: {
            description:
              'Remove base64 images to reduce content size (leave unchecked to use global default)',
          },
        },
        {
          name: 'blockAds',
          label: 'Block Advertisements',
          type: 'checkbox',
          defaultValue: true,
          admin: {
            description:
              'Enable ad-blocking and cookie popup blocking (leave unchecked to use global default)',
          },
        },
        {
          name: 'excludeTags',
          label: 'Exclude HTML Tags',
          type: 'array',
          admin: {
            description:
              'HTML tags/selectors to exclude from extraction (e.g., ".advertisement", "#paywall")',
          },
          defaultValue: [
            { tag: '.advertisement' },
            { tag: '.ad-banner' },
            { tag: '.consent-banner' },
            { tag: '#usercentrics-root' },
            { tag: '.cookie-banner' },
            { tag: '.sidebar' },
            { tag: '.comments' },
            { tag: '.social-share' },
          ],
          fields: [
            {
              name: 'tag',
              label: 'Tag/Selector',
              type: 'text',
              required: true,
              admin: {
                placeholder: 'e.g., .advertisement, #paywall, .cookie-banner',
              },
            },
          ],
        },
        {
          name: 'includeTags',
          label: 'Include Only These Tags',
          type: 'array',
          admin: {
            description:
              'If specified, only extract content from these HTML tags/selectors',
          },
          fields: [
            {
              name: 'tag',
              label: 'Tag/Selector',
              type: 'text',
              required: true,
              admin: {
                placeholder: 'e.g., article, .main-content, .article-body',
              },
            },
          ],
        },
      ],
    },

    // Keyword Filtering Settings
    {
      name: 'keywordFiltering',
      label: 'Keyword Filtering Settings',
      type: 'group',
      admin: {
        description: 'Custom keyword filtering rules for this feed',
      },
      fields: [
        {
          name: 'strictKeywordMatching',
          label: 'Strict Keyword Matching',
          type: 'checkbox',
          admin: {
            description:
              'Require exact keyword matches (more restrictive filtering)',
          },
        },
        {
          name: 'customKeywords',
          label: 'Feed-Specific Keywords',
          type: 'array',
          admin: {
            description:
              'Additional keywords specific to this feed (supplements global keywords)',
          },
          fields: [
            {
              name: 'keyword',
              label: 'Keyword (German)',
              type: 'text',
              required: true,
              admin: {
                placeholder: 'e.g., Börse, Aktien, DAX',
              },
            },
            {
              name: 'englishKeyword',
              label: 'English Translation',
              type: 'text',
              required: true,
              admin: {
                placeholder: 'e.g., Stock Exchange, Stocks, DAX',
              },
            },
            {
              name: 'weight',
              label: 'Keyword Weight',
              type: 'number',
              min: 1,
              max: 10,
              defaultValue: 5,
              admin: {
                description: 'Importance of this keyword (1=low, 10=high)',
              },
            },
          ],
        },
      ],
    },

    // Processing Behavior Settings
    {
      name: 'processingOptions',
      label: 'Processing Behavior',
      type: 'group',
      admin: {
        description: 'Control how articles from this feed are processed',
      },
      fields: [
        {
          name: 'maxFirecrawlScrape',
          label: 'Max Firecrawl Scrapes Per Run',
          type: 'number',
          min: 1,
          max: 100,
          admin: {
            description:
              'Maximum number of articles to scrape with Firecrawl per run (after keyword filtering)',
            placeholder: 'e.g., 15',
          },
        },
        {
          name: 'maxArticlesPerRun',
          label: 'Max Articles Sent to OpenAI',
          type: 'number',
          min: 1,
          max: 50,
          admin: {
            description:
              'Maximum number of successfully scraped articles to send to OpenAI for processing',
            placeholder: 'e.g., 10',
          },
        },
        {
          name: 'skipTranslation',
          label: 'Skip OpenAI Translation',
          type: 'checkbox',
          admin: {
            description:
              'Skip automatic translation for articles from this feed',
          },
        },
        {
          name: 'skipEnhancement',
          label: 'Skip AI Enhancement',
          type: 'checkbox',
          admin: {
            description:
              'Skip AI content enhancement for articles from this feed',
          },
        },
        {
          name: 'customTimeout',
          label: 'Custom Extraction Timeout (seconds)',
          type: 'number',
          min: 10,
          max: 300,
          admin: {
            description:
              'Override default timeout for content extraction (leave empty to use site defaults)',
            placeholder: 'e.g., 60',
          },
        },
        {
          name: 'enableStealth',
          label: 'Force Stealth Mode',
          type: 'checkbox',
          admin: {
            description:
              'Always use stealth mode for this feed (useful for sites with anti-bot protection)',
          },
        },
      ],
    },
  ],
  timestamps: true,
  hooks: {
    beforeDelete: [
      async ({ req, id }) => {
        // Delete all related records before deleting the RSS feed
        try {
          const payload = req.payload;

          // Find all ProcessedUrls that reference this RSS feed
          const relatedUrls = await payload.find({
            collection: 'processed-urls',
            where: {
              feedId: {
                equals: id,
              },
            },
            limit: 1000, // Adjust if you expect more than 1000 URLs per feed
          });

          // Update related ProcessedUrls to remove the RSS feed reference (preserve audit log)
          if (relatedUrls.docs.length > 0) {
            console.log(
              `🔗 Updating ${relatedUrls.docs.length} related ProcessedUrls to remove RSS feed reference`
            );

            for (const urlDoc of relatedUrls.docs) {
              await payload.update({
                collection: 'processed-urls',
                id: urlDoc.id,
                data: {
                  feedId: null, // Remove the reference but keep the audit log
                },
              });
            }

            console.log(
              `✅ Successfully updated ${relatedUrls.docs.length} ProcessedUrls (removed RSS feed reference)`
            );
          }

          console.log(
            `🎯 RSS feed ${id} is ready for deletion (updated ${relatedUrls.docs.length} ProcessedUrls to remove feed references)`
          );
        } catch (error: any) {
          console.error('❌ Error deleting related records:', error);
          throw new Error(`Failed to delete related records: ${error.message}`);
        }
      },
    ],
  },
};

export default RSSFeeds;
