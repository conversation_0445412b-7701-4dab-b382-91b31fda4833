import type { CollectionConfig } from 'payload';
import { authenticated } from '../access/authenticated';

export const Media: CollectionConfig = {
  slug: 'media',
  access: {
    create: authenticated, // Only authenticated users can upload
    delete: authenticated, // Only authenticated users can delete
    read: () => true, // Public read for serving images
    update: authenticated, // Only authenticated users can update
  },
  admin: {
    group: 'Media',
    description: 'Upload and manage images for articles',
  },
  upload: {
    staticDir: 'media',
    imageSizes: [
      // Admin thumbnail - small preview for CMS
      {
        name: 'thumbnail',
        width: 400,
        height: 225, // 16:9 aspect ratio
        position: 'centre',
      },
      // Standard cards (NewsCard default variant) - covers up to ~507px display at 2x DPR
      {
        name: 'card',
        width: 1024,
        height: 576, // 16:9 aspect ratio
        position: 'centre',
      },
      // Feature images - large hero images
      {
        name: 'feature',
        width: 1456,
        height: 816, // Exact match for Midjourney --ar 16:9 output
        position: 'centre',
      },
      // TODO: Add horizontal, social, hero sizes after proper database migration
    ],
    adminThumbnail: 'thumbnail',
    // Enhanced security for file uploads
    mimeTypes: [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/webp',
      'image/svg+xml', // For icons/graphics
    ],
    // File size limits (10MB max - reasonable for news images)
    focalPoint: false,
    crop: false,
    // Add file size validation in beforeValidate hook
  },
  hooks: {
    beforeValidate: [
      ({ data, req }) => {
        // File size validation (10MB limit)
        const maxSize = 10 * 1024 * 1024; // 10MB in bytes

        if (req.file && req.file.size > maxSize) {
          throw new Error('File size too large. Maximum size is 10MB.');
        }

        // File name sanitization
        if (req.file && req.file.name) {
          // Remove potentially dangerous characters
          const sanitizedName = req.file.name
            .replace(/[^a-zA-Z0-9.-]/g, '_')
            .replace(/_{2,}/g, '_')
            .toLowerCase();

          req.file.name = sanitizedName;
        }

        return data;
      },
    ],
  },
  fields: [
    {
      name: 'alt',
      type: 'text',
      required: true,
      admin: {
        description: 'Alternative text for accessibility and SEO',
      },
    },
  ],
};
