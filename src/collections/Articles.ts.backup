import type { CollectionConfig } from 'payload';
import { anyone } from '../access/anyone';
import { authenticated } from '../access/authenticated';
import {
  MetaTitleField,
  MetaDescriptionField,
  MetaImageField,
  PreviewField,
  OverviewField,
} from '@payloadcms/plugin-seo/fields';
import { authenticatedOrPublished } from '../access/authenticatedOrPublished';
import { defaultLexical } from '../fields/defaultLexical';
import {
  prepareLexicalForReading,
  prepareLexicalForStorage,
} from '../lib/utils/lexical-validation';
import { formatSlug } from '../fields/slug/formatSlug';

export const Articles: CollectionConfig = {
  slug: 'articles',
  access: {
    create: authenticated,
    delete: authenticated,
    read: authenticatedOrPublished,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'workflowStage', 'articleType', 'updatedAt'],
    group: 'Content',
  },
  fields: [
    // FIXED: Simplified title logic using beforeValidate
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description:
          'Article title - auto-populated from German or English content when available',
      },
      hooks: {
        beforeValidate: [
          ({ value, data }) => {
            try {
              // Only auto-populate if no title provided
              if (!value || (typeof value === 'string' && !value.trim())) {
                // Priority: German title > English title > existing value
                return (
                  data?.germanTab?.germanTitle ||
                  data?.englishTab?.enhancedTitle ||
                  value
                );
              }
              return value;
            } catch (error) {
              console.error('Error in title hook:', error);
              return value; // Fallback to existing value
            }
          },
        ],
      },
    },
    // Slug field - moved to main area under title
    {
      name: 'slug',
      type: 'text',
      admin: {
        description: 'URL-friendly version of the title',
      },
      hooks: {
        beforeValidate: [
          ({ value, data, operation }) => {
            try {
              // On create: Generate slug from enhanced English title or main title (no more pending slugs)
              if (!value && operation === 'create') {
                const titleSource =
                  data?.englishTab?.enhancedTitle || data?.title;

                if (titleSource) {
                  return formatSlug(titleSource);
                }
              }

              // On update: Switch to German slug when translation is added
              if (
                operation === 'update' &&
                data?.germanTab?.germanTitle &&
                data?.hasGermanTranslation
              ) {
                return formatSlug(data.germanTab.germanTitle);
              }

              // On update: Update to English enhanced title if it becomes available and no German exists
              if (
                operation === 'update' &&
                data?.englishTab?.enhancedTitle &&
                !data?.hasGermanTranslation &&
                !value
              ) {
                return formatSlug(data.englishTab.enhancedTitle);
              }

              return value;
            } catch (error) {
              console.error('Error in slug generation:', error);
              return value; // Fallback to existing value
            }
          },
        ],
      },
    },
    // Featured Image field
    {
      name: 'featuredImage',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Featured image for the article',
      },
      filterOptions: {
        mimeType: { contains: 'image' },
      },
    },

    // Translate button at the very top of sidebar
    {
      type: 'ui',
      name: 'documentControls',
      admin: {
        position: 'sidebar',
        components: {
          Field:
            '@/components/admin/article-actions/DocumentControls#ArticleDocumentControls',
        },
      },
    },
    // 1. Article Type at top of sidebar
    {
      name: 'articleType',
      type: 'select',
      required: true,
      defaultValue: 'generated',
      options: [
        { label: 'Generated (from RSS)', value: 'generated' },
        { label: 'Curated (manual)', value: 'curated' },
      ],
      admin: {
        position: 'sidebar',
        description: 'Type of article - determines source and workflow',
      },
    },
    // 2. Workflow Stage
    {
      name: 'workflowStage',
      type: 'select',
      required: true,
      defaultValue: 'candidate-article',
      options: [
        { label: 'Candidate Article', value: 'candidate-article' },
        { label: 'Translated', value: 'translated' },
        { label: 'Ready for Review', value: 'ready-for-review' },
        { label: 'Published', value: 'published' },
      ],
      admin: {
        position: 'sidebar',
        description:
          'Current stage in the article workflow and publication status',
      },
    },
    // 3. Categories
    {
      name: 'categories',
      type: 'relationship',
      relationTo: 'categories',
      hasMany: true,
      admin: {
        position: 'sidebar',
        description: 'Article categories for organization and filtering',
      },
    },
    // 4. Placement
    {
      name: 'placement',
      type: 'select',
      options: [
        { label: 'Tier 1', value: 'tier-1' },
        { label: 'Tier 2', value: 'tier-2' },
        { label: 'Tier 3', value: 'tier-3' },
      ],
      admin: {
        position: 'sidebar',
        description: 'Article placement tier for prioritisation',
      },
    },
    // 5. Pinned
    {
      name: 'pinned',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        position: 'sidebar',
        description: 'Pin this article for featured placement',
      },
    },
    // 6. Matched Keywords (under categories)
    {
      name: 'keywordsMatched',
      label: 'Matched Keywords',
      type: 'relationship',
      relationTo: 'keywords',
      hasMany: true,
      admin: {
        position: 'sidebar',
        readOnly: true,
        isSortable: false,
        description:
          'Keywords that matched during RSS processing and triggered article acceptance',
      },
    },
    // 7. Related Companies
    {
      name: 'relatedCompanies',
      label: 'Related Companies',
      type: 'array',
      fields: [
        {
          name: 'name',
          label: 'Company Name',
          type: 'text',
          required: true,
        },
        {
          name: 'ticker',
          label: 'Ticker Symbol',
          type: 'text',
          admin: {
            description: 'Stock ticker symbol (e.g., AAPL, MSFT)',
          },
        },
        {
          name: 'exchange',
          label: 'Exchange Symbol',
          type: 'text',
          admin: {
            description: 'Stock exchange code (e.g., NYSE, NASDAQ, LSE)',
          },
        },
        {
          name: 'relevance',
          label: 'Relevance Level',
          type: 'select',
          options: [
            { label: 'High', value: 'high' },
            { label: 'Medium', value: 'medium' },
            { label: 'Low', value: 'low' },
          ],
          defaultValue: 'medium',
        },
        {
          name: 'confidence',
          label: 'Confidence Score',
          type: 'number',
          min: 0,
          max: 100,
          defaultValue: 100,
          admin: {
            description: 'AI confidence score (0-100)',
          },
        },
      ],
      admin: {
        position: 'sidebar',
        description:
          'Companies extracted from the article content by AI processing',
      },
    },
    // Publishing metadata
    {
      name: 'publishedBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        position: 'sidebar',
        description: 'User who published this article',
      },
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        position: 'sidebar',
        date: {
          pickerAppearance: 'dayAndTime',
        },
        description: 'When this article was published',
      },
    },
    // German translation flag at bottom of sidebar
    {
      name: 'hasGermanTranslation',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        position: 'sidebar',
        readOnly: true,
        description: 'Indicates if German translation is available',
      },
    },
    // Tab structure
    {
      type: 'tabs',
      tabs: [
        {
          label: 'English Content',
          name: 'englishTab',
          fields: [
            {
              name: 'enhancedTitle',
              label: 'Enhanced English Title',
              type: 'text',
              admin: {
                description:
                  'AI-enhanced English title optimised for international markets',
              },
            },
            {
              name: 'enhancedSummary',
              label: 'Enhanced English Summary',
              type: 'textarea',
              admin: {
                description: 'AI-enhanced English summary (100-150 characters)',
              },
            },
            {
              type: 'collapsible',
              label: 'Enhanced English Content',
              admin: {
                initCollapsed: true,
                description:
                  'AI-enhanced English content optimised for international markets (600-750 words)',
              },
              fields: [
                // FIXED: Optimized Lexical field hooks
                {
                  name: 'enhancedContent',
                  label: 'Content',
                  type: 'richText',
                  editor: defaultLexical,
                  admin: {
                    description:
                      'Enhanced English content for international markets',
                  },
                  hooks: {
                    afterRead: [
                      ({ value }) => {
                        try {
                          return prepareLexicalForReading(value);
                        } catch (error) {
                          console.error('Error in Lexical afterRead:', error);
                          return value;
                        }
                      },
                    ],
                    beforeChange: [
                      ({ value }) => {
                        try {
                          // Only process if value needs normalization
                          if (!value || typeof value === 'string') {
                            return prepareLexicalForStorage(value);
                          }
                          return value;
                        } catch (error) {
                          console.error(
                            'Error in Lexical beforeChange:',
                            error
                          );
                          return value;
                        }
                      },
                    ],
                  },
                },
              ],
            },
            {
              name: 'keywords',
              label: 'Enhanced Keywords',
              type: 'array',
              fields: [
                {
                  name: 'keyword',
                  type: 'text',
                },
              ],
              admin: {
                description: 'AI-enhanced keywords for English content',
              },
            },
            {
              name: 'enhancedKeyInsights',
              label: 'Enhanced Key Insights',
              type: 'array',
              fields: [
                {
                  name: 'insight',
                  type: 'text',
                  required: true,
                },
              ],
              admin: {
                description:
                  'AI-enhanced key insights highlighting the most important takeaways',
              },
            },
          ],
        },
        {
          label: 'Sources',
          name: 'sourcesTab',
          fields: [
            // Source URL moved from sidebar to Sources tab
            {
              name: 'sourceUrl',
              label: 'Source URL',
              type: 'text',
              admin: {
                description: 'Original article URL from RSS feed',
              },
            },
            {
              name: 'originalPublishedAt',
              label: 'Source Published Date',
              type: 'date',
              admin: {
                readOnly: true,
                date: {
                  pickerAppearance: 'dayAndTime',
                },
                description: 'When the original source article was published',
              },
            },
            {
              name: 'originalTitle',
              label: 'Original Title',
              type: 'text',
              admin: {
                readOnly: true,
                description: 'Original article title from the source',
              },
            },
            {
              name: 'originalSummary',
              label: 'Original Summary',
              type: 'textarea',
              admin: {
                readOnly: true,
                description:
                  'Original article summary from Firecrawl extraction',
              },
            },
            {
              type: 'collapsible',
              label: 'Original Content',
              admin: {
                initCollapsed: true,
                description: 'Original scraped content from source article',
              },
              fields: [
                // FIXED: Minimal hooks for read-only content
                {
                  name: 'originalContent',
                  label: 'Content',
                  type: 'richText',
                  editor: defaultLexical,
                  admin: {
                    readOnly: true,
                    description:
                      'Original scraped content from Firecrawl, converted to Lexical format',
                  },
                  hooks: {
                    afterRead: [
                      ({ value }) => {
                        try {
                          return prepareLexicalForReading(value);
                        } catch (error) {
                          console.error(
                            'Error in original content afterRead:',
                            error
                          );
                          return value;
                        }
                      },
                    ],
                  },
                },
              ],
            },
          ],
        },
        // FIXED: Proper conditional tab visibility
        {
          label: 'German Translation',
          name: 'germanTab',
          admin: {
            condition: data => data?.hasGermanTranslation === true,
          },
          fields: [
            {
              name: 'germanTitle',
              label: 'German Title',
              type: 'text',
              admin: {
                description:
                  'AI-translated German title optimised for German markets',
              },
            },
            {
              name: 'germanSummary',
              label: 'German Summary',
              type: 'textarea',
              admin: {
                description: 'AI-translated German summary (150-200 words)',
              },
            },
            // FIXED: Consistent Lexical hook pattern
            {
              name: 'germanContent',
              label: 'German Content',
              type: 'richText',
              editor: defaultLexical,
              admin: {
                description:
                  'AI-translated German content optimised for German markets (600-750 words)',
              },
              hooks: {
                afterRead: [
                  ({ value }) => {
                    try {
                      return prepareLexicalForReading(value);
                    } catch (error) {
                      console.error(
                        'Error in German content afterRead:',
                        error
                      );
                      return value;
                    }
                  },
                ],
                beforeChange: [
                  ({ value }) => {
                    try {
                      if (!value || typeof value === 'string') {
                        return prepareLexicalForStorage(value);
                      }
                      return value;
                    } catch (error) {
                      console.error(
                        'Error in German content beforeChange:',
                        error
                      );
                      return value;
                    }
                  },
                ],
              },
            },
            {
              name: 'germanKeywords',
              label: 'German Keywords',
              type: 'array',
              fields: [
                {
                  name: 'keyword',
                  type: 'text',
                },
              ],
              admin: {
                description: 'AI-translated German keywords',
              },
            },
            {
              name: 'germanKeyInsights',
              label: 'German Key Insights',
              type: 'array',
              fields: [
                {
                  name: 'insight',
                  type: 'text',
                  required: true,
                },
              ],
              admin: {
                description: 'AI-translated German key insights',
              },
            },
            // Translation quality metrics removed - no longer needed
          ],
        },
        {
          label: 'SEO',
          name: 'meta',
          fields: [
            MetaTitleField({
              hasGenerateFn: true,
            }),
            MetaDescriptionField({
              hasGenerateFn: true,
            }),
            MetaImageField({
              relationTo: 'media',
              hasGenerateFn: true,
            }),
            PreviewField({
              hasGenerateFn: true,
              titlePath: 'meta.title',
              descriptionPath: 'meta.description',
            }),
            OverviewField({
              titlePath: 'meta.title',
              descriptionPath: 'meta.description',
              imagePath: 'meta.image',
            }),
          ],
        },
      ],
    },
  ],
  // FIXED: Use beforeValidate for preprocessing with proper error handling
  hooks: {
    beforeValidate: [
      async ({ data, operation, req }) => {
        try {
          // Set published metadata when moving to published status
          if (operation === 'update' && data?.workflowStage === 'published') {
            // Set published metadata only if not already set
            if (!data.publishedAt) {
              data.publishedAt = new Date().toISOString();
            }
            if (!data.publishedBy && req?.user?.id) {
              data.publishedBy = req.user.id;
            }
          }
          return data;
        } catch (error) {
          console.error('Error in collection beforeValidate hook:', error);
          return data;
        }
      },
    ],
  },
  // Versioning disabled to avoid database migration conflicts
  // Using custom workflowStage field for article lifecycle management
  // versions: {
  //   drafts: true,
  // },
};

export default Articles;
