import { vercelBlobStorage } from '@payloadcms/storage-vercel-blob';
import { postgresAdapter } from '@payloadcms/db-postgres';
//import { payloadCloudPlugin } from '@payloadcms/payload-cloud';
import { seoPlugin } from '@payloadcms/plugin-seo';
import { lexicalEditor } from '@payloadcms/richtext-lexical';
import { resendAdapter } from '@payloadcms/email-resend';
import { buildConfig } from 'payload';
import sharp from 'sharp';
import { fileURLToPath } from 'node:url';
import path from 'path';
import { lexicalToText } from '@/lib/utils/lexical';

const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);
import Articles from './collections/Articles';
import { Categories } from './collections/Categories';
import Keywords from './collections/Keywords';
import { Media } from './collections/Media';
import ProcessedUrls from './collections/ProcessedUrls';
import RSSFeeds from './collections/RSSFeeds';
import { Users } from './collections/Users';
import { Pages } from './collections/Pages';
// Import migrations for production
import { migrations } from './migrations';
// Import globals
import { Header, Footer } from './globals';

export default buildConfig({
  admin: {
    user: Users.slug,
    importMap: {
      baseDir: path.resolve(dirname),
    },
  },
  email: resendAdapter({
    defaultFromAddress:
      process.env.RESEND_DEFAULT_FROM_ADDRESS || '<EMAIL>',
    defaultFromName: process.env.RESEND_DEFAULT_FROM_NAME || 'Börsen Blick',
    apiKey: process.env.RESEND_API_KEY || '',
  }),
  collections: [
    Users,
    Articles,
    Categories,
    Pages,
    Keywords,
    ProcessedUrls,
    RSSFeeds,
    Media,
  ],
  globals: [Header, Footer],
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  db: postgresAdapter({
    pool: {
      connectionString: process.env.DATABASE_URI || '',
    },
    // Disable automatic schema sync to prevent dev mode prompts
    push: false,
    // Use migrations in production for safety
    prodMigrations:
      process.env.NODE_ENV === 'production' ? migrations : undefined,
  }),
  sharp,
  plugins: [
    vercelBlobStorage({
      enabled: true,
      collections: {
        media: true,
      },
      token: process.env.BLOB_READ_WRITE_TOKEN,
    }),
    seoPlugin({
      generateTitle: ({ doc }) =>
        doc.germanTab?.germanTitle ||
        doc.englishTab?.enhancedTitle ||
        doc.englishTab?.title ||
        doc.title,
      generateImage: ({ doc }) => doc.featuredImage,
      generateDescription: ({ doc }) => {
        // Handle Articles collection
        if (doc.germanTab?.germanSummary) {
          return doc.germanTab.germanSummary;
        }
        if (doc.englishTab?.enhancedSummary) {
          return doc.englishTab.enhancedSummary;
        }

        // Handle Pages collection - prioritize German content
        if (doc.germanTab?.germanContent) {
          const plainText = lexicalToText(doc.germanTab.germanContent);
          if (plainText) {
            // Extract first paragraph (up to first double newline or 160 chars max for SEO)
            const firstParagraph = plainText.split('\n\n')[0];
            return firstParagraph.length > 160
              ? firstParagraph.substring(0, 157) + '...'
              : firstParagraph;
          }
        }

        // Fallback to English content for Pages if no German translation
        if (doc.englishTab?.content) {
          const plainText = lexicalToText(doc.englishTab.content);
          if (plainText) {
            // Extract first paragraph (up to first double newline or 160 chars max for SEO)
            const firstParagraph = plainText.split('\n\n')[0];
            return firstParagraph.length > 160
              ? firstParagraph.substring(0, 157) + '...'
              : firstParagraph;
          }
        }

        return '';
      },
      generateURL: ({ doc, collectionSlug }) =>
        `https://www.borsenblick.de/${collectionSlug}/${doc?.slug}`,
    }),
  ],
});
