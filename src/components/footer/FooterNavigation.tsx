import { Footer } from './Footer';
import { getPayload } from 'payload';
import config from '@payload-config';
import type { Footer as FooterType } from '@/payload-types';

export default async function FooterNavigation() {
  try {
    // PayloadCMS native pattern - direct Local API usage
    const payload = await getPayload({ config });

    const footer = await payload.findGlobal({
      slug: 'footer',
      depth: 1,
    });

    return <Footer footerData={footer} />;
  } catch (error) {
    // Framework-appropriate error handling - show footer with fallback data
    console.error('Footer: Failed to fetch footer global:', error);
    return <Footer footerData={null} />;
  }
}
