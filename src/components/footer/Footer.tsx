import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {
  Instagram,
  Facebook,
  Twitter,
  Linkedin,
  Youtube,
  Github,
  Mail,
  MessageCircle,
} from 'lucide-react';
import type { NavigationItem } from '@/components/navigation/types';
import type { Footer as FooterType } from '@/payload-types';

interface FooterProps {
  footerData?: FooterType | null;
}

interface ProcessedFooterData {
  logo: {
    image?: string;
    title: string;
    url: string;
    alt?: string;
  };
  description?: string;
  navigationSections: Array<{
    title: string;
    links: NavigationItem[];
  }>;
  socialLinks: Array<{
    platform: string;
    icon: React.ReactElement;
    href: string;
    label: string;
  }>;
  legalLinks: NavigationItem[];
  copyright: string;
}

// Icon mapping for social platforms
const socialIconMap = {
  instagram: <Instagram className="size-5" />,
  facebook: <Facebook className="size-5" />,
  twitter: <Twitter className="size-5" />,
  linkedin: <Linkedin className="size-5" />,
  youtube: <Youtube className="size-5" />,
  github: <Github className="size-5" />,
  threads: <MessageCircle className="size-5" />,
  email: <Mail className="size-5" />,
};

// Fallback data for when no footer data is available
const getFallbackFooterData = (): ProcessedFooterData => ({
  logo: {
    title: 'Börsen Blick',
    url: '/',
  },
  description: 'Deutsche Finanz- und Wirtschaftsnachrichten',
  navigationSections: [
    {
      title: 'Navigation',
      links: [
        {
          id: '1',
          label: 'Home',
          href: '/',
          isExternal: false,
          openInNewTab: false,
        },
        {
          id: '2',
          label: 'Articles',
          href: '/articles',
          isExternal: false,
          openInNewTab: false,
        },
      ],
    },
  ],
  socialLinks: [],
  legalLinks: [
    {
      id: '1',
      label: 'Datenschutz',
      href: '/privacy',
      isExternal: false,
      openInNewTab: false,
    },
    {
      id: '2',
      label: 'Impressum',
      href: '/imprint',
      isExternal: false,
      openInNewTab: false,
    },
  ],
  copyright: `© ${new Date().getFullYear()} börsenblick.de. Alle Rechte vorbehalten.`,
});

// Process PayloadCMS footer data into component-friendly format
const processFooterData = (footerData: FooterType): ProcessedFooterData => {
  // Process navigation sections
  const navigationSections =
    footerData.navigationSections?.map((section: any) => ({
      title: section.title,
      links:
        section.links?.map((linkItem: any) => ({
          id: linkItem.id || crypto.randomUUID(),
          label: linkItem.link.label,
          href:
            linkItem.link.type === 'reference'
              ? resolveInternalLink(linkItem.link.reference)
              : linkItem.link.url || '#',
          isExternal:
            linkItem.link.type === 'custom' && isExternalUrl(linkItem.link.url),
          openInNewTab: linkItem.link.newTab || false,
        })) || [],
    })) || [];

  // Process social links
  const socialLinks =
    footerData.socialLinks?.map((social: any) => ({
      platform: social.platform,
      icon: socialIconMap[social.platform as keyof typeof socialIconMap] || (
        <Mail className="size-5" />
      ),
      href: social.url,
      label: social.label,
    })) || [];

  // Process legal links
  const legalLinks =
    footerData.legalLinks?.map((linkItem: any) => ({
      id: linkItem.id || crypto.randomUUID(),
      label: linkItem.link.label,
      href:
        linkItem.link.type === 'reference'
          ? resolveInternalLink(linkItem.link.reference)
          : linkItem.link.url || '#',
      isExternal:
        linkItem.link.type === 'custom' && isExternalUrl(linkItem.link.url),
      openInNewTab: linkItem.link.newTab || false,
    })) || [];

  // Process logo
  const logo = {
    image:
      typeof footerData.logo?.image === 'object' && footerData.logo.image?.url
        ? footerData.logo.image.url
        : undefined,
    title: footerData.logo?.title || 'Börsen Blick',
    url: footerData.logo?.url || '/',
    alt:
      typeof footerData.logo?.image === 'object' && footerData.logo.image?.alt
        ? footerData.logo.image.alt
        : 'Logo',
  };

  // Generate German copyright text
  const currentYear = new Date().getFullYear();
  const companyName = footerData.copyright?.companyName || 'börsenblick.de';
  const customText = footerData.copyright?.customText;
  const copyright = `© ${currentYear} ${companyName}. Alle Rechte vorbehalten.${customText ? ` ${customText}` : ''}`;

  return {
    logo,
    description: footerData.description || undefined,
    navigationSections,
    socialLinks,
    legalLinks,
    copyright,
  };
};

// Helper functions (similar to header link resolver)
function resolveInternalLink(reference: any): string {
  if (!reference) return '#';

  switch (reference.relationTo) {
    case 'pages':
      return reference.value.slug === 'home' ? '/' : `/${reference.value.slug}`;
    case 'articles':
      return `/articles/${reference.value.slug}`;
    case 'categories':
      return `/categories/${reference.value.slug}`;
    default:
      return '#';
  }
}

function isExternalUrl(url: string | null | undefined): boolean {
  if (!url) return false;
  try {
    const urlObj = new URL(
      url,
      typeof window !== 'undefined'
        ? window.location.origin
        : 'http://localhost:3000'
    );
    return (
      urlObj.origin !==
      (typeof window !== 'undefined'
        ? window.location.origin
        : 'http://localhost:3000')
    );
  } catch {
    return false;
  }
}

export const Footer: React.FC<FooterProps> = ({ footerData }) => {
  const data = footerData
    ? processFooterData(footerData)
    : getFallbackFooterData();

  return (
    <footer className="py-8 md:py-12 bg-background">
      <div className="flex w-full items-center">
        {/* Left spacer */}
        <div className="flex-1"></div>

        {/* 1440px container matching header */}
        <div className="w-full max-w-[1440px] px-6">
          <div className="flex w-full flex-col justify-between gap-10 lg:flex-row lg:items-start lg:text-left">
            {/* Logo and Description Section */}
            <div className="flex w-full flex-col justify-between gap-6 lg:items-start">
              {/* Logo */}
              <div className="flex items-center gap-2 lg:justify-start">
                <Link href={data.logo.url} className="flex items-center gap-2">
                  {data.logo.image && (
                    <div className="relative h-8 w-8">
                      <Image
                        src={data.logo.image}
                        alt={data.logo.alt || 'Logo'}
                        fill
                        className="object-contain"
                      />
                    </div>
                  )}
                  <h2 className="text-xl font-medium">{data.logo.title}</h2>
                </Link>
              </div>

              {/* Description */}
              {data.description && (
                <p className="text-muted-foreground max-w-xl text-sm">
                  {data.description}
                </p>
              )}

              {/* Social Links */}
              {data.socialLinks.length > 0 && (
                <ul className="text-foreground flex items-center gap-6">
                  {data.socialLinks.map((social, idx) => (
                    <li
                      key={idx}
                      className="hover:text-primary font-medium transition-colors"
                    >
                      <Link
                        href={social.href}
                        aria-label={social.label}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {social.icon}
                      </Link>
                    </li>
                  ))}
                </ul>
              )}
            </div>

            {/* Navigation Sections */}
            <div className="grid w-full gap-6 md:grid-cols-3 lg:gap-20">
              {data.navigationSections.map((section, sectionIdx) => (
                <div key={sectionIdx}>
                  <h3 className="mb-4 font-medium">{section.title}</h3>
                  <ul className="text-muted-foreground flex flex-col gap-3 text-sm">
                    {section.links.map(link => (
                      <li
                        key={link.id}
                        className="hover:text-primary font-base transition-colors"
                      >
                        <Link
                          href={link.href}
                          target={link.openInNewTab ? '_blank' : undefined}
                          rel={
                            link.isExternal ? 'noopener noreferrer' : undefined
                          }
                        >
                          {link.label}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

          {/* Bottom Section: Copyright and Legal Links */}
          <div className="text-muted-foreground mt-8 flex flex-col justify-between gap-4 border-t border-border py-8 text-xs font-base md:flex-row md:items-center md:text-left">
            <p className="order-2 lg:order-1">{data.copyright}</p>

            {data.legalLinks.length > 0 && (
              <ul className="order-1 flex flex-col gap-2 md:order-2 md:flex-row md:gap-4">
                {data.legalLinks.map(link => (
                  <li
                    key={link.id}
                    className="hover:text-primary transition-colors"
                  >
                    <Link
                      href={link.href}
                      target={link.openInNewTab ? '_blank' : undefined}
                      rel={link.isExternal ? 'noopener noreferrer' : undefined}
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>

        {/* Right spacer */}
        <div className="flex-1"></div>
      </div>
    </footer>
  );
};
