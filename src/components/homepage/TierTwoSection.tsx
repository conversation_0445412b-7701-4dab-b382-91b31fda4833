import { getCachedTier2Articles } from '@/lib/cache/articles';
import NewsCard from '@/components/NewsCard';
import { DEFAULT_HOMEPAGE_CONFIG } from './types';
import { TriangleAlert } from 'lucide-react';

export default async function TierTwoSection() {
  const config = DEFAULT_HOMEPAGE_CONFIG;

  try {
    const tier2Data = await getCachedTier2Articles();

    return (
      <aside
        id="market-analysis"
        className="space-y-3 md:space-y-4"
        aria-labelledby="tier-2-heading"
      >
        <h2 id="tier-2-heading" className="sr-only">
          Marktanalyse
        </h2>

        {/* Tier 2 Articles - Default cards in vertical stack */}
        {tier2Data.docs.length > 0 ? (
          <div className="space-y-3 md:space-y-4">
            {tier2Data.docs.map((article, index) => (
              <NewsCard
                key={article.id}
                article={article}
                variant="default"
                showDescription={true}
                locale={config.locale}
                priority={index < 2} // Priority for first 2 articles
                className="shadow-sm"
              />
            ))}
          </div>
        ) : (
          // Empty State
          <div className="text-center py-4 md:py-6">
            <div className="text-muted-foreground">
              <TriangleAlert className="size-4 mx-auto mb-3" />
              <p className="text-xs mb-2">Keine Marktanalyse verfügbar</p>
              <p className="text-xs">Schauen Sie später noch einmal vorbei</p>
            </div>
          </div>
        )}
      </aside>
    );
  } catch (error) {
    console.error('Error fetching tier 2 articles:', error);

    // Graceful fallback with error state
    return (
      <aside
        id="market-analysis"
        className="space-y-3 md:space-y-4"
        aria-label="Marktanalyse"
      >
        <div className="text-center py-4 md:py-6">
          <div className="text-muted-foreground">
            <TriangleAlert className="size-4 mx-auto mb-3" />
            <p className="text-xs mb-2">Fehler beim Laden der Marktanalyse</p>
            <p className="text-xs">Schauen Sie später noch einmal vorbei</p>
          </div>
        </div>
      </aside>
    );
  }
}
