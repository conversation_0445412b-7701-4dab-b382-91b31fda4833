import { CardSkeleton } from '@/components/NewsCard';

export function TierOneSkeleton() {
  return (
    <div className="mb-8 sm:mb-10 lg:mb-12">
      {/* Large featured article skeleton */}
      <div className="mb-6 md:mb-8">
        <CardSkeleton variant="default" />
      </div>

      {/* Two smaller featured articles */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6 lg:gap-8">
        <CardSkeleton variant="default" />
        <CardSkeleton variant="default" />
      </div>
    </div>
  );
}

export function TierTwoSkeleton() {
  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Horizontal cards grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-3 md:gap-4 lg:gap-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <CardSkeleton key={i} variant="horizontal-left" />
        ))}
      </div>
    </div>
  );
}

export function TierThreeSkeleton() {
  return (
    <div className="space-y-4 sm:space-y-5">
      {/* Title-only cards */}
      <div className="space-y-4">
        {Array.from({ length: 6 }).map((_, i) => (
          <CardSkeleton key={i} variant="title-only" />
        ))}
      </div>
    </div>
  );
}

export function TrendingSkeleton() {
  return (
    <div>
      {/* Trending placeholder */}
      <div className="p-4 lg:p-6 bg-gray-50 dark:bg-gray-900 rounded-lg animate-pulse">
        <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-3/4 mx-auto mb-2" />
        <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded w-1/2 mx-auto" />
      </div>
    </div>
  );
}
