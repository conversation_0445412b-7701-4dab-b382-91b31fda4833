'use client';

import { useEffect, useRef } from 'react';
import type { AccessibilityConfig } from './types';

interface AccessibilityNavProps {
  config: AccessibilityConfig;
  isLoading: boolean;
  hasError: boolean;
}

export const AccessibilityNav = ({
  config,
  isLoading,
  hasError,
}: AccessibilityNavProps) => {
  const liveRegionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!config.enableAriaLive || !liveRegionRef.current) return;

    let message = '';
    if (isLoading) {
      message = 'Loading content...';
    } else if (hasError) {
      message = 'Error loading content. Please try again.';
    } else {
      message = 'Content loaded successfully';
    }

    // Update live region with appropriate delay
    const timer = setTimeout(() => {
      if (liveRegionRef.current) {
        liveRegionRef.current.textContent = message;
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [isLoading, hasError, config.enableAriaLive]);

  // Handle skip to content internally
  const handleSkipToContent = () => {
    focusManagement.focusMainContent();
  };

  if (!config.enableSkipLinks) {
    return (
      <>
        {/* ARIA Live Region */}
        {config.enableAriaLive && (
          <div
            ref={liveRegionRef}
            className="sr-only"
            aria-live="polite"
            aria-atomic="true"
            role="status"
          />
        )}
      </>
    );
  }

  return (
    <>
      {/* Skip Links */}
      <nav
        className="sr-only focus-within:not-sr-only"
        aria-label="Skip navigation"
      >
        <div className="fixed top-0 left-0 z-50 bg-background border border-border p-4 m-4 rounded-md shadow-lg">
          <ul className="flex flex-col gap-2 text-sm">
            <li>
              <a
                href="#main-content"
                className="text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-2 py-1"
                onClick={handleSkipToContent}
              >
                Skip to main content
              </a>
            </li>
            <li>
              <a
                href="#featured-articles"
                className="text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-2 py-1"
              >
                Skip to featured articles
              </a>
            </li>
            <li>
              <a
                href="#latest-updates"
                className="text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-2 py-1"
              >
                Skip to latest updates
              </a>
            </li>
            <li>
              <a
                href="#market-analysis"
                className="text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-2 py-1"
              >
                Skip to market analysis
              </a>
            </li>
          </ul>
        </div>
      </nav>

      {/* ARIA Live Region */}
      {config.enableAriaLive && (
        <div
          ref={liveRegionRef}
          className="sr-only"
          aria-live="polite"
          aria-atomic="true"
          role="status"
        />
      )}

      {/* High Contrast Mode Toggle */}
      {config.enableHighContrast && (
        <button
          className="fixed top-4 right-4 z-50 bg-background border border-border p-2 rounded-md shadow-lg sr-only focus:not-sr-only"
          onClick={() => {
            document.documentElement.classList.toggle('high-contrast');
          }}
          aria-label="Toggle high contrast mode"
        >
          <span className="text-sm font-medium">High Contrast</span>
        </button>
      )}
    </>
  );
};

// Focus management utilities
export const focusManagement = {
  /**
   * Focuses the main content area
   */
  focusMainContent: () => {
    const mainContent = document.getElementById('main-content');
    if (mainContent) {
      mainContent.focus();
      mainContent.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  },

  /**
   * Focuses a specific tier section
   */
  focusTierSection: (tier: 'tier-1' | 'tier-2' | 'tier-3') => {
    const section = document.getElementById(`${tier}-section`);
    if (section) {
      section.focus();
      section.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  },

  /**
   * Manages focus after content updates
   */
  manageFocusAfterUpdate: (updatedTier?: 'tier-1' | 'tier-2' | 'tier-3') => {
    if (updatedTier) {
      // Focus the updated tier
      setTimeout(() => focusManagement.focusTierSection(updatedTier), 100);
    } else {
      // Focus main content
      setTimeout(() => focusManagement.focusMainContent(), 100);
    }
  },

  /**
   * Traps focus within a modal or dialog
   */
  trapFocus: (element: HTMLElement) => {
    const focusableElements = element.querySelectorAll(
      'a[href], button, textarea, input[type="text"], input[type="radio"], input[type="checkbox"], select'
    );
    const firstFocusableElement = focusableElements[0] as HTMLElement;
    const lastFocusableElement = focusableElements[
      focusableElements.length - 1
    ] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstFocusableElement) {
            lastFocusableElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastFocusableElement) {
            firstFocusableElement.focus();
            e.preventDefault();
          }
        }
      }
    };

    element.addEventListener('keydown', handleTabKey);
    firstFocusableElement.focus();

    return () => {
      element.removeEventListener('keydown', handleTabKey);
    };
  },
};
