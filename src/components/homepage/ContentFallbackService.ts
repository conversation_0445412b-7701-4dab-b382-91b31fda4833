import type { Article } from '@/payload-types';
import type { TierData, ContentFallbackStrategy } from './types';

export class ContentFallbackService {
  private strategy: ContentFallbackStrategy;

  constructor(strategy: ContentFallbackStrategy) {
    this.strategy = strategy;
  }

  /**
   * Applies content fallback strategies to ensure balanced content across tiers
   */
  applyFallbackStrategies(articles: TierData): TierData {
    if (!this.strategy.enableCrossTierPromotion) {
      return articles;
    }

    let result = { ...articles };

    // Apply cross-tier promotion
    result = this.applyCrossTierPromotion(result);

    // Ensure minimum articles per tier
    result = this.ensureMinimumContent(result);

    return result;
  }

  /**
   * Promotes articles from lower tiers to higher tiers when needed
   */
  private applyCrossTierPromotion(articles: TierData): TierData {
    const result = { ...articles };

    // If tier 1 is empty, promote from tier 2
    if (result.tier1.length === 0 && result.tier2.length > 0) {
      // Take the first article from tier 2 and promote to tier 1
      const promotedArticle = result.tier2.shift()!;
      result.tier1.push(promotedArticle);
      console.log(
        `Promoted article "${promotedArticle.title}" from tier-2 to tier-1`
      );
    }

    // If tier 1 still needs more articles, promote from tier 3
    if (
      result.tier1.length < this.strategy.minimumArticlesPerTier &&
      result.tier3.length > 0
    ) {
      const articlesNeeded =
        this.strategy.minimumArticlesPerTier - result.tier1.length;
      const promotedArticles = result.tier3.splice(0, articlesNeeded);
      result.tier1.push(...promotedArticles);
      console.log(
        `Promoted ${promotedArticles.length} articles from tier-3 to tier-1`
      );
    }

    // If tier 2 is empty, promote from tier 3
    if (result.tier2.length === 0 && result.tier3.length > 0) {
      const articlesNeeded = Math.min(3, result.tier3.length);
      const promotedArticles = result.tier3.splice(0, articlesNeeded);
      result.tier2.push(...promotedArticles);
      console.log(
        `Promoted ${promotedArticles.length} articles from tier-3 to tier-2`
      );
    }

    return result;
  }

  /**
   * Ensures each tier has minimum required content
   */
  private ensureMinimumContent(articles: TierData): TierData {
    const result = { ...articles };

    // Create a pool of all available articles for redistribution
    const allArticles = [...result.tier1, ...result.tier2, ...result.tier3];

    if (allArticles.length === 0) {
      return result;
    }

    // If we don't have enough articles total, duplicate some for demonstration
    if (allArticles.length < this.strategy.minimumArticlesPerTier * 3) {
      console.warn(
        'Insufficient articles for all tiers, content may be duplicated'
      );
    }

    // Redistribute articles to ensure minimum content
    const redistributed = this.redistributeArticles(allArticles);

    return redistributed;
  }

  /**
   * Redistributes articles across tiers to ensure balanced content
   */
  private redistributeArticles(articles: Article[]): TierData {
    const result: TierData = { tier1: [], tier2: [], tier3: [] };

    // Sort articles by publication date and pinned status
    const sortedArticles = [...articles].sort((a, b) => {
      // Pinned articles come first
      if (a.pinned && !b.pinned) return -1;
      if (!a.pinned && b.pinned) return 1;

      // Then by publication date
      const dateA = new Date(a.publishedAt || a.createdAt);
      const dateB = new Date(b.publishedAt || b.createdAt);
      return dateB.getTime() - dateA.getTime();
    });

    // Distribute articles across tiers
    const tier1Count = Math.min(3, sortedArticles.length);
    const tier2Count = Math.min(
      6,
      Math.max(0, sortedArticles.length - tier1Count)
    );
    const tier3Count = Math.max(
      0,
      sortedArticles.length - tier1Count - tier2Count
    );

    result.tier1 = sortedArticles.slice(0, tier1Count);
    result.tier2 = sortedArticles.slice(tier1Count, tier1Count + tier2Count);
    result.tier3 = sortedArticles.slice(
      tier1Count + tier2Count,
      tier1Count + tier2Count + tier3Count
    );

    return result;
  }

  /**
   * Checks if content needs rebalancing
   */
  needsRebalancing(articles: TierData): boolean {
    const totalArticles =
      articles.tier1.length + articles.tier2.length + articles.tier3.length;

    // Check if any tier is completely empty
    if (
      articles.tier1.length === 0 ||
      articles.tier2.length === 0 ||
      articles.tier3.length === 0
    ) {
      return totalArticles > 0;
    }

    // Check if tiers are severely imbalanced
    const maxTierSize = Math.max(
      articles.tier1.length,
      articles.tier2.length,
      articles.tier3.length
    );
    const minTierSize = Math.min(
      articles.tier1.length,
      articles.tier2.length,
      articles.tier3.length
    );

    return maxTierSize > minTierSize * 3; // Rebalance if one tier has 3x more than another
  }

  /**
   * Gets content statistics for debugging
   */
  getContentStats(articles: TierData): {
    tier1: number;
    tier2: number;
    tier3: number;
    total: number;
    pinnedCount: number;
    recentCount: number;
  } {
    const allArticles = [
      ...articles.tier1,
      ...articles.tier2,
      ...articles.tier3,
    ];
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    return {
      tier1: articles.tier1.length,
      tier2: articles.tier2.length,
      tier3: articles.tier3.length,
      total: allArticles.length,
      pinnedCount: allArticles.filter(a => a.pinned).length,
      recentCount: allArticles.filter(a => {
        const publishDate = new Date(a.publishedAt || a.createdAt);
        return publishDate > oneDayAgo;
      }).length,
    };
  }
}
