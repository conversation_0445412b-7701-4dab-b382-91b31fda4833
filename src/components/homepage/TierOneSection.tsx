import { getCachedTier1Layout } from '@/lib/cache/articles';
import NewsCard from '@/components/NewsCard';
import { DEFAULT_HOMEPAGE_CONFIG } from './types';
import { TriangleAlert } from 'lucide-react'; // ✅ FIX: Import consistent icon

export default async function TierOneSection() {
  const config = DEFAULT_HOMEPAGE_CONFIG;

  try {
    const { prominentPost, gridPosts, horizontalPosts } =
      await getCachedTier1Layout();

    return (
      <section
        className="space-y-4 md:space-y-6 mb-4 sm:mb-5 lg:mb-6"
        aria-labelledby="tier-1-heading"
      >
        <h2 id="tier-1-heading" className="sr-only">
          Wichtige Artikel
        </h2>

        {/* 1. Prominent Post - Full width hero */}
        {prominentPost && (
          <div className="mb-4 md:mb-6">
            <NewsCard
              article={prominentPost}
              variant="default"
              showDescription={true}
              priority={true}
              locale={config.locale}
              className="shadow-sm"
            />
            {prominentPost.pinned && (
              <div className="mt-2 flex items-center gap-2 text-xs text-muted-foreground">
                <span className="size-2 bg-blue-500 rounded-full animate-pulse" />
                <span>Wichtiger Artikel</span>
              </div>
            )}
          </div>
        )}

        {/* 2. Grid Posts - 6 posts in 2x3 grid */}
        {gridPosts.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4 mb-4 md:mb-6">
            {gridPosts.map((article, index) => (
              <NewsCard
                key={article.id}
                article={article}
                variant="default"
                showDescription={true}
                locale={config.locale}
                priority={index < 2} // Priority for first 2 grid items
              />
            ))}
          </div>
        )}

        {/* 3. Horizontal Posts - All remaining articles */}
        {horizontalPosts.length > 0 && (
          <div className="space-y-2 md:space-y-3">
            <h3 className="text-sm font-medium text-muted-foreground mb-3">
              Weitere wichtige Artikel
            </h3>
            {horizontalPosts.map(article => (
              <NewsCard
                key={article.id}
                article={article}
                variant="horizontal-left"
                showDescription={true}
                locale={config.locale}
              />
            ))}
          </div>
        )}

        {/* Empty State */}
        {!prominentPost &&
          gridPosts.length === 0 &&
          horizontalPosts.length === 0 && (
            <div className="text-center py-6">
              <div className="text-muted-foreground">
                <TriangleAlert className="size-4 mx-auto mb-3" />
                <p className="text-xs mb-2">
                  Keine wichtigen Artikel verfügbar
                </p>
                <p className="text-xs">Schauen Sie später noch einmal vorbei</p>
              </div>
            </div>
          )}
      </section>
    );
  } catch (error) {
    console.error('Error fetching tier 1 layout:', error);

    // Graceful fallback with error state
    return (
      <section
        className="space-y-4 md:space-y-6 mb-4 sm:mb-5 lg:mb-6"
        aria-label="Wichtige Artikel"
      >
        <div className="text-center py-6">
          <div className="text-muted-foreground">
            <TriangleAlert className="size-4 mx-auto mb-3" />
            <p className="text-xs mb-2">
              Fehler beim Laden der wichtigen Artikel
            </p>
            <p className="text-xs">Schauen Sie später noch einmal vorbei</p>
          </div>
        </div>
      </section>
    );
  }
}
