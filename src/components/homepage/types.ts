import type { Article } from '@/payload-types';

export interface TierData {
  tier1: Article[];
  tier2: Article[];
  tier3: Article[];
}

export interface HomepageProps {
  locale?: 'en' | 'de';
  initialData?: TierData;
}

export interface HomepageState {
  articles: TierData;
  isLoading: boolean;
  error: Error | null;
  lastFetch: Date | null;
}

export interface TierConfig {
  tier: 'tier-1' | 'tier-2' | 'tier-3';
  title: string;
  variant: 'featured' | 'horizontal' | 'title-only';
  maxArticles?: number;
  emptyStateMessage?: string;
  showMoreButton?: boolean;
  className?: string;
}

export interface ContentFallbackStrategy {
  enableCrossTierPromotion: boolean;
  fallbackToMockData: boolean;
  minimumArticlesPerTier: number;
}

export interface PerformanceConfig {
  enablePrefetching: boolean;
  revalidateTime: number;
  cacheStrategy: 'aggressive' | 'moderate' | 'conservative';
}

export interface AccessibilityConfig {
  enableSkipLinks: boolean;
  enableFocusManagement: boolean;
  enableAriaLive: boolean;
  enableHighContrast: boolean;
  announcePageChanges: boolean;
  keyboardNavigationMode: 'standard' | 'enhanced';
}

export interface HomepageConfig {
  locale: 'en' | 'de';
  tiers: TierConfig[];
  fallbackStrategy: ContentFallbackStrategy;
  performance: PerformanceConfig;
  accessibility: AccessibilityConfig;
}

// Enhanced error handling types
export interface HomepageError {
  type: 'network' | 'payload' | 'cache' | 'unknown';
  message: string;
  code?: string;
  retryable: boolean;
  timestamp: Date;
}

export interface ErrorBoundaryState {
  hasError: boolean;
  error?: HomepageError;
  errorInfo?: any;
  retryCount: number;
  lastRetryTime?: Date;
}

// Cache-related types
export interface CacheConfig {
  enabled: boolean;
  tags: string[];
  revalidateTime: number;
  staleWhileRevalidate: boolean;
}

export interface TierCacheConfig {
  tier1: CacheConfig;
  tier2: CacheConfig;
  tier3: CacheConfig;
}

// Loading state types
export interface LoadingState {
  tier1: boolean;
  tier2: boolean;
  tier3: boolean;
  trending: boolean;
}

// Default configuration with all improvements
export const DEFAULT_HOMEPAGE_CONFIG: HomepageConfig = {
  locale: 'de',
  tiers: [
    {
      tier: 'tier-1',
      title: 'Wichtige Artikel',
      variant: 'featured',
      maxArticles: 3,
      emptyStateMessage: 'Keine wichtigen Artikel verfügbar',
      showMoreButton: false,
      className: 'mb-8 sm:mb-10 lg:mb-12',
    },
    {
      tier: 'tier-2',
      title: 'Marktanalyse',
      variant: 'horizontal',
      maxArticles: 6,
      emptyStateMessage: 'Keine Marktanalyse verfügbar',
      showMoreButton: true,
      className: 'space-y-4 sm:space-y-6',
    },
    {
      tier: 'tier-3',
      title: 'Aktuelle Meldungen',
      variant: 'title-only',
      maxArticles: 8,
      emptyStateMessage: 'Derzeit keine aktuellen Meldungen verfügbar',
      showMoreButton: true,
      className: 'space-y-4 sm:space-y-5',
    },
  ],
  fallbackStrategy: {
    enableCrossTierPromotion: true,
    fallbackToMockData: false, // Changed to false for cleaner production experience
    minimumArticlesPerTier: 1,
  },
  performance: {
    enablePrefetching: true,
    revalidateTime: 300, // 5 minutes
    cacheStrategy: 'moderate',
  },
  accessibility: {
    enableSkipLinks: true,
    enableFocusManagement: true,
    enableAriaLive: true,
    enableHighContrast: false,
    announcePageChanges: true,
    keyboardNavigationMode: 'enhanced',
  },
};

// Cache configuration
export const DEFAULT_CACHE_CONFIG: TierCacheConfig = {
  tier1: {
    enabled: true,
    tags: ['articles', 'tier-1-articles', 'featured-articles'],
    revalidateTime: 300,
    staleWhileRevalidate: true,
  },
  tier2: {
    enabled: true,
    tags: ['articles', 'tier-2-articles', 'market-analysis'],
    revalidateTime: 300,
    staleWhileRevalidate: true,
  },
  tier3: {
    enabled: true,
    tags: ['articles', 'tier-3-articles', 'latest-updates'],
    revalidateTime: 300,
    staleWhileRevalidate: true,
  },
};

// Utility type for component props
export type TierSectionProps = {
  tier: TierConfig['tier'];
  articles: Article[];
  title: string;
  variant: TierConfig['variant'];
  locale?: 'en' | 'de';
  maxArticles?: number;
  emptyStateMessage?: string;
  showMoreButton?: boolean;
  className?: string;
  isLoading?: boolean;
  onShowMore?: () => void;
  showDescriptionForFirst?: number;
};
