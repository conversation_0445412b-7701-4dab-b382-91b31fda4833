import { Suspense } from 'react';
import { getCachedTier2Articles } from '@/lib/cache/articles';
import NewsCard from '@/components/NewsCard';
import RelatedArticlesSkeleton from '@/components/articles/RelatedArticlesSkeleton';

interface RecentArticlesSectionProps {
  maxArticles?: number;
  locale?: 'de' | 'en';
}

async function RecentArticles({
  maxArticles = 4,
  locale = 'de',
}: RecentArticlesSectionProps) {
  try {
    // Use tier-2 articles as "recent articles" for pages
    const articlesData = await getCachedTier2Articles();
    const articles = articlesData.docs.slice(0, maxArticles);

    if (articles.length === 0) {
      return (
        <div className="space-y-4">
          <h3 className="font-sans font-medium text-gray-900 dark:text-gray-100 text-sm pl-4">
            Aktuelle Artikel
          </h3>
          <div className="text-gray-500 dark:text-gray-400 text-sm italic pl-4">
            Derzeit keine aktuellen Artikel verfügbar
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-2 sm:space-y-2.5 font-sans">
        <h3 className="font-sans font-medium text-gray-900 dark:text-gray-100 text-sm pl-4">
          Aktuelle Artikel
        </h3>

        <div className="space-y-1.5 md:space-y-2">
          {articles.map(article => (
            <NewsCard
              key={article.id}
              article={article}
              variant="title-only"
              showDescription={true}
              locale={locale}
            />
          ))}
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error fetching recent articles:', error);
    return (
      <div className="space-y-4">
        <h3 className="font-sans font-medium text-gray-900 dark:text-gray-100 text-sm pl-4">
          Aktuelle Artikel
        </h3>
        <div className="text-gray-500 dark:text-gray-400 text-sm italic pl-4">
          Fehler beim Laden der aktuellen Artikel
        </div>
      </div>
    );
  }
}

export default function RecentArticlesSection(
  props: RecentArticlesSectionProps
) {
  return (
    <Suspense
      fallback={<RelatedArticlesSkeleton maxArticles={props.maxArticles} />}
    >
      <RecentArticles {...props} />
    </Suspense>
  );
}
