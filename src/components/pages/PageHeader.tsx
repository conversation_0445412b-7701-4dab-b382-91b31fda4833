import type { Page } from '@/payload-types';

interface PageHeaderProps {
  page: Page;
  locale?: 'de' | 'en';
}

export default function PageHeader({ page, locale = 'de' }: PageHeaderProps) {
  // Prioritise German content first, then English, then fallback
  const title =
    page.germanTab?.germanTitle || page.englishTab?.title || page.title;

  // Format publication date in German style
  const publishDate = new Date(page.publishedAt || page.createdAt);
  const formattedDate = publishDate.toLocaleDateString('de-DE', {
    weekday: 'long',
    day: 'numeric',
    month: 'long',
    year: 'numeric',
  });

  return (
    <div className="space-y-4">
      {/* Large serif title */}
      <h1 className="text-xl/6 md:text-2xl/8 lg:text-3xl/9 font-serif font-normal text-gray-900 dark:text-gray-100">
        {title}
      </h1>

      {/* Publication Date */}
      <div className="text-xs/4 text-gray-600 dark:text-gray-400">
        <span className="font-sans">{formattedDate}</span>
      </div>
    </div>
  );
}
