import Link from 'next/link';
import type { Page } from '@/payload-types';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

interface PageBreadcrumbsProps {
  page: Page;
  locale?: 'de' | 'en';
}

// Helper function to build breadcrumb chain recursively
function buildBreadcrumbChain(page: Page): Page[] {
  const chain: Page[] = [];
  let currentPage: Page | null = page;

  // Build chain from current page up to root
  while (currentPage) {
    chain.unshift(currentPage); // Add to beginning of array

    // Get parent if it exists and is a Page object (not just ID)
    const parent = currentPage.parent;
    if (parent && typeof parent === 'object') {
      currentPage = parent as Page;
    } else {
      currentPage = null;
    }
  }

  return chain;
}

export default function PageBreadcrumbs({
  page,
  locale = 'de',
}: PageBreadcrumbsProps) {
  // Don't show breadcrumbs if disabled or no hierarchy
  if (!page.enableBreadcrumbs || !page.parent) {
    return null;
  }

  const breadcrumbChain = buildBreadcrumbChain(page);

  // Don't show if only one page in chain (no parent)
  if (breadcrumbChain.length <= 1) {
    return null;
  }

  const getPageTitle = (pageItem: Page): string => {
    return (
      pageItem.germanTab?.germanTitle ||
      pageItem.englishTab?.title ||
      pageItem.title
    );
  };

  return (
    <nav aria-label="Breadcrumb navigation" className="mb-6">
      <Breadcrumb>
        <BreadcrumbList>
          {/* Home link */}
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Startseite</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />

          {/* Breadcrumb chain */}
          {breadcrumbChain.map((pageItem, index) => {
            const isLast = index === breadcrumbChain.length - 1;
            const title = getPageTitle(pageItem);
            const href = `/${pageItem.slug}`;

            return (
              <div key={pageItem.id} className="contents">
                <BreadcrumbItem>
                  {isLast ? (
                    <BreadcrumbPage>{title}</BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink asChild>
                      <Link href={href}>{title}</Link>
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
                {!isLast && <BreadcrumbSeparator />}
              </div>
            );
          })}
        </BreadcrumbList>
      </Breadcrumb>
    </nav>
  );
}
