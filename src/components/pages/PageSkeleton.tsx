import { CardSkeleton } from '@/components/NewsCard';

export default function PageSkeleton() {
  return (
    <div className="min-h-dvh bg-background">
      <div className="max-w-[1440px] mx-auto px-4 py-8 lg:py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-0">
          {/* Column 1: Reserved space skeleton */}
          <aside className="sm:border-r sm:border-border sm:pr-2 lg:pr-3 xl:pr-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-3 sm:order-1 hidden sm:block">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-2/3 mb-2" />
              <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded w-1/2" />
            </div>
          </aside>

          {/* Columns 2-3: Page content skeleton */}
          <section className="sm:col-span-1 lg:col-span-2 xl:col-span-2 sm:border-r sm:border-border sm:px-2 lg:px-3 xl:px-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-1 sm:order-2">
            <div className="animate-pulse space-y-6">
              {/* Title skeleton */}
              <div className="space-y-2">
                <div className="h-8 bg-gray-200 dark:bg-gray-800 rounded w-3/4" />
                <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-1/3" />
              </div>

              {/* Image skeleton */}
              <div className="bg-gray-200 dark:bg-gray-800 aspect-[16/9] rounded-lg" />

              {/* Content skeleton */}
              <div className="space-y-4">
                <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-full" />
                <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-5/6" />
                <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-4/5" />
                <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-full" />
                <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-3/4" />
              </div>
            </div>
          </section>

          {/* Column 4: Recent articles skeleton */}
          <aside className="xl:pl-4 order-2 sm:order-3">
            <div className="space-y-2 sm:space-y-2.5">
              <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-28" />
              <div className="space-y-1.5 md:space-y-2">
                {Array.from({ length: 4 }).map((_, index) => (
                  <CardSkeleton key={index} variant="title-only" />
                ))}
              </div>
            </div>
          </aside>
        </div>
      </div>
    </div>
  );
}
