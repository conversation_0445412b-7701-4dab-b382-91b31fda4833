'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';
import { useRouter } from 'next/navigation';

export function ReloadButton() {
  const router = useRouter();

  const handleReload = () => {
    router.refresh();
  };

  return (
    <Button
      onClick={handleReload}
      className="flex-1 flex items-center gap-2"
      variant="outline"
    >
      <RefreshCw className="h-4 w-4" />
      Neu laden
    </Button>
  );
}
