import NewsCard from '@/components/NewsCard';
import type { Article, Category } from '@/payload-types';
import { TriangleAlert } from 'lucide-react';

interface CategoryTitleOnlySectionProps {
  articles: Article[];
  categoryInfo: Category;
}

export default function CategoryTitleOnlySection({
  articles,
  categoryInfo,
}: CategoryTitleOnlySectionProps) {
  if (articles.length === 0) {
    return (
      <div className="text-center py-6">
        <div className="text-muted-foreground">
          <TriangleAlert className="size-4 mx-auto mb-3" />
          <p className="text-xs mb-1"><PERSON><PERSON></p>
          <p className="text-xs">verfügbar</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2 md:space-y-3">
      {articles.map((article, index) => (
        <NewsCard
          key={article.id}
          article={article}
          variant="title-only"
          showDescription={true}
          locale="de"
          className={`${index === 0 ? 'border-t border-border pt-2' : ''} pb-2 ${index < articles.length - 1 ? 'border-b border-border' : ''}`}
        />
      ))}
    </div>
  );
}
