'use client';

import { useState, useEffect } from 'react';
import NewsCard from '@/components/NewsCard';
import type { Article, Category } from '@/payload-types';

interface UnifiedAdditionalSectionProps {
  initialTier1Articles: Article[];
  initialTier2Articles: Article[];
  initialTier3Articles: Article[];
  initialTrendingArticles: Article[];
  categoryInfo: Category;
  tierStats: {
    tier1: number;
    tier2: number;
    tier3: number;
    trending: number;
  };
}

export default function UnifiedAdditionalSection({
  initialTier1Articles,
  initialTier2Articles,
  initialTier3Articles,
  initialTrendingArticles,
  categoryInfo,
  tierStats,
}: UnifiedAdditionalSectionProps) {
  // State for all columns
  const [tier3Articles, setTier3Articles] =
    useState<Article[]>(initialTier3Articles);
  const [tier1And2Articles, setTier1And2Articles] = useState<Article[]>([
    ...initialTier1Articles,
    ...initialTier2Articles,
  ]);
  const [trendingArticles, setTrendingArticles] = useState<Article[]>(
    initialTrendingArticles
  );

  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMoreContent, setHasMoreContent] = useState(true);

  // Calculate if we have more content to load
  useEffect(() => {
    const totalLoaded =
      tier3Articles.length + tier1And2Articles.length + trendingArticles.length;
    const totalAvailable =
      tierStats.tier1 + tierStats.tier2 + tierStats.tier3 + tierStats.trending;
    setHasMoreContent(totalLoaded < totalAvailable);
  }, [tier3Articles, tier1And2Articles, trendingArticles, tierStats]);

  const handleGlobalReadMore = async () => {
    if (isLoading || !hasMoreContent) return;

    setIsLoading(true);
    try {
      const nextPage = currentPage + 1;

      // Fetch more articles for all tiers simultaneously
      const [tier1Response, tier2Response, tier3Response, trendingResponse] =
        await Promise.all([
          fetch(
            `/api/categories/${categoryInfo.slug}/articles?page=${nextPage}&tier=tier-1&limit=6`
          ),
          fetch(
            `/api/categories/${categoryInfo.slug}/articles?page=${nextPage}&tier=tier-2&limit=6`
          ),
          fetch(
            `/api/categories/${categoryInfo.slug}/articles?page=${nextPage}&tier=tier-3&limit=6`
          ),
          fetch(
            `/api/categories/${categoryInfo.slug}/articles?page=${nextPage}&trending=true&limit=4`
          ),
        ]);

      const [tier1Data, tier2Data, tier3Data, trendingData] = await Promise.all(
        [
          tier1Response.json(),
          tier2Response.json(),
          tier3Response.json(),
          trendingResponse.json(),
        ]
      );

      // Update state with new articles
      if (tier3Data.articles?.length > 0) {
        setTier3Articles(prev => [...prev, ...tier3Data.articles]);
      }

      if (tier1Data.articles?.length > 0 || tier2Data.articles?.length > 0) {
        setTier1And2Articles(prev => [
          ...prev,
          ...(tier1Data.articles || []),
          ...(tier2Data.articles || []),
        ]);
      }

      if (trendingData.articles?.length > 0) {
        setTrendingArticles(prev => [...prev, ...trendingData.articles]);
      }

      setCurrentPage(nextPage);
    } catch (error) {
      console.error('Error loading more articles:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Don't render if no articles at all
  if (
    tier3Articles.length === 0 &&
    tier1And2Articles.length === 0 &&
    trendingArticles.length === 0
  ) {
    return null;
  }

  return (
    <section
      className="mt-8 md:mt-10 lg:mt-12 pt-6 md:pt-8 border-t border-border"
      aria-label="Additional articles"
    >
      {/* 4-Column Grid Layout */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8">
        {/* Column 1: Tier 3 Articles (Title-only) */}
        <div className="xl:col-span-1">
          <div className="space-y-3">
            {tier3Articles.map((article, index) => (
              <NewsCard
                key={article.id}
                article={article}
                variant="title-only"
                showDescription={true}
                locale="de"
                priority={false}
                className="pb-3 border-b border-border last:border-b-0"
              />
            ))}
          </div>
        </div>

        {/* Columns 2-3: Tier 1 + 2 Articles (Horizontal View) */}
        <div className="sm:col-span-1 lg:col-span-2 xl:col-span-2">
          <div className="space-y-4">
            {tier1And2Articles.map((article, index) => (
              <NewsCard
                key={article.id}
                article={article}
                variant="horizontal-left"
                showDescription={true}
                locale="de"
                priority={index < 4}
                className="border border-border rounded-lg p-4"
              />
            ))}
          </div>
        </div>

        {/* Column 4: Trending Articles */}
        <div className="xl:col-span-1 hidden xl:block">
          <div className="space-y-4">
            {trendingArticles.map((article, index) => (
              <NewsCard
                key={article.id}
                article={article}
                variant="title-only"
                showDescription={true}
                locale="de"
                priority={index < 2}
                className="pb-4 border-b border-border last:border-b-0"
              />
            ))}
          </div>
        </div>
      </div>

      {/* Global Read More Button */}
      {hasMoreContent && (
        <div className="text-center mt-8 pt-6 border-t border-border">
          <button
            onClick={handleGlobalReadMore}
            disabled={isLoading}
            className="text-foreground hover:text-foreground/80 disabled:opacity-50 transition-colors duration-200 text-sm font-normal"
          >
            {isLoading ? 'Lade weitere Artikel...' : 'Mehr Artikel laden'}
          </button>
        </div>
      )}
    </section>
  );
}
