import NewsCard from '@/components/NewsCard';
import type { Article, Category } from '@/payload-types';
import { TriangleAlert } from 'lucide-react';

interface CategoryVerticalSectionProps {
  articles: Article[];
  categoryInfo: Category;
}

export default function CategoryVerticalSection({
  articles,
  categoryInfo,
}: CategoryVerticalSectionProps) {
  if (articles.length === 0) {
    return (
      <div className="text-center py-6">
        <div className="text-muted-foreground">
          <TriangleAlert className="size-4 mx-auto mb-3" />
          <p className="text-xs mb-1">Keine weiteren Artikel</p>
          <p className="text-xs">verfügbar</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3 md:space-y-4">
      {articles.map((article, index) => (
        <NewsCard
          key={article.id}
          article={article}
          variant="default"
          showDescription={true}
          locale="de"
          className={index === 0 ? 'border-t border-border pt-3' : ''}
        />
      ))}
    </div>
  );
}
