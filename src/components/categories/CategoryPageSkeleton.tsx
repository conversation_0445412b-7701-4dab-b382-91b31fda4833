import { CardSkeleton } from '@/components/NewsCard';

export default function CategoryPageSkeleton() {
  return (
    <div className="max-w-[1440px] mx-auto px-4 py-6 sm:py-8 md:py-10 lg:py-12">
      {/* Category Header Skeleton */}
      <div className="mb-6 md:mb-8 lg:mb-10">
        <div className="animate-pulse">
          <div className="h-8 md:h-10 lg:h-12 bg-gray-200 dark:bg-gray-800 rounded-md w-1/3 mb-2" />
          <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded-md w-1/4" />
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
        {/* Left: Hero + Featured Articles Skeleton */}
        <div className="lg:col-span-2 xl:col-span-3 space-y-6 md:space-y-8">
          {/* Hero Article Skeleton */}
          <div className="mb-6 md:mb-8">
            <CardSkeleton variant="default" />
          </div>

          {/* Featured Grid Skeleton */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6">
            {Array.from({ length: 4 }).map((_, i) => (
              <CardSkeleton key={i} variant="default" />
            ))}
          </div>

          {/* Additional Content Skeleton (Bottom Section) */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <CardSkeleton key={i} variant="title-only" />
            ))}
          </div>
        </div>

        {/* Right: Sidebar Skeleton */}
        <div className="lg:col-span-1 xl:col-span-1">
          <div className="space-y-1.5 md:space-y-2">
            {Array.from({ length: 8 }).map((_, i) => (
              <CardSkeleton key={i} variant="title-only" />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
