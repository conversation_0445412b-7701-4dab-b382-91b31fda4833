'use client';

import { useEffect, useRef } from 'react';
import Link from 'next/link';

interface CategoryAccessibilityNavProps {
  categorySlug: string;
  isLoading?: boolean;
  hasError?: boolean;
}

export default function CategoryAccessibilityNav({
  categorySlug,
  isLoading = false,
  hasError = false,
}: CategoryAccessibilityNavProps) {
  const liveRegionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!liveRegionRef.current) return;

    let message = '';
    if (isLoading) {
      message = 'Loading category content...';
    } else if (hasError) {
      message = 'Error loading category. Please try again.';
    } else {
      message = 'Category loaded successfully';
    }

    // Update live region with appropriate delay
    const timer = setTimeout(() => {
      if (liveRegionRef.current) {
        liveRegionRef.current.textContent = message;
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [isLoading, hasError]);

  // Handle skip to content functionality (removed event handlers to fix Next.js 15 serialization)
  // const handleSkipToContent = (targetId: string) => {
  //   const target = document.getElementById(targetId);
  //   if (target) {
  //     target.focus();
  //     target.scrollIntoView({ behavior: 'smooth', block: 'start' });
  //   }
  // };

  return (
    <>
      {/* Skip Links */}
      <nav
        className="sr-only focus-within:not-sr-only"
        aria-label="Skip navigation"
      >
        <div className="fixed top-0 left-0 z-50 bg-background border border-border p-4 m-4 rounded-md shadow-lg">
          <ul className="flex flex-col gap-2 text-sm">
            <li>
              <a
                href="#main-content"
                className="text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-2 py-1"
              >
                Skip to main content
              </a>
            </li>
            <li>
              <a
                href="#vertical-list-heading"
                className="text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-2 py-1"
              >
                Skip to article list
              </a>
            </li>
            <li>
              <a
                href="#title-only-heading"
                className="text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-2 py-1"
              >
                Skip to headlines
              </a>
            </li>
            <li>
              <Link
                href="/"
                className="text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-2 py-1"
              >
                Return to homepage
              </Link>
            </li>
          </ul>
        </div>
      </nav>

      {/* ARIA Live Region */}
      <div
        ref={liveRegionRef}
        className="sr-only"
        aria-live="polite"
        aria-atomic="true"
        role="status"
      />
    </>
  );
}
