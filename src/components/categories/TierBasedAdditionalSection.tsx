'use client';

import { useState, useEffect } from 'react';
import NewsCard from '@/components/NewsCard';
import LoadMoreButton from './LoadMoreButton';
import type { Article, Category } from '@/payload-types';

interface TierArticleGroup {
  articles: Article[];
  currentPage: number;
  hasNextPage: boolean;
  totalCount: number;
}

interface TierBasedAdditionalSectionProps {
  initialTier1Articles: Article[];
  initialTier2Articles: Article[];
  initialTier3Articles: Article[];
  categoryInfo: Category;
  tierStats: {
    tier1: number;
    tier2: number;
    tier3: number;
  };
}

export default function TierBasedAdditionalSection({
  initialTier1Articles,
  initialTier2Articles,
  initialTier3Articles,
  categoryInfo,
  tierStats,
}: TierBasedAdditionalSectionProps) {
  const [tier1Group, setTier1Group] = useState<TierArticleGroup>({
    articles: initialTier1Articles,
    currentPage: 1,
    hasNextPage: initialTier1Articles.length < tierStats.tier1,
    totalCount: tierStats.tier1,
  });

  const [tier2Group, setTier2Group] = useState<TierArticleGroup>({
    articles: initialTier2Articles,
    currentPage: 1,
    hasNextPage: initialTier2Articles.length < tierStats.tier2,
    totalCount: tierStats.tier2,
  });

  const [tier3Group, setTier3Group] = useState<TierArticleGroup>({
    articles: initialTier3Articles,
    currentPage: 1,
    hasNextPage: initialTier3Articles.length < tierStats.tier3,
    totalCount: tierStats.tier3,
  });

  const handleLoadMoreTier1 = (newArticles: Article[], newPage: number) => {
    setTier1Group(prev => ({
      ...prev,
      articles: [...prev.articles, ...newArticles],
      currentPage: newPage,
      hasNextPage: prev.articles.length + newArticles.length < prev.totalCount,
    }));
  };

  const handleLoadMoreTier2 = (newArticles: Article[], newPage: number) => {
    setTier2Group(prev => ({
      ...prev,
      articles: [...prev.articles, ...newArticles],
      currentPage: newPage,
      hasNextPage: prev.articles.length + newArticles.length < prev.totalCount,
    }));
  };

  const handleLoadMoreTier3 = (newArticles: Article[], newPage: number) => {
    setTier3Group(prev => ({
      ...prev,
      articles: [...prev.articles, ...newArticles],
      currentPage: newPage,
      hasNextPage: prev.articles.length + newArticles.length < prev.totalCount,
    }));
  };

  // Don't render if no articles at all
  if (
    tier1Group.articles.length === 0 &&
    tier2Group.articles.length === 0 &&
    tier3Group.articles.length === 0
  ) {
    return null;
  }

  return (
    <section
      className="mt-8 md:mt-10 lg:mt-12 pt-6 md:pt-8 border-t"
      aria-labelledby="tier-based-content-heading"
    >
      {/* Section Header */}
      <div className="flex items-center justify-between mb-6 md:mb-8">
        <h3
          id="tier-based-content-heading"
          className="font-serif text-lg/6 md:text-xl/7 font-normal text-foreground"
        >
          Weitere {categoryInfo.title}-Artikel
        </h3>
        <span className="font-sans text-xs text-muted-foreground">
          {tier1Group.totalCount +
            tier2Group.totalCount +
            tier3Group.totalCount}{' '}
          verfügbar
        </span>
      </div>

      {/* Tier-based Layout - matches the image structure */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
        {/* Left Column - Tier 3 Articles */}
        {tier3Group.articles.length > 0 && (
          <div className="space-y-6">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-3 h-3 bg-rose-500 rounded-full"></div>
              <h4 className="font-serif text-base font-medium text-rose-700">
                Tier 3 Artikel
              </h4>
            </div>

            <div className="space-y-4">
              {tier3Group.articles.map((article, index) => (
                <NewsCard
                  key={article.id}
                  article={article}
                  variant="title-only"
                  showDescription={true}
                  locale="de"
                  hideCategory={true}
                  priority={index < 3}
                  className="pb-3 border-b border-border last:border-b-0"
                />
              ))}
            </div>

            {/* Tier 3 Load More Button */}
            <LoadMoreButton
              tier="tier-3"
              categorySlug={categoryInfo.slug!}
              currentPage={tier3Group.currentPage}
              hasNextPage={tier3Group.hasNextPage}
              totalArticles={tier3Group.totalCount}
              loadedArticles={tier3Group.articles.length}
              onLoadMore={handleLoadMoreTier3}
            />
          </div>
        )}

        {/* Right Column - Tier 2 Articles */}
        {tier2Group.articles.length > 0 && (
          <div className="space-y-6">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-3 h-3 bg-amber-500 rounded-full"></div>
              <h4 className="font-serif text-base font-medium text-amber-700">
                Tier 2 Artikel
              </h4>
            </div>

            <div className="space-y-4">
              {tier2Group.articles.map((article, index) => (
                <NewsCard
                  key={article.id}
                  article={article}
                  variant="default"
                  showDescription={true}
                  locale="de"
                  hideCategory={true}
                  priority={index < 3}
                  className="shadow-sm"
                />
              ))}
            </div>

            {/* Tier 2 Load More Button */}
            <LoadMoreButton
              tier="tier-2"
              categorySlug={categoryInfo.slug!}
              currentPage={tier2Group.currentPage}
              hasNextPage={tier2Group.hasNextPage}
              totalArticles={tier2Group.totalCount}
              loadedArticles={tier2Group.articles.length}
              onLoadMore={handleLoadMoreTier2}
            />
          </div>
        )}
      </div>

      {/* Tier 1 Additional Articles - Full width at bottom */}
      {tier1Group.articles.length > 0 && (
        <div className="mt-8 pt-6 border-t border-border">
          <div className="flex items-center gap-2 mb-6">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <h4 className="font-serif text-base font-medium text-blue-700">
              Weitere Tier 1 Artikel
            </h4>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
            {tier1Group.articles.map((article, index) => (
              <NewsCard
                key={article.id}
                article={article}
                variant="default"
                showDescription={true}
                locale="de"
                hideCategory={true}
                priority={index < 6}
                className="shadow-sm border border-border"
              />
            ))}
          </div>

          {/* Tier 1 Load More Button */}
          <LoadMoreButton
            tier="tier-1"
            categorySlug={categoryInfo.slug!}
            currentPage={tier1Group.currentPage}
            hasNextPage={tier1Group.hasNextPage}
            totalArticles={tier1Group.totalCount}
            loadedArticles={tier1Group.articles.length}
            onLoadMore={handleLoadMoreTier1}
            className="mt-6"
          />
        </div>
      )}
    </section>
  );
}
