import NewsCard from '@/components/NewsCard';
import type { Article, Category } from '@/payload-types';

interface CategorySidebarProps {
  articles: Article[];
  categoryInfo: Category;
}

export default function CategorySidebar({
  articles,
  categoryInfo,
}: CategorySidebarProps) {
  if (articles.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-muted-foreground">
          <span className="size-4 mx-auto mb-3 block text-lg">📄</span>
          <p className="text-xs mb-2">
            Keine weiteren {categoryInfo.title}-Artikel
          </p>
          <p className="text-xs"><PERSON>hauen Si<PERSON> später noch einmal vorbei</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-1.5 md:space-y-2">
      {articles.map(article => (
        <NewsCard
          key={article.id}
          article={article}
          variant="title-only"
          showDescription={true}
          locale="de"
        />
      ))}
    </div>
  );
}
