'use client';

import React from 'react';
import { useRowLabel } from '@payloadcms/ui';
import type { RowLabelProps } from '@payloadcms/ui';

export const SectionRowLabel: React.FC<RowLabelProps> = () => {
  const data = useRowLabel<any>();

  const label = data?.data?.link?.label
    ? `Link ${data.rowNumber !== undefined ? data.rowNumber + 1 : ''}: ${data?.data?.link?.label}`
    : `Link ${data.rowNumber !== undefined ? data.rowNumber + 1 : ''}`;

  return <div>{label}</div>;
};
