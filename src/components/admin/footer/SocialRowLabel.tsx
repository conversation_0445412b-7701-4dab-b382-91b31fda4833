'use client';

import React from 'react';
import { useRowLabel } from '@payloadcms/ui';
import type { RowLabelProps } from '@payloadcms/ui';

export const SocialRowLabel: React.FC<RowLabelProps> = () => {
  const data = useRowLabel<any>();

  const platform = data?.data?.platform
    ? data.data.platform.charAt(0).toUpperCase() + data.data.platform.slice(1)
    : '';
  const label = data?.data?.label ? ` - ${data.data.label}` : '';

  const displayLabel = platform
    ? `${platform}${label}`
    : `Social Link ${data.rowNumber !== undefined ? data.rowNumber + 1 : ''}`;

  return <div>{displayLabel}</div>;
};
