import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

// ✅ Mock PayloadCMS hooks
vi.mock('@payloadcms/ui', () => ({
  useFormFields: vi.fn(),
  useAllFormFields: vi.fn(),
  useDocumentInfo: vi.fn(),
}));

// ✅ Mock validation service
const mockValidateArticleOperations = vi.fn();
vi.mock('../../../lib/services/article-validation', () => ({
  validateArticleOperations: mockValidateArticleOperations,
}));

// ✅ Mock notifications with individual functions
const mockShowEnhancementSuccess = vi.fn();
const mockShowTranslationSuccess = vi.fn();
const mockShowOperationError = vi.fn();
const mockShowValidationError = vi.fn();
const mockShowProcessingInfo = vi.fn();

vi.mock('../../notifications/ArticleNotifications', () => ({
  useArticleNotifications: () => ({
    showEnhancementSuccess: mockShowEnhancementSuccess,
    showTranslationSuccess: mockShowTranslationSuccess,
    showOperationError: mockShowOperationError,
    showValidationError: mockShowValidationError,
    showProcessingInfo: mockShowProcessingInfo,
  }),
}));

// Import after mocking
import { ArticleDocumentControls } from '../DocumentControls';

// Get mock functions from payloadcms/ui
const mockUseFormFields = vi.mocked(
  await import('@payloadcms/ui')
).useFormFields;
const mockUseAllFormFields = vi.mocked(
  await import('@payloadcms/ui')
).useAllFormFields;
const mockUseDocumentInfo = vi.mocked(
  await import('@payloadcms/ui')
).useDocumentInfo;

describe('DocumentControls - Sprint 6: Comprehensive Testing', () => {
  // ✅ Default mock fields structure
  const mockFields = {};
  const mockDispatchFields = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseDocumentInfo.mockReturnValue({
      id: 'test-article-id',
    } as any);
    mockUseAllFormFields.mockReturnValue([mockFields, mockDispatchFields]);

    // Default fetch mock
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: () =>
        Promise.resolve({
          success: true,
          message: 'Operation successful',
          data: {},
        }),
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  // ✅ Helper to setup article state
  const setupArticleState = (config: {
    articleType: 'generated' | 'curated';
    workflowStage?: string;
    hasBeenEnhanced?: boolean;
    hasGermanTranslation?: boolean;
    hasOriginalSource?: boolean;
    enhancedTitle?: string;
    enhancedSummary?: string;
    enhancedContent?: any;
    germanTitle?: string;
    germanContent?: any;
  }) => {
    const {
      articleType,
      workflowStage = articleType === 'generated'
        ? 'enhanced-draft'
        : 'curated-draft',
      hasBeenEnhanced = articleType === 'generated',
      hasGermanTranslation = false,
      hasOriginalSource = articleType === 'generated',
      enhancedTitle = '',
      enhancedSummary = '',
      enhancedContent = null,
      germanTitle = '',
      germanContent = null,
    } = config;

    // Mock useFormFields calls in order
    mockUseFormFields
      .mockReturnValueOnce('Test Article') // title
      .mockReturnValueOnce(articleType) // articleType
      .mockReturnValueOnce(workflowStage) // workflowStage
      .mockReturnValueOnce(hasBeenEnhanced) // hasBeenEnhanced
      .mockReturnValueOnce(enhancedTitle) // enhancedTitle
      .mockReturnValueOnce(enhancedSummary) // enhancedSummary
      .mockReturnValueOnce(enhancedContent) // enhancedContent
      .mockReturnValueOnce(germanTitle) // germanTitle
      .mockReturnValueOnce(germanContent) // germanContent
      .mockReturnValueOnce(hasGermanTranslation) // hasGermanTranslation
      .mockReturnValueOnce(hasOriginalSource); // hasOriginalSource

    // Mock validation results based on article state
    const canEnhance =
      articleType === 'curated' &&
      enhancedTitle.length >= 20 &&
      enhancedSummary.length >= 20 &&
      !!enhancedContent;

    const canTranslate =
      enhancedTitle.length >= 20 &&
      enhancedSummary.length >= 20 &&
      !!enhancedContent;

    mockValidateArticleOperations.mockReturnValue({
      enhancement: {
        isValid: canEnhance,
        errors: canEnhance ? [] : ['Invalid content for enhancement'],
        buttonText: hasBeenEnhanced ? 'Re-Enhance' : 'Enhance Content',
      },
      translation: {
        isValid: canTranslate,
        errors: canTranslate ? [] : ['Invalid content for translation'],
        buttonText: hasGermanTranslation
          ? 'Re-Translate'
          : 'Translate to German',
      },
      buttons: {
        showEnhanceButton: articleType === 'curated',
        showTranslateButton: true,
      },
      firstError:
        canEnhance && canTranslate ? null : 'Content requirements not met',
    });
  };

  describe('✅ Generated Article (Default)', () => {
    beforeEach(() => {
      setupArticleState({
        articleType: 'generated',
        enhancedTitle: 'Enhanced Title with enough characters for validation',
        enhancedSummary:
          'Enhanced summary with enough characters for validation requirements',
        enhancedContent: {
          root: {
            children: [
              {
                type: 'paragraph',
                children: [{ type: 'text', text: 'Valid content' }],
              },
            ],
          },
        },
      });
    });

    it('should hide enhance button for generated articles', () => {
      render(<ArticleDocumentControls />);
      expect(screen.queryByText(/enhance/i)).not.toBeInTheDocument();
    });

    it('should show active translate button when enhanced fields >20 chars', () => {
      render(<ArticleDocumentControls />);
      const translateButton = screen.getByText(/translate/i);
      expect(translateButton).toBeInTheDocument();
      expect(translateButton).not.toBeDisabled();
    });

    it('should show "Re-Translate" after translation', () => {
      setupArticleState({
        articleType: 'generated',
        hasGermanTranslation: true,
        enhancedTitle: 'Enhanced Title with enough characters',
        enhancedSummary: 'Enhanced summary with enough characters',
        enhancedContent: {
          root: {
            children: [
              {
                type: 'paragraph',
                children: [{ type: 'text', text: 'Content' }],
              },
            ],
          },
        },
        germanTitle: 'German Title',
        germanContent: {
          root: {
            children: [
              {
                type: 'paragraph',
                children: [{ type: 'text', text: 'German content' }],
              },
            ],
          },
        },
      });

      render(<ArticleDocumentControls />);
      expect(screen.getByText(/re-translate/i)).toBeInTheDocument();
    });
  });

  describe('✅ Generated → Curated Switch', () => {
    beforeEach(() => {
      setupArticleState({
        articleType: 'curated', // Switched to curated
        hasOriginalSource: true, // Converted from generated
        enhancedTitle: 'Enhanced Title with enough characters for validation',
        enhancedSummary:
          'Enhanced summary with enough characters for validation',
        enhancedContent: {
          root: {
            children: [
              {
                type: 'paragraph',
                children: [{ type: 'text', text: 'Valid content' }],
              },
            ],
          },
        },
      });
    });

    it('should show both buttons when switched to curated', () => {
      render(<ArticleDocumentControls />);
      expect(screen.getByText(/enhance/i)).toBeInTheDocument();
      expect(screen.getByText(/translate/i)).toBeInTheDocument();
    });

    it('should enable both buttons when validation passes', () => {
      render(<ArticleDocumentControls />);
      const enhanceButton = screen.getByText(/enhance/i);
      const translateButton = screen.getByText(/translate/i);
      expect(enhanceButton).not.toBeDisabled();
      expect(translateButton).not.toBeDisabled();
    });
  });

  describe('✅ New Curated Article', () => {
    beforeEach(() => {
      setupArticleState({
        articleType: 'curated',
        hasBeenEnhanced: false,
        hasOriginalSource: false, // New curated article
        enhancedTitle: 'Short', // <20 characters
        enhancedSummary: 'Short', // <20 characters
        enhancedContent: undefined,
      });
    });

    it('should show both buttons but disabled for insufficient content', () => {
      render(<ArticleDocumentControls />);
      const enhanceButton = screen.getByText(/enhance/i);
      const translateButton = screen.getByText(/translate/i);
      expect(enhanceButton).toBeInTheDocument();
      expect(translateButton).toBeInTheDocument();
      expect(enhanceButton).toBeDisabled();
      expect(translateButton).toBeDisabled();
    });
  });

  describe('✅ Curated - Valid Content', () => {
    beforeEach(() => {
      setupArticleState({
        articleType: 'curated',
        hasBeenEnhanced: false,
        hasOriginalSource: false,
        enhancedTitle:
          'Valid title with enough characters for validation requirements',
        enhancedSummary:
          'Valid summary with enough characters for validation requirements',
        enhancedContent: {
          root: {
            children: [
              {
                type: 'paragraph',
                children: [
                  { type: 'text', text: 'Valid content for processing' },
                ],
              },
            ],
          },
        },
      });
    });

    it('should enable both buttons when validation passes', () => {
      render(<ArticleDocumentControls />);
      const enhanceButton = screen.getByText(/enhance/i);
      const translateButton = screen.getByText(/translate/i);
      expect(enhanceButton).not.toBeDisabled();
      expect(translateButton).not.toBeDisabled();
    });

    it('should call enhance API when enhance button clicked', async () => {
      render(<ArticleDocumentControls />);
      const enhanceButton = screen.getByText(/enhance/i);

      fireEvent.click(enhanceButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/articles/enhance', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ articleId: 'test-article-id' }),
        });
      });
    });

    it('should call translate API when translate button clicked', async () => {
      render(<ArticleDocumentControls />);
      const translateButton = screen.getByText(/translate/i);

      fireEvent.click(translateButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/articles/translate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ articleId: 'test-article-id' }),
        });
      });
    });
  });

  describe('✅ Re-operation Button Text', () => {
    it('should show "Re-Enhance" for previously enhanced curated articles', () => {
      setupArticleState({
        articleType: 'curated',
        hasBeenEnhanced: true, // Previously enhanced
        enhancedTitle: 'Enhanced Title with enough characters',
        enhancedSummary: 'Enhanced summary with enough characters',
        enhancedContent: {
          root: {
            children: [
              {
                type: 'paragraph',
                children: [{ type: 'text', text: 'Content' }],
              },
            ],
          },
        },
      });

      render(<ArticleDocumentControls />);
      expect(screen.getByText(/re-enhance/i)).toBeInTheDocument();
    });

    it('should show "Re-Translate" for previously translated articles', () => {
      setupArticleState({
        articleType: 'curated',
        hasGermanTranslation: true, // Previously translated
        enhancedTitle: 'Enhanced Title with enough characters',
        enhancedSummary: 'Enhanced summary with enough characters',
        enhancedContent: {
          root: {
            children: [
              {
                type: 'paragraph',
                children: [{ type: 'text', text: 'Content' }],
              },
            ],
          },
        },
        germanTitle: 'German Title',
        germanContent: {
          root: {
            children: [
              {
                type: 'paragraph',
                children: [{ type: 'text', text: 'German content' }],
              },
            ],
          },
        },
      });

      render(<ArticleDocumentControls />);
      expect(screen.getByText(/re-translate/i)).toBeInTheDocument();
    });
  });

  describe('✅ Mutual Exclusion', () => {
    beforeEach(() => {
      setupArticleState({
        articleType: 'curated',
        enhancedTitle: 'Valid title with enough characters for validation',
        enhancedSummary: 'Valid summary with enough characters for validation',
        enhancedContent: {
          root: {
            children: [
              {
                type: 'paragraph',
                children: [{ type: 'text', text: 'Valid content' }],
              },
            ],
          },
        },
      });
    });

    it('should disable translate button when enhance is running', async () => {
      // Mock slow API response for enhancement
      global.fetch = vi.fn().mockImplementation(url => {
        if (url.includes('/api/articles/enhance')) {
          return new Promise(resolve =>
            setTimeout(
              () =>
                resolve({
                  ok: true,
                  json: () => Promise.resolve({ success: true, data: {} }),
                }),
              100
            )
          );
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ success: true, data: {} }),
        });
      });

      render(<ArticleDocumentControls />);
      const enhanceButton = screen.getByText(/enhance/i);
      const translateButton = screen.getByText(/translate/i);

      fireEvent.click(enhanceButton);

      // During enhancement, translate should be disabled
      await waitFor(() => {
        expect(translateButton).toBeDisabled();
      });
    });

    it('should disable enhance button when translate is running', async () => {
      // Mock slow API response for translation
      global.fetch = vi.fn().mockImplementation(url => {
        if (url.includes('/api/articles/translate')) {
          return new Promise(resolve =>
            setTimeout(
              () =>
                resolve({
                  ok: true,
                  json: () => Promise.resolve({ success: true, data: {} }),
                }),
              100
            )
          );
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ success: true, data: {} }),
        });
      });

      render(<ArticleDocumentControls />);
      const enhanceButton = screen.getByText(/enhance/i);
      const translateButton = screen.getByText(/translate/i);

      fireEvent.click(translateButton);

      // During translation, enhance should be disabled
      await waitFor(() => {
        expect(enhanceButton).toBeDisabled();
      });
    });
  });

  describe('✅ Error Handling', () => {
    beforeEach(() => {
      setupArticleState({
        articleType: 'curated',
        enhancedTitle: 'Valid title with enough characters',
        enhancedSummary: 'Valid summary with enough characters',
        enhancedContent: {
          root: {
            children: [
              {
                type: 'paragraph',
                children: [{ type: 'text', text: 'Content' }],
              },
            ],
          },
        },
      });
    });

    it('should handle API errors gracefully for enhancement', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        json: () =>
          Promise.resolve({
            success: false,
            error: 'Enhancement failed',
          }),
      });

      render(<ArticleDocumentControls />);
      const enhanceButton = screen.getByText(/enhance/i);

      fireEvent.click(enhanceButton);

      await waitFor(() => {
        expect(mockShowOperationError).toHaveBeenCalledWith(
          'enhancement',
          'Enhancement failed'
        );
      });
    });

    it('should handle network errors gracefully', async () => {
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));

      render(<ArticleDocumentControls />);
      const translateButton = screen.getByText(/translate/i);

      fireEvent.click(translateButton);

      await waitFor(() => {
        expect(mockShowOperationError).toHaveBeenCalledWith(
          'translation',
          'Failed to communicate with the translation service'
        );
      });
    });
  });

  describe('✅ Validation Messages', () => {
    it('should show save message when no article ID', () => {
      mockUseDocumentInfo.mockReturnValue({ id: undefined } as any);

      setupArticleState({
        articleType: 'curated',
        enhancedTitle: 'Short',
        enhancedSummary: 'Short',
        enhancedContent: undefined,
      });

      render(<ArticleDocumentControls />);

      // Both buttons should be hidden or disabled when no ID
      const buttons = screen.queryAllByRole('button');
      buttons.forEach(button => {
        expect(button).toBeDisabled();
      });
    });

    it('should provide clear validation messages for insufficient content', () => {
      setupArticleState({
        articleType: 'curated',
        enhancedTitle: 'Short', // <20 characters
        enhancedSummary: 'Also short', // <20 characters
        enhancedContent: undefined,
      });

      render(<ArticleDocumentControls />);

      const enhanceButton = screen.getByText(/enhance/i);
      const translateButton = screen.getByText(/translate/i);

      expect(enhanceButton).toBeDisabled();
      expect(translateButton).toBeDisabled();
    });
  });

  describe('✅ Form State Updates', () => {
    beforeEach(() => {
      setupArticleState({
        articleType: 'curated',
        enhancedTitle: 'Valid title with enough characters',
        enhancedSummary: 'Valid summary with enough characters',
        enhancedContent: {
          root: {
            children: [
              {
                type: 'paragraph',
                children: [{ type: 'text', text: 'Content' }],
              },
            ],
          },
        },
      });
    });

    it('should update form fields after successful enhancement', async () => {
      const mockEnhancementResponse = {
        success: true,
        message: 'Enhancement successful',
        data: {
          englishTab: {
            enhancedTitle: 'Enhanced Title Result',
            enhancedSummary: 'Enhanced Summary Result',
            enhancedContent: { enhanced: 'content' },
            enhancedKeyInsights: ['Insight 1', 'Insight 2'],
            keywords: ['keyword1', 'keyword2'],
          },
          workflowStage: 'enhanced-draft',
          hasBeenEnhanced: true,
        },
      };

      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockEnhancementResponse),
      });

      render(<ArticleDocumentControls />);
      const enhanceButton = screen.getByText(/enhance/i);

      fireEvent.click(enhanceButton);

      await waitFor(() => {
        // Verify form fields were updated with dispatchFields
        expect(mockDispatchFields).toHaveBeenCalledWith({
          type: 'UPDATE',
          path: 'englishTab.enhancedTitle',
          value: 'Enhanced Title Result',
        });

        expect(mockDispatchFields).toHaveBeenCalledWith({
          type: 'UPDATE',
          path: 'workflowStage',
          value: 'enhanced-draft',
        });
      });
    });

    it('should update form fields after successful translation', async () => {
      const mockTranslationResponse = {
        success: true,
        message: 'Translation successful',
        data: {
          germanTab: {
            germanTitle: 'German Title Result',
            germanSummary: 'German Summary Result',
            germanContent: { german: 'content' },
            germanKeyInsights: ['German Insight 1'],
            germanKeywords: ['deutsches_keyword'],
          },
          workflowStage: 'translated',
          hasGermanTranslation: true,
        },
      };

      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockTranslationResponse),
      });

      render(<ArticleDocumentControls />);
      const translateButton = screen.getByText(/translate/i);

      fireEvent.click(translateButton);

      await waitFor(() => {
        // Verify German tab fields were updated
        expect(mockDispatchFields).toHaveBeenCalledWith({
          type: 'UPDATE',
          path: 'germanTab.germanTitle',
          value: 'German Title Result',
        });

        expect(mockDispatchFields).toHaveBeenCalledWith({
          type: 'UPDATE',
          path: 'hasGermanTranslation',
          value: true,
        });
      });
    });
  });
});
