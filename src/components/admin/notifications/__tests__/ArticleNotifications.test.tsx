import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import { useArticleNotifications } from '../ArticleNotifications';

// Mock PayloadCMS toast
const mockToast = {
  success: vi.fn(),
  error: vi.fn(),
  warning: vi.fn(),
  info: vi.fn(),
};

vi.mock('@payloadcms/ui', () => ({
  toast: mockToast,
}));

describe('useArticleNotifications', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('showNotification', () => {
    it('should call toast with correct parameters for success notification', () => {
      const { result } = renderHook(() => useArticleNotifications());

      result.current.showNotification({
        type: 'success',
        message: 'Test success message',
        description: 'Test description',
        duration: 3000,
      });

      expect(mockToast.success).toHaveBeenCalledWith('Test success message', {
        description: 'Test description',
        duration: 3000,
      });
    });

    it('should call toast with correct parameters for error notification', () => {
      const { result } = renderHook(() => useArticleNotifications());

      result.current.showNotification({
        type: 'error',
        message: 'Test error message',
        description: 'Error details',
        duration: 6000,
      });

      expect(mockToast.error).toHaveBeenCalledWith('Test error message', {
        description: 'Error details',
        duration: 6000,
      });
    });

    it('should use default duration when not specified', () => {
      const { result } = renderHook(() => useArticleNotifications());

      result.current.showNotification({
        type: 'info',
        message: 'Test info message',
      });

      expect(mockToast.info).toHaveBeenCalledWith('Test info message', {
        description: undefined,
        duration: 5000,
      });
    });
  });

  describe('showEnhancementSuccess', () => {
    it('should show correct message for first-time enhancement', () => {
      const { result } = renderHook(() => useArticleNotifications());

      result.current.showEnhancementSuccess(false);

      expect(mockToast.success).toHaveBeenCalledWith(
        'Content Enhanced Successfully!',
        {
          description:
            'Your article content has been enhanced with AI and is ready for review.',
          duration: 4000,
        }
      );
    });

    it('should show correct message for re-enhancement', () => {
      const { result } = renderHook(() => useArticleNotifications());

      result.current.showEnhancementSuccess(true);

      expect(mockToast.success).toHaveBeenCalledWith(
        'Content Enhanced Successfully!',
        {
          description:
            'Your article has been re-enhanced and is ready for review.',
          duration: 4000,
        }
      );
    });
  });

  describe('showTranslationSuccess', () => {
    it('should show correct message for first-time translation', () => {
      const { result } = renderHook(() => useArticleNotifications());

      result.current.showTranslationSuccess(false);

      expect(mockToast.success).toHaveBeenCalledWith('Translation Completed!', {
        description:
          'Your article has been translated to German and is ready for review.',
        duration: 4000,
      });
    });

    it('should show correct message for re-translation', () => {
      const { result } = renderHook(() => useArticleNotifications());

      result.current.showTranslationSuccess(true);

      expect(mockToast.success).toHaveBeenCalledWith('Translation Completed!', {
        description:
          'Your article has been re-translated to German and is ready for review.',
        duration: 4000,
      });
    });
  });

  describe('showOperationError', () => {
    it('should show correct error message for enhancement failure', () => {
      const { result } = renderHook(() => useArticleNotifications());

      result.current.showOperationError(
        'enhancement',
        'AI service unavailable'
      );

      expect(mockToast.error).toHaveBeenCalledWith('Enhancement Failed', {
        description: 'AI service unavailable',
        duration: 6000,
      });
    });

    it('should show correct error message for translation failure', () => {
      const { result } = renderHook(() => useArticleNotifications());

      result.current.showOperationError('translation');

      expect(mockToast.error).toHaveBeenCalledWith('Translation Failed', {
        description:
          'Failed to translate article. Please try again or contact support if the issue persists.',
        duration: 6000,
      });
    });

    it('should use default error message when no specific error provided', () => {
      const { result } = renderHook(() => useArticleNotifications());

      result.current.showOperationError('enhancement');

      expect(mockToast.error).toHaveBeenCalledWith('Enhancement Failed', {
        description:
          'Failed to enhance article. Please try again or contact support if the issue persists.',
        duration: 6000,
      });
    });
  });

  describe('showValidationError', () => {
    it('should show validation warning with correct message', () => {
      const { result } = renderHook(() => useArticleNotifications());

      result.current.showValidationError(
        'Title must be at least 20 characters'
      );

      expect(mockToast.warning).toHaveBeenCalledWith('Validation Required', {
        description: 'Title must be at least 20 characters',
        duration: 4000,
      });
    });
  });

  describe('showProcessingInfo', () => {
    it('should show processing info for enhancement', () => {
      const { result } = renderHook(() => useArticleNotifications());

      result.current.showProcessingInfo('enhancement');

      expect(mockToast.info).toHaveBeenCalledWith('Enhancement in Progress', {
        description:
          'Your article is being enhanced. This may take a few moments...',
        duration: 3000,
      });
    });

    it('should show processing info for translation', () => {
      const { result } = renderHook(() => useArticleNotifications());

      result.current.showProcessingInfo('translation');

      expect(mockToast.info).toHaveBeenCalledWith('Translation in Progress', {
        description:
          'Your article is being translated. This may take a few moments...',
        duration: 3000,
      });
    });
  });

  describe('hook stability', () => {
    it('should return stable function references across re-renders', () => {
      const { result, rerender } = renderHook(() => useArticleNotifications());

      const firstRenderFunctions = result.current;
      rerender();
      const secondRenderFunctions = result.current;

      // Verify all functions are stable (same reference)
      expect(firstRenderFunctions.showNotification).toBe(
        secondRenderFunctions.showNotification
      );
      expect(firstRenderFunctions.showEnhancementSuccess).toBe(
        secondRenderFunctions.showEnhancementSuccess
      );
      expect(firstRenderFunctions.showTranslationSuccess).toBe(
        secondRenderFunctions.showTranslationSuccess
      );
      expect(firstRenderFunctions.showOperationError).toBe(
        secondRenderFunctions.showOperationError
      );
      expect(firstRenderFunctions.showValidationError).toBe(
        secondRenderFunctions.showValidationError
      );
      expect(firstRenderFunctions.showProcessingInfo).toBe(
        secondRenderFunctions.showProcessingInfo
      );
    });
  });
});
