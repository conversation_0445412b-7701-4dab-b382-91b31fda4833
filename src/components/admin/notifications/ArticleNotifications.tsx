'use client';

import React, { useCallback } from 'react';
import { toast } from '@payloadcms/ui';

export interface NotificationOptions {
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  description?: string;
  duration?: number;
}

export const useArticleNotifications = () => {
  const showNotification = useCallback((options: NotificationOptions) => {
    const { type, message, description, duration = 5000 } = options;

    toast[type](message, {
      description,
      duration,
    });
  }, []);

  const showEnhancementSuccess = useCallback(
    (hasBeenEnhanced: boolean) => {
      showNotification({
        type: 'success',
        message: 'Content Enhanced Successfully!',
        description: hasBeenEnhanced
          ? 'Your article has been re-enhanced and is ready for review.'
          : 'Your article content has been enhanced with AI and is ready for review.',
        duration: 4000,
      });
    },
    [showNotification]
  );

  const showTranslationSuccess = useCallback(
    (hasGermanTranslation: boolean) => {
      showNotification({
        type: 'success',
        message: 'Translation Completed!',
        description: hasGermanTranslation
          ? 'Your article has been re-translated to German and is ready for review.'
          : 'Your article has been translated to German and is ready for review.',
        duration: 4000,
      });
    },
    [showNotification]
  );

  const showOperationError = useCallback(
    (operation: 'enhancement' | 'translation', error?: string) => {
      showNotification({
        type: 'error',
        message: `${operation === 'enhancement' ? 'Enhancement' : 'Translation'} Failed`,
        description:
          error ||
          `Failed to ${operation === 'enhancement' ? 'enhance' : 'translate'} article. Please try again or contact support if the issue persists.`,
        duration: 6000,
      });
    },
    [showNotification]
  );

  const showValidationError = useCallback(
    (message: string) => {
      showNotification({
        type: 'warning',
        message: 'Validation Required',
        description: message,
        duration: 4000,
      });
    },
    [showNotification]
  );

  const showProcessingInfo = useCallback(
    (operation: 'enhancement' | 'translation') => {
      showNotification({
        type: 'info',
        message: `${operation === 'enhancement' ? 'Enhancement' : 'Translation'} in Progress`,
        description: `Your article is being ${operation === 'enhancement' ? 'enhanced' : 'translated'}. This may take a few moments...`,
        duration: 3000,
      });
    },
    [showNotification]
  );

  return {
    showNotification,
    showEnhancementSuccess,
    showTranslationSuccess,
    showOperationError,
    showValidationError,
    showProcessingInfo,
  };
};
