/**
 * Page Document Controls Component
 *
 * Provides translation functionality specifically for Pages collection
 * using the generic TranslationControls component. This is a thin wrapper
 * that configures the generic component for Pages-specific needs.
 *
 * <AUTHOR> Blick Development Team
 * @created 2025-01-27
 */

'use client';

import React from 'react';
import { useDocumentInfo } from '@payloadcms/ui';
import { TranslationControls } from '../shared/TranslationControls';

/**
 * Page-specific validation context
 */
interface PageValidationContext {
  collection: 'pages';
  fields: {
    title: string;
    content: any;
    summary?: string;
    hasGermanTranslation: boolean;
  };
}

/**
 * Additional validation rules specific to Pages (optional - main validation is in TranslationControls)
 */
function validatePageForTranslation(context: any): {
  isValid: boolean;
  errors: string[];
} {
  // No additional validation needed - TranslationControls handles all validation
  // This function is kept for consistency but delegates to the main validation logic
  return {
    isValid: true,
    errors: [],
  };
}

/**
 * Handle translation completion for Pages
 */
function handlePageTranslationComplete(result: any): void {
  console.log('✅ Page translation completed:', {
    success: result.success,
    hasGermanContent: !!result.data?.germanTab?.germanTitle,
    processingTime: result.metrics?.processingTime,
  });

  // Pages don't have workflow stages like Articles, so no additional processing needed
  // The generic component handles all the form updates
}

/**
 * Page Document Controls Component
 */
export const PageDocumentControls: React.FC = () => {
  console.log('🔧 PageDocumentControls component is loading...');

  const docInfo = useDocumentInfo();
  const { id } = docInfo;

  console.log('📄 Document info:', { id, docInfo });

  // Clean, minimal rendering - just the translation controls
  return (
    <div>
      {!id ? (
        <div
          style={{
            padding: 'var(--base-half, 0.5rem)',
            color: 'var(--theme-color-text-disabled, #9ca3af)',
            fontSize: 'var(--font-size-xs, 0.75rem)',
            textAlign: 'center',
          }}
        >
          Save the document first to enable translation
        </div>
      ) : (
        <TranslationControls
          collection="pages"
          apiEndpoint="/api/pages/translate"
          additionalValidation={validatePageForTranslation}
          onTranslationComplete={handlePageTranslationComplete}
        />
      )}
    </div>
  );
};

export default PageDocumentControls;
