'use client';

import React from 'react';
import { useDocumentInfo, useAllFormFields } from '@payloadcms/ui';

// Export as named export to match the #PageViewControls syntax in collection config
export const PageViewControls: React.FC = () => {
  const docInfo = useDocumentInfo();
  const { id } = docInfo;
  const [fields] = useAllFormFields();

  // Get slug, status, and parent from form fields
  const slug = fields?.slug?.value;
  const status = fields?._status?.value;
  const parentField = fields?.parent;

  // Don't show if no slug
  if (!slug) {
    return null;
  }

  const isPublished = status === 'published';

  // Build hierarchical URL path based on parent
  let urlPath = `/${slug}`;

  // Check for parent relationship - it can be in different formats
  const parentValue = parentField?.value;

  if (parentValue) {
    let parentSlug = null;

    if (typeof parentValue === 'object') {
      // Handle relationship field structure: { relationTo: 'pages', value: { slug: 'parent-slug' } }
      if (
        'value' in parentValue &&
        parentValue.value &&
        typeof parentValue.value === 'object' &&
        'slug' in parentValue.value
      ) {
        parentSlug = parentValue.value.slug as string;
      }
      // Handle direct object: { slug: 'parent-slug' }
      else if ('slug' in parentValue) {
        parentSlug = parentValue.slug as string;
      }
    }

    if (parentSlug) {
      urlPath = `/${parentSlug}/${slug}`;
    }
  }

  const liveUrl = `${process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000'}${urlPath}`;

  // Only show for published pages
  if (!isPublished) {
    return null;
  }

  return (
    <div style={{ marginBottom: '20px' }}>
      <span>Live URL:</span>{' '}
      <a href={liveUrl} target="_blank" rel="noopener noreferrer">
        {liveUrl}
      </a>
    </div>
  );
};

export default PageViewControls;
