import NewsCard from '@/components/NewsCard';
import type { Article } from '@/payload-types';

interface RelatedArticlesProps {
  articles: Article[];
  currentArticleId?: string | number;
  maxArticles?: number;
  locale?: 'de' | 'en';
}

export default function RelatedArticles({
  articles,
  currentArticleId,
  maxArticles = 4,
  locale = 'de',
}: RelatedArticlesProps) {
  // Filter out the current article and limit the number of articles
  const relatedArticles = articles
    .filter(article => article.id !== currentArticleId)
    .slice(0, maxArticles);

  // If no articles available, show placeholder
  if (relatedArticles.length === 0) {
    return (
      <div className="space-y-4">
        <h3 className="font-roboto font-medium text-gray-900 dark:text-gray-100 text-sm pl-4">
          Verwandte Artikel
        </h3>
        <div className="text-gray-500 dark:text-gray-400 text-sm italic pl-4">
          Derzeit keine verwandten Artikel verfügbar
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2 sm:space-y-2.5 font-sans">
      <h3 className="font-sans font-medium text-gray-900 dark:text-gray-100 text-sm pl-4">
        Verwandte Artikel
      </h3>

      {/* Related articles list using NewsCard */}
      <div className="space-y-1.5 md:space-y-2">
        {relatedArticles.map(article => (
          <NewsCard
            key={article.id}
            article={article}
            variant="title-only"
            showDescription={true}
            locale={locale}
          />
        ))}
      </div>

      {/* Show more indicator if there are additional articles */}
      {articles.length > maxArticles && (
        <div className="pt-2 text-xs text-gray-500 dark:text-gray-400 font-sans pl-4">
          +{articles.length - maxArticles} weitere verwandte Artikel
        </div>
      )}
    </div>
  );
}
