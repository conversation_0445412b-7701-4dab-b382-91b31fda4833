import RelatedArticles from './RelatedArticles';
import { getCachedRelatedArticles } from '@/lib/queries/related-articles';
import type { Article } from '@/payload-types';

interface RelatedArticlesSectionProps {
  currentArticle: Article;
  maxArticles?: number;
  locale?: 'de' | 'en';
}

export default async function RelatedArticlesSection({
  currentArticle,
  maxArticles = 4,
  locale = 'de',
}: RelatedArticlesSectionProps) {
  // Fetch related articles using the proper algorithm
  const getRelatedArticlesFunc = getCachedRelatedArticles(currentArticle.id);
  const relatedArticles = await getRelatedArticlesFunc(currentArticle, {
    limit: maxArticles,
  }).catch(() => []);

  return (
    <RelatedArticles
      articles={relatedArticles}
      currentArticleId={currentArticle.id}
      maxArticles={maxArticles}
      locale={locale}
    />
  );
}
