interface RelatedArticlesSkeletonProps {
  maxArticles?: number;
}

export default function RelatedArticlesSkeleton({
  maxArticles = 4,
}: RelatedArticlesSkeletonProps) {
  return (
    <div className="space-y-2 sm:space-y-2.5 font-sans animate-pulse">
      <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-28" />

      {/* Related articles list skeleton */}
      <div className="space-y-1.5 md:space-y-2">
        {Array.from({ length: maxArticles }).map((_, index) => (
          <div key={index} className="space-y-2 p-4">
            {/* Category badge skeleton */}
            <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded w-16" />

            {/* Title skeleton - multiple lines */}
            <div className="space-y-1">
              <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-full" />
              <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-4/5" />
            </div>

            {/* Description skeleton */}
            <div className="space-y-1">
              <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded w-full" />
              <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded w-3/4" />
            </div>

            {/* Footer metadata skeleton */}
            <div className="flex items-center gap-4 pt-2">
              <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded w-12" />
              <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded w-16" />
              <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded w-14" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
