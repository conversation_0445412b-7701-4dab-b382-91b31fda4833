import type { Article } from '@/payload-types';

interface ArticleMetadataProps {
  article: Article;
}

// Company metadata interface based on the Articles collection structure
interface CompanyData {
  name: string;
  ticker?: string;
  exchange?: string;
  relevance?: 'high' | 'medium' | 'low';
  confidence?: number;
}

export default function ArticleMetadata({ article }: ArticleMetadataProps) {
  // Get all related companies
  const companies =
    Array.isArray(article.relatedCompanies) &&
    article.relatedCompanies.length > 0
      ? (article.relatedCompanies as CompanyData[])
      : [];

  // If no companies are available, show a placeholder
  if (companies.length === 0) {
    return (
      <div className="p-4 border border-border rounded-lg">
        <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
          Unternehmensinformationen
        </h3>
        <div className="text-sm text-gray-500 dark:text-gray-400 italic">
          Keine Unternehmensinformationen verfügbar
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 border border-border rounded-lg font-sans">
      <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
        {companies.length === 1
          ? 'Unternehmensinformationen'
          : 'Verwandte Unternehmen'}
      </h3>

      <div className="flex flex-col gap-4">
        {companies.map((company: CompanyData, index: number) => (
          <div
            key={index}
            className={`${index > 0 ? 'pt-4 border-t border-border' : ''}`}
          >
            <div className="flex flex-col gap-3">
              {/* Company Name */}
              <div>
                <dt className="text-xs text-gray-500 dark:text-gray-400 font-roboto">
                  Unternehmen
                </dt>
                <dd className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {company.name}
                </dd>
              </div>

              {/* Ticker Symbol */}
              {company.ticker &&
                company.ticker !== '/' &&
                company.ticker !== '' &&
                company.ticker.trim() !== '' && (
                  <div>
                    <dt className="text-xs text-gray-500 dark:text-gray-400 font-roboto">
                      Ticker
                    </dt>
                    <dd className="text-sm font-mono text-gray-900 dark:text-gray-100">
                      {company.ticker}
                      {company.exchange &&
                        company.exchange !== '/' &&
                        company.exchange !== '' &&
                        company.exchange.trim() !== '' && (
                          <span className="text-gray-500 dark:text-gray-400 ml-1">
                            ({company.exchange})
                          </span>
                        )}
                    </dd>
                  </div>
                )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
