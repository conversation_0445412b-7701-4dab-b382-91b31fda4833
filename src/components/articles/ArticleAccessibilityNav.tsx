'use client';

import { useEffect, useRef } from 'react';
import Link from 'next/link';

interface ArticleAccessibilityNavProps {
  isLoading?: boolean;
  hasError?: boolean;
}

export default function ArticleAccessibilityNav({
  isLoading = false,
  hasError = false,
}: ArticleAccessibilityNavProps) {
  const liveRegionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!liveRegionRef.current) return;

    let message = '';
    if (isLoading) {
      message = 'Loading article content...';
    } else if (hasError) {
      message = 'Error loading article. Please try again.';
    } else {
      message = 'Article loaded successfully';
    }

    // Update live region with appropriate delay
    const timer = setTimeout(() => {
      if (liveRegionRef.current) {
        liveRegionRef.current.textContent = message;
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [isLoading, hasError]);

  // Handle skip to content functionality
  const handleSkipToContent = (targetId: string) => {
    const target = document.getElementById(targetId);
    if (target) {
      target.focus();
      target.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  return (
    <>
      {/* Skip Links */}
      <nav
        className="sr-only focus-within:not-sr-only"
        aria-label="Skip navigation"
      >
        <div className="fixed top-0 left-0 z-50 bg-background border border-border p-4 m-4 rounded-md shadow-lg">
          <ul className="flex flex-col gap-2 text-sm">
            <li>
              <a
                href="#article-content"
                className="text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-2 py-1"
                onClick={e => {
                  e.preventDefault();
                  handleSkipToContent('article-content');
                }}
              >
                Skip to article content
              </a>
            </li>
            <li>
              <a
                href="#article-metadata"
                className="text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-2 py-1"
                onClick={e => {
                  e.preventDefault();
                  handleSkipToContent('article-metadata');
                }}
              >
                Skip to article details
              </a>
            </li>
            <li>
              <a
                href="#related-articles"
                className="text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-2 py-1"
                onClick={e => {
                  e.preventDefault();
                  handleSkipToContent('related-articles');
                }}
              >
                Skip to related articles
              </a>
            </li>
            <li>
              <Link
                href="/"
                className="text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-2 py-1"
              >
                Return to homepage
              </Link>
            </li>
          </ul>
        </div>
      </nav>

      {/* ARIA Live Region */}
      <div
        ref={liveRegionRef}
        className="sr-only"
        aria-live="polite"
        aria-atomic="true"
        role="status"
      />
    </>
  );
}
