import { Clock } from 'lucide-react';
import type { Article, Category } from '@/payload-types';
import { formatReadingTime } from '@/lib/utils/readtime'; // ✅ PHASE 2: Use readtime utility

interface ArticleHeaderProps {
  article: Article;
  locale?: 'de' | 'en';
}

// ✅ PHASE 2: Removed calculateReadTime - now using pre-computed readTimeMinutes field

export default function ArticleHeader({
  article,
  locale = 'de',
}: ArticleHeaderProps) {
  // Get the first category (can be number ID or Category object)
  const firstCategory =
    Array.isArray(article.categories) && article.categories.length > 0
      ? typeof article.categories[0] === 'object'
        ? (article.categories[0] as Category)
        : null
      : null;

  // Prioritise German content first, then English, then fallback
  const title =
    article.germanTab?.germanTitle ||
    article.englishTab?.enhancedTitle ||
    article.title;

  // Calculate reading time from available content
  const content =
    article.germanTab?.germanContent ||
    article.englishTab?.enhancedContent ||
    null;

  // ✅ PHASE 2: Use pre-computed readTimeMinutes field instead of calculating from content
  const readTimeMinutes = (article as any).readTimeMinutes; // Temp: until types regenerated
  const readTime = readTimeMinutes
    ? formatReadingTime(readTimeMinutes, locale)
    : '5 Min. Lesezeit';

  // Format publication date in German style
  const publishDate = new Date(article.publishedAt || article.createdAt);

  // Format date in German locale
  const weekday = publishDate.toLocaleDateString('de-DE', { weekday: 'long' });
  const day = publishDate.getDate();
  const month = publishDate.toLocaleDateString('de-DE', { month: 'long' });
  const year = publishDate.getFullYear();

  const formattedDate = `${weekday}, ${day}. ${month} ${year}`;

  return (
    <div className="space-y-4">
      {/* Category Badge - NO pill background, just text */}
      <span className="text-xs font-medium text-[#B08D57] dark:text-[#D4AF37] font-sans">
        {firstCategory?.title || 'Technologie'}
      </span>

      {/* Large serif title */}
      <h1 className="text-xl md:text-2xl lg:text-3xl font-serif font-normal text-gray-900 dark:text-gray-100 leading-tight">
        {title}
      </h1>

      {/* Date and read time on same line */}
      <div className="flex items-center justify-between text-xs/4 text-gray-600 dark:text-gray-400">
        {/* Publication Date */}
        <span className="font-sans">{formattedDate}</span>

        {/* Reading Time */}
        <div className="flex items-center gap-2">
          <Clock className="h-4 w-4" />
          <span className="font-sans">{readTime}</span>
        </div>
      </div>
    </div>
  );
}
