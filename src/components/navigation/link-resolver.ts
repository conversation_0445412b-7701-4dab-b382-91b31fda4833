import type { NavigationItem, HeaderNavItem } from './types';

/**
 * Transform PayloadCMS Header navItems into NavigationItem format
 */
export function resolveNavigationLinks(
  navItems: HeaderNavItem[]
): NavigationItem[] {
  return navItems.map(item => ({
    id: item.id || crypto.randomUUID(),
    label: item.link.label,
    href:
      item.link.type === 'reference'
        ? resolveInternalLink(item.link.reference)
        : item.link.url || '#',
    isExternal: item.link.type === 'custom' && isExternalUrl(item.link.url),
    openInNewTab: item.link.newTab || false,
  }));
}

/**
 * Resolve internal PayloadCMS references to URL paths
 */
function resolveInternalLink(reference: any): string {
  if (!reference) return '#';

  switch (reference.relationTo) {
    case 'pages':
      return reference.value.slug === 'home' ? '/' : `/${reference.value.slug}`;
    case 'articles':
      return `/articles/${reference.value.slug}`;
    case 'categories':
      return `/categories/${reference.value.slug}`;
    default:
      return '#';
  }
}

/**
 * Check if a URL is external
 */
function isExternalUrl(url: string | null | undefined): boolean {
  if (!url) return false;

  try {
    const urlObj = new URL(url, window.location.origin);
    return urlObj.origin !== window.location.origin;
  } catch {
    // If URL parsing fails, treat as internal
    return false;
  }
}

/**
 * Fallback navigation for errors or empty data
 */
export function getFallbackNavigation(): NavigationItem[] {
  return [
    {
      id: '1',
      label: 'Home',
      href: '/',
      isExternal: false,
      openInNewTab: false,
    },
    {
      id: '2',
      label: 'Articles',
      href: '/articles',
      isExternal: false,
      openInNewTab: false,
    },
  ];
}
