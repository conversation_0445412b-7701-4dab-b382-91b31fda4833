# Navigation System Documentation

## Overview

The Navigation System provides a responsive, PayloadCMS-integrated navigation bar for the Börsen Blick website. It supports both desktop and mobile layouts with different UX patterns.

## Architecture

### Component Structure

```
navigation/
├── types.ts                 # TypeScript interfaces
├── navigation-client.tsx    # Client-side navigation component
├── header-navigation.tsx    # Server wrapper for PayloadCMS integration
├── link-resolver.ts         # PayloadCMS data transformation
├── __tests__/              # Integration tests
└── README.md               # This file
```

### Data Flow

1. **Header Global** (PayloadCMS) → 2. **Link Resolver** → 3. **Navigation Component** → 4. **Browser**

## Components

### HeaderNavigation (Server Component)

**Purpose**: Fetches navigation data from PayloadCMS Header global and passes it to the client component.

**Features**:

- PayloadCMS integration with depth 1 for reference population
- Error handling with fallback navigation
- Comprehensive logging for debugging

**Usage**:

```tsx
import HeaderNavigation from '@/components/navigation/header-navigation';

export default function Layout() {
  return <HeaderNavigation currentPath="/current-page" />;
}
```

### NavigationClient (Client Component)

**Purpose**: Renders the actual navigation UI with responsive behaviour.

**Features**:

- **Desktop**: Evenly spaced navigation with theme toggle
- **Mobile**: Horizontal scroll navigation (no theme toggle)
- Active state highlighting
- External link handling (`target="_blank"`)
- Accessibility support (focus states, proper semantics)

**Props**:

```tsx
interface NavigationClientProps {
  items: NavigationItem[];
  currentPath?: string;
}
```

### Link Resolver

**Purpose**: Transforms PayloadCMS link field data into standardised NavigationItem format.

**Features**:

- Internal link resolution (pages, articles, categories)
- External URL detection and handling
- Fallback handling for malformed data
- Server-side safe URL parsing

**Methods**:

- `resolveNavigationLinks()` - Main transformation function
- `getFallbackNavigation()` - Emergency fallback items

## Type System

### NavigationItem

```tsx
interface NavigationItem {
  id: string;
  label: string;
  href: string;
  isExternal: boolean;
  openInNewTab: boolean;
}
```

### HeaderNavItem (PayloadCMS)

Matches the PayloadCMS Header global `navItems` field structure:

```tsx
interface HeaderNavItem {
  id?: string;
  link: {
    type: 'reference' | 'custom';
    newTab?: boolean;
    reference?: PayloadReference;
    url?: string;
    label: string;
  };
}
```

## PayloadCMS Integration

### Header Global Configuration

The navigation expects a PayloadCMS Header global with the following structure:

```javascript
// In your Header global config
fields: [
  {
    name: 'navItems',
    type: 'array',
    fields: [
      {
        name: 'link',
        type: 'link', // Custom link field
        required: true,
      },
    ],
  },
];
```

### Link Field Support

Supports both reference links and custom URLs:

- **Reference Links**: `pages`, `articles`, `categories`
- **Custom URLs**: Any valid URL (automatically detected as external)
- **New Tab**: Configurable per link

## Responsive Behavior

### Desktop (md and up)

- Evenly spaced navigation items using `justify-between`
- Theme toggle visible on the right
- Hover states and focus indicators

### Mobile (below md)

- Horizontal scrolling navigation with `overflow-x-auto`
- Theme toggle hidden (will be in footer)
- Touch-friendly spacing with `gap-3`
- Scrollbar hidden with `scrollbar-hide` utility

## Styling

### Tailwind Classes Used

- **Layout**: `flex`, `items-center`, `gap-3`, `overflow-x-auto`
- **Typography**: `text-sm`, `font-medium`
- **States**: `text-foreground`, `text-muted-foreground`
- **Responsiveness**: `hidden md:flex`, `min-w-0`
- **Accessibility**: `focus:outline-none`, `focus:ring-2`

### Custom Utilities

```css
/* Added to global.css */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
```

## Error Handling

### Fallback Strategy

1. **Primary**: PayloadCMS Header global data
2. **Fallback**: Predefined navigation items (Home, Articles)
3. **Error Logging**: Console warnings for debugging

### Error Scenarios

- PayloadCMS unavailable
- Header global not configured
- Empty navigation items
- Malformed link data

## Testing

### Unit Tests

Located in `__tests__/link-resolver.test.ts`:

- Internal link resolution
- External URL handling
- Error handling for malformed data
- Fallback navigation validation

### Manual Testing

1. **Desktop Navigation**: Check layout and theme toggle
2. **Mobile Navigation**: Test horizontal scroll
3. **PayloadCMS Integration**: Add/remove nav items in admin
4. **Error Handling**: Test with empty/malformed data

## Performance

### Optimizations

- Server-side data fetching (no client-side loading states)
- Cached navigation data via PayloadCMS's built-in caching
- Minimal JavaScript bundle (only interactive features)

### Considerations

- Navigation data fetched on each page load (consider adding unstable_cache)
- Link resolution happens server-side (reduces client bundle)

## Future Enhancements

### Phase 3 Considerations

- Add unstable_cache wrapper for navigation data
- Implement dynamic active state detection
- Add keyboard navigation support
- Consider sub-navigation support

### Potential Features

- Breadcrumb integration
- Search integration
- User-specific navigation items
- Analytics tracking for navigation usage

## Troubleshooting

### Common Issues

1. **Theme toggle not showing**: Check `hidden md:flex` classes
2. **Horizontal scroll on full page**: Ensure `overflow-hidden` on container
3. **PayloadCMS data not loading**: Check server logs for fetch errors
4. **Links not resolving**: Verify reference depth and collection slugs

### Debug Steps

1. Check console for warnings/errors
2. Verify Header global has navItems configured
3. Test with fallback navigation (comment out PayloadCMS fetch)
4. Check network tab for API requests

## Integration Examples

### Basic Layout Integration

```tsx
// src/app/(frontend)/layout.tsx
import HeaderNavigation from '@/components/navigation/header-navigation';

export default function Layout({ children }) {
  return (
    <div className="min-h-screen flex flex-col">
      <header className="border-b">
        <HeaderNavigation />
      </header>
      <main className="flex-1">{children}</main>
    </div>
  );
}
```

### With Current Path Detection

```tsx
// Use Next.js pathname detection
import { headers } from 'next/headers';

export default function Layout({ children }) {
  const headersList = headers();
  const pathname = headersList.get('x-pathname') || '/';

  return <HeaderNavigation currentPath={pathname} />;
}
```
