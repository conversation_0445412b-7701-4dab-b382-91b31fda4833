'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { ModeToggle } from '@/components/ui/mode-toggle';
import type { NavigationItem } from './types';

interface NavigationClientProps {
  items: NavigationItem[];
}

export function NavigationClient({ items }: NavigationClientProps) {
  const pathname = usePathname();
  const [isMounted, setIsMounted] = useState(false);

  // Ensure hydration safety
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const isActiveLink = (href: string): boolean => {
    // Don't calculate active state until hydrated
    if (!isMounted || !pathname) return false;
    if (href === '/' && pathname === '/') return true;
    if (href === '/') return false;
    return pathname.startsWith(href);
  };

  return (
    <nav className="flex w-full items-center font-sans">
      {/* Desktop Navigation - Proper three-column layout */}
      <div className="hidden w-full items-center md:flex">
        {/* Left spacer */}
        <div className="flex-1"></div>

        {/* 1440px container with navigation items - first left, last right */}
        <div className="flex w-full max-w-[1440px] items-center justify-between px-6">
          {items.map(item => (
            <Link
              key={item.id}
              href={item.href}
              target={item.isExternal ? '_blank' : undefined}
              rel={item.isExternal ? 'noopener noreferrer' : undefined}
              className={`
                relative flex items-center transition-colors duration-200 
                text-sm/6 px-4 py-2 rounded-md
                hover:text-foreground before:absolute before:bottom-[-13px] before:left-0 before:h-0.5 before:bg-border 
                before:transition-all before:duration-300 before:ease-out
                ${
                  isActiveLink(item.href)
                    ? 'text-foreground font-bold tracking-wide before:w-full'
                    : 'text-foreground font-normal tracking-wide before:w-0 hover:before:w-full'
                }
              `}
            >
              {item.label}
            </Link>
          ))}
        </div>

        {/* Right spacer with theme toggle */}
        <div className="flex flex-1 justify-end pr-6">
          <ModeToggle />
        </div>
      </div>

      {/* Mobile Navigation - Horizontal scroll only */}
      <div className="flex w-full overflow-hidden md:hidden">
        <div className="flex min-w-0 gap-3 overflow-x-auto px-1 py-2 scrollbar-hide">
          {items.map(item => (
            <Link
              key={item.id}
              href={item.href}
              target={item.isExternal ? '_blank' : undefined}
              rel={item.isExternal ? 'noopener noreferrer' : undefined}
              className={`
                relative flex shrink-0 items-center whitespace-nowrap
                text-sm/5 px-3 py-2 rounded-md
                transition-colors duration-200
                hover:text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:font-bold
                before:absolute before:bottom-[-13px] before:left-0 before:h-0.5 before:bg-border 
                before:transition-all before:duration-300 before:ease-out
                ${
                  isActiveLink(item.href)
                    ? 'text-foreground font-bold before:w-full'
                    : 'text-muted-foreground font-medium before:w-0 hover:before:w-full'
                }
              `}
            >
              {item.label}
            </Link>
          ))}
        </div>
      </div>
    </nav>
  );
}
