{"id": "fcc18e64-b170-4239-8627-71a43b36d692", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "reset_password_token": {"name": "reset_password_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "reset_password_expiration": {"name": "reset_password_expiration", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "salt": {"name": "salt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "hash": {"name": "hash", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "login_attempts": {"name": "login_attempts", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "lock_until": {"name": "lock_until", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"users_updated_at_idx": {"name": "users_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_created_at_idx": {"name": "users_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.articles_related_companies": {"name": "articles_related_companies", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "ticker": {"name": "ticker", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "relevance": {"name": "relevance", "type": "enum_articles_related_companies_relevance", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'medium'"}, "confidence": {"name": "confidence", "type": "numeric", "primaryKey": false, "notNull": false, "default": 100}, "featured": {"name": "featured", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {"articles_related_companies_order_idx": {"name": "articles_related_companies_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_related_companies_parent_id_idx": {"name": "articles_related_companies_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"articles_related_companies_parent_id_fk": {"name": "articles_related_companies_parent_id_fk", "tableFrom": "articles_related_companies", "tableTo": "articles", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.articles_english_tab_keywords": {"name": "articles_english_tab_keywords", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "keyword": {"name": "keyword", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"articles_english_tab_keywords_order_idx": {"name": "articles_english_tab_keywords_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_english_tab_keywords_parent_id_idx": {"name": "articles_english_tab_keywords_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"articles_english_tab_keywords_parent_id_fk": {"name": "articles_english_tab_keywords_parent_id_fk", "tableFrom": "articles_english_tab_keywords", "tableTo": "articles", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.articles_english_tab_enhanced_key_insights": {"name": "articles_english_tab_enhanced_key_insights", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "insight": {"name": "insight", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"articles_english_tab_enhanced_key_insights_order_idx": {"name": "articles_english_tab_enhanced_key_insights_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_english_tab_enhanced_key_insights_parent_id_idx": {"name": "articles_english_tab_enhanced_key_insights_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"articles_english_tab_enhanced_key_insights_parent_id_fk": {"name": "articles_english_tab_enhanced_key_insights_parent_id_fk", "tableFrom": "articles_english_tab_enhanced_key_insights", "tableTo": "articles", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.articles_german_tab_german_keywords": {"name": "articles_german_tab_german_keywords", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "keyword": {"name": "keyword", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"articles_german_tab_german_keywords_order_idx": {"name": "articles_german_tab_german_keywords_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_german_tab_german_keywords_parent_id_idx": {"name": "articles_german_tab_german_keywords_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"articles_german_tab_german_keywords_parent_id_fk": {"name": "articles_german_tab_german_keywords_parent_id_fk", "tableFrom": "articles_german_tab_german_keywords", "tableTo": "articles", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.articles_german_tab_german_key_insights": {"name": "articles_german_tab_german_key_insights", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "insight": {"name": "insight", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"articles_german_tab_german_key_insights_order_idx": {"name": "articles_german_tab_german_key_insights_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_german_tab_german_key_insights_parent_id_idx": {"name": "articles_german_tab_german_key_insights_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"articles_german_tab_german_key_insights_parent_id_fk": {"name": "articles_german_tab_german_key_insights_parent_id_fk", "tableFrom": "articles_german_tab_german_key_insights", "tableTo": "articles", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.articles": {"name": "articles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "featured_image_id": {"name": "featured_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "article_type": {"name": "article_type", "type": "enum_articles_article_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'curated'"}, "workflow_stage": {"name": "workflow_stage", "type": "enum_articles_workflow_stage", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'curated-draft'"}, "placement": {"name": "placement", "type": "enum_articles_placement", "typeSchema": "public", "primaryKey": false, "notNull": false}, "pinned": {"name": "pinned", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "trending": {"name": "trending", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "read_time_minutes": {"name": "read_time_minutes", "type": "numeric", "primaryKey": false, "notNull": false}, "published_by_id": {"name": "published_by_id", "type": "integer", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "has_been_enhanced": {"name": "has_been_enhanced", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "has_german_translation": {"name": "has_german_translation", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "has_original_source": {"name": "has_original_source", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "english_tab_enhanced_title": {"name": "english_tab_enhanced_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "english_tab_enhanced_summary": {"name": "english_tab_enhanced_summary", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "english_tab_enhanced_content": {"name": "english_tab_enhanced_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "sources_tab_source_feed_id": {"name": "sources_tab_source_feed_id", "type": "integer", "primaryKey": false, "notNull": false}, "sources_tab_source_url": {"name": "sources_tab_source_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sources_tab_original_published_at": {"name": "sources_tab_original_published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "sources_tab_original_title": {"name": "sources_tab_original_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sources_tab_original_summary": {"name": "sources_tab_original_summary", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sources_tab_original_content": {"name": "sources_tab_original_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "german_tab_german_title": {"name": "german_tab_german_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "german_tab_german_summary": {"name": "german_tab_german_summary", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "german_tab_german_content": {"name": "german_tab_german_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_title": {"name": "meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_description": {"name": "meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_image_id": {"name": "meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_articles_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"articles_featured_image_idx": {"name": "articles_featured_image_idx", "columns": [{"expression": "featured_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_trending_idx": {"name": "articles_trending_idx", "columns": [{"expression": "trending", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_published_by_idx": {"name": "articles_published_by_idx", "columns": [{"expression": "published_by_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_sources_tab_sources_tab_source_feed_idx": {"name": "articles_sources_tab_sources_tab_source_feed_idx", "columns": [{"expression": "sources_tab_source_feed_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_meta_meta_image_idx": {"name": "articles_meta_meta_image_idx", "columns": [{"expression": "meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_updated_at_idx": {"name": "articles_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_created_at_idx": {"name": "articles_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles__status_idx": {"name": "articles__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"articles_featured_image_id_media_id_fk": {"name": "articles_featured_image_id_media_id_fk", "tableFrom": "articles", "tableTo": "media", "columnsFrom": ["featured_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "articles_published_by_id_users_id_fk": {"name": "articles_published_by_id_users_id_fk", "tableFrom": "articles", "tableTo": "users", "columnsFrom": ["published_by_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "articles_sources_tab_source_feed_id_rss_feeds_id_fk": {"name": "articles_sources_tab_source_feed_id_rss_feeds_id_fk", "tableFrom": "articles", "tableTo": "rss_feeds", "columnsFrom": ["sources_tab_source_feed_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "articles_meta_image_id_media_id_fk": {"name": "articles_meta_image_id_media_id_fk", "tableFrom": "articles", "tableTo": "media", "columnsFrom": ["meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.articles_rels": {"name": "articles_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "categories_id": {"name": "categories_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"articles_rels_order_idx": {"name": "articles_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_rels_parent_idx": {"name": "articles_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_rels_path_idx": {"name": "articles_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "articles_rels_categories_id_idx": {"name": "articles_rels_categories_id_idx", "columns": [{"expression": "categories_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"articles_rels_parent_fk": {"name": "articles_rels_parent_fk", "tableFrom": "articles_rels", "tableTo": "articles", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "articles_rels_categories_fk": {"name": "articles_rels_categories_fk", "tableFrom": "articles_rels", "tableTo": "categories", "columnsFrom": ["categories_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._articles_v_version_related_companies": {"name": "_articles_v_version_related_companies", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "ticker": {"name": "ticker", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "relevance": {"name": "relevance", "type": "enum__articles_v_version_related_companies_relevance", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'medium'"}, "confidence": {"name": "confidence", "type": "numeric", "primaryKey": false, "notNull": false, "default": 100}, "featured": {"name": "featured", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_articles_v_version_related_companies_order_idx": {"name": "_articles_v_version_related_companies_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_version_related_companies_parent_id_idx": {"name": "_articles_v_version_related_companies_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_articles_v_version_related_companies_parent_id_fk": {"name": "_articles_v_version_related_companies_parent_id_fk", "tableFrom": "_articles_v_version_related_companies", "tableTo": "_articles_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._articles_v_version_english_tab_keywords": {"name": "_articles_v_version_english_tab_keywords", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "keyword": {"name": "keyword", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_articles_v_version_english_tab_keywords_order_idx": {"name": "_articles_v_version_english_tab_keywords_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_version_english_tab_keywords_parent_id_idx": {"name": "_articles_v_version_english_tab_keywords_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_articles_v_version_english_tab_keywords_parent_id_fk": {"name": "_articles_v_version_english_tab_keywords_parent_id_fk", "tableFrom": "_articles_v_version_english_tab_keywords", "tableTo": "_articles_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._articles_v_version_english_tab_enhanced_key_insights": {"name": "_articles_v_version_english_tab_enhanced_key_insights", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "insight": {"name": "insight", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_articles_v_version_english_tab_enhanced_key_insights_order_idx": {"name": "_articles_v_version_english_tab_enhanced_key_insights_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_version_english_tab_enhanced_key_insights_parent_id_idx": {"name": "_articles_v_version_english_tab_enhanced_key_insights_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_articles_v_version_english_tab_enhanced_key_insights_parent_id_fk": {"name": "_articles_v_version_english_tab_enhanced_key_insights_parent_id_fk", "tableFrom": "_articles_v_version_english_tab_enhanced_key_insights", "tableTo": "_articles_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._articles_v_version_german_tab_german_keywords": {"name": "_articles_v_version_german_tab_german_keywords", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "keyword": {"name": "keyword", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_articles_v_version_german_tab_german_keywords_order_idx": {"name": "_articles_v_version_german_tab_german_keywords_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_version_german_tab_german_keywords_parent_id_idx": {"name": "_articles_v_version_german_tab_german_keywords_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_articles_v_version_german_tab_german_keywords_parent_id_fk": {"name": "_articles_v_version_german_tab_german_keywords_parent_id_fk", "tableFrom": "_articles_v_version_german_tab_german_keywords", "tableTo": "_articles_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._articles_v_version_german_tab_german_key_insights": {"name": "_articles_v_version_german_tab_german_key_insights", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "insight": {"name": "insight", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_articles_v_version_german_tab_german_key_insights_order_idx": {"name": "_articles_v_version_german_tab_german_key_insights_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_version_german_tab_german_key_insights_parent_id_idx": {"name": "_articles_v_version_german_tab_german_key_insights_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_articles_v_version_german_tab_german_key_insights_parent_id_fk": {"name": "_articles_v_version_german_tab_german_key_insights_parent_id_fk", "tableFrom": "_articles_v_version_german_tab_german_key_insights", "tableTo": "_articles_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._articles_v": {"name": "_articles_v", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_title": {"name": "version_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_slug": {"name": "version_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_featured_image_id": {"name": "version_featured_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_article_type": {"name": "version_article_type", "type": "enum__articles_v_version_article_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'curated'"}, "version_workflow_stage": {"name": "version_workflow_stage", "type": "enum__articles_v_version_workflow_stage", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'curated-draft'"}, "version_placement": {"name": "version_placement", "type": "enum__articles_v_version_placement", "typeSchema": "public", "primaryKey": false, "notNull": false}, "version_pinned": {"name": "version_pinned", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "version_trending": {"name": "version_trending", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "version_read_time_minutes": {"name": "version_read_time_minutes", "type": "numeric", "primaryKey": false, "notNull": false}, "version_published_by_id": {"name": "version_published_by_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_published_at": {"name": "version_published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_has_been_enhanced": {"name": "version_has_been_enhanced", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "version_has_german_translation": {"name": "version_has_german_translation", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "version_has_original_source": {"name": "version_has_original_source", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "version_english_tab_enhanced_title": {"name": "version_english_tab_enhanced_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_english_tab_enhanced_summary": {"name": "version_english_tab_enhanced_summary", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_english_tab_enhanced_content": {"name": "version_english_tab_enhanced_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "version_sources_tab_source_feed_id": {"name": "version_sources_tab_source_feed_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_sources_tab_source_url": {"name": "version_sources_tab_source_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_sources_tab_original_published_at": {"name": "version_sources_tab_original_published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_sources_tab_original_title": {"name": "version_sources_tab_original_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_sources_tab_original_summary": {"name": "version_sources_tab_original_summary", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_sources_tab_original_content": {"name": "version_sources_tab_original_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "version_german_tab_german_title": {"name": "version_german_tab_german_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_german_tab_german_summary": {"name": "version_german_tab_german_summary", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_german_tab_german_content": {"name": "version_german_tab_german_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "version_meta_title": {"name": "version_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_meta_description": {"name": "version_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_meta_image_id": {"name": "version_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__articles_v_version_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_articles_v_parent_idx": {"name": "_articles_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_version_version_featured_image_idx": {"name": "_articles_v_version_version_featured_image_idx", "columns": [{"expression": "version_featured_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_version_version_trending_idx": {"name": "_articles_v_version_version_trending_idx", "columns": [{"expression": "version_trending", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_version_version_published_by_idx": {"name": "_articles_v_version_version_published_by_idx", "columns": [{"expression": "version_published_by_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_version_sources_tab_version_sources_tab_source_feed_idx": {"name": "_articles_v_version_sources_tab_version_sources_tab_source_feed_idx", "columns": [{"expression": "version_sources_tab_source_feed_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_version_meta_version_meta_image_idx": {"name": "_articles_v_version_meta_version_meta_image_idx", "columns": [{"expression": "version_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_version_version_updated_at_idx": {"name": "_articles_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_version_version_created_at_idx": {"name": "_articles_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_version_version__status_idx": {"name": "_articles_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_created_at_idx": {"name": "_articles_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_updated_at_idx": {"name": "_articles_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_latest_idx": {"name": "_articles_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_articles_v_parent_id_articles_id_fk": {"name": "_articles_v_parent_id_articles_id_fk", "tableFrom": "_articles_v", "tableTo": "articles", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_articles_v_version_featured_image_id_media_id_fk": {"name": "_articles_v_version_featured_image_id_media_id_fk", "tableFrom": "_articles_v", "tableTo": "media", "columnsFrom": ["version_featured_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_articles_v_version_published_by_id_users_id_fk": {"name": "_articles_v_version_published_by_id_users_id_fk", "tableFrom": "_articles_v", "tableTo": "users", "columnsFrom": ["version_published_by_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_articles_v_version_sources_tab_source_feed_id_rss_feeds_id_fk": {"name": "_articles_v_version_sources_tab_source_feed_id_rss_feeds_id_fk", "tableFrom": "_articles_v", "tableTo": "rss_feeds", "columnsFrom": ["version_sources_tab_source_feed_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_articles_v_version_meta_image_id_media_id_fk": {"name": "_articles_v_version_meta_image_id_media_id_fk", "tableFrom": "_articles_v", "tableTo": "media", "columnsFrom": ["version_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._articles_v_rels": {"name": "_articles_v_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "categories_id": {"name": "categories_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"_articles_v_rels_order_idx": {"name": "_articles_v_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_rels_parent_idx": {"name": "_articles_v_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_rels_path_idx": {"name": "_articles_v_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_articles_v_rels_categories_id_idx": {"name": "_articles_v_rels_categories_id_idx", "columns": [{"expression": "categories_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_articles_v_rels_parent_fk": {"name": "_articles_v_rels_parent_fk", "tableFrom": "_articles_v_rels", "tableTo": "_articles_v", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_articles_v_rels_categories_fk": {"name": "_articles_v_rels_categories_fk", "tableFrom": "_articles_v_rels", "tableTo": "categories", "columnsFrom": ["categories_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "english": {"name": "english", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug_lock": {"name": "slug_lock", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"categories_slug_idx": {"name": "categories_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "categories_updated_at_idx": {"name": "categories_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "categories_created_at_idx": {"name": "categories_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages": {"name": "pages", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "featured_image_id": {"name": "featured_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "english_tab_title": {"name": "english_tab_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "english_tab_content": {"name": "english_tab_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "german_tab_german_title": {"name": "german_tab_german_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "german_tab_german_content": {"name": "german_tab_german_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_title": {"name": "meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_image_id": {"name": "meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_description": {"name": "meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug_lock": {"name": "slug_lock", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "enable_breadcrumbs": {"name": "enable_breadcrumbs", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "has_german_translation": {"name": "has_german_translation", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_pages_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"pages_featured_image_idx": {"name": "pages_featured_image_idx", "columns": [{"expression": "featured_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_meta_meta_image_idx": {"name": "pages_meta_meta_image_idx", "columns": [{"expression": "meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_slug_idx": {"name": "pages_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "pages_parent_idx": {"name": "pages_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_updated_at_idx": {"name": "pages_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_created_at_idx": {"name": "pages_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages__status_idx": {"name": "pages__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_featured_image_id_media_id_fk": {"name": "pages_featured_image_id_media_id_fk", "tableFrom": "pages", "tableTo": "media", "columnsFrom": ["featured_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "pages_meta_image_id_media_id_fk": {"name": "pages_meta_image_id_media_id_fk", "tableFrom": "pages", "tableTo": "media", "columnsFrom": ["meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "pages_parent_id_pages_id_fk": {"name": "pages_parent_id_pages_id_fk", "tableFrom": "pages", "tableTo": "pages", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v": {"name": "_pages_v", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_title": {"name": "version_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_featured_image_id": {"name": "version_featured_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_english_tab_title": {"name": "version_english_tab_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_english_tab_content": {"name": "version_english_tab_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "version_german_tab_german_title": {"name": "version_german_tab_german_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_german_tab_german_content": {"name": "version_german_tab_german_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "version_meta_title": {"name": "version_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_meta_image_id": {"name": "version_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_meta_description": {"name": "version_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_published_at": {"name": "version_published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_slug": {"name": "version_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_slug_lock": {"name": "version_slug_lock", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "version_parent_id": {"name": "version_parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_enable_breadcrumbs": {"name": "version_enable_breadcrumbs", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "version_has_german_translation": {"name": "version_has_german_translation", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__pages_v_version_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_parent_idx": {"name": "_pages_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_version_featured_image_idx": {"name": "_pages_v_version_version_featured_image_idx", "columns": [{"expression": "version_featured_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_meta_version_meta_image_idx": {"name": "_pages_v_version_meta_version_meta_image_idx", "columns": [{"expression": "version_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_version_slug_idx": {"name": "_pages_v_version_version_slug_idx", "columns": [{"expression": "version_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_version_parent_idx": {"name": "_pages_v_version_version_parent_idx", "columns": [{"expression": "version_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_version_updated_at_idx": {"name": "_pages_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_version_created_at_idx": {"name": "_pages_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_version__status_idx": {"name": "_pages_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_created_at_idx": {"name": "_pages_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_updated_at_idx": {"name": "_pages_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_latest_idx": {"name": "_pages_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_parent_id_pages_id_fk": {"name": "_pages_v_parent_id_pages_id_fk", "tableFrom": "_pages_v", "tableTo": "pages", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_pages_v_version_featured_image_id_media_id_fk": {"name": "_pages_v_version_featured_image_id_media_id_fk", "tableFrom": "_pages_v", "tableTo": "media", "columnsFrom": ["version_featured_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_pages_v_version_meta_image_id_media_id_fk": {"name": "_pages_v_version_meta_image_id_media_id_fk", "tableFrom": "_pages_v", "tableTo": "media", "columnsFrom": ["version_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_pages_v_version_parent_id_pages_id_fk": {"name": "_pages_v_version_parent_id_pages_id_fk", "tableFrom": "_pages_v", "tableTo": "pages", "columnsFrom": ["version_parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.keywords": {"name": "keywords", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "keyword": {"name": "keyword", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "english_keyword": {"name": "english_keyword", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "usage_count": {"name": "usage_count", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"keywords_keyword_idx": {"name": "keywords_keyword_idx", "columns": [{"expression": "keyword", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "keywords_updated_at_idx": {"name": "keywords_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "keywords_created_at_idx": {"name": "keywords_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.processed_urls": {"name": "processed_urls", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "enum_processed_urls_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "publication_date": {"name": "publication_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "feed_id_id": {"name": "feed_id_id", "type": "integer", "primaryKey": false, "notNull": false}, "processed_at": {"name": "processed_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "reason": {"name": "reason", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "article_id_id": {"name": "article_id_id", "type": "integer", "primaryKey": false, "notNull": false}, "attempt_count": {"name": "attempt_count", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "last_attempt_at": {"name": "last_attempt_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"processed_urls_url_idx": {"name": "processed_urls_url_idx", "columns": [{"expression": "url", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "processed_urls_feed_id_idx": {"name": "processed_urls_feed_id_idx", "columns": [{"expression": "feed_id_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "processed_urls_article_id_idx": {"name": "processed_urls_article_id_idx", "columns": [{"expression": "article_id_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "processed_urls_updated_at_idx": {"name": "processed_urls_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "processed_urls_created_at_idx": {"name": "processed_urls_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"processed_urls_feed_id_id_rss_feeds_id_fk": {"name": "processed_urls_feed_id_id_rss_feeds_id_fk", "tableFrom": "processed_urls", "tableTo": "rss_feeds", "columnsFrom": ["feed_id_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "processed_urls_article_id_id_articles_id_fk": {"name": "processed_urls_article_id_id_articles_id_fk", "tableFrom": "processed_urls", "tableTo": "articles", "columnsFrom": ["article_id_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.rss_feeds_firecrawl_options_exclude_tags": {"name": "rss_feeds_firecrawl_options_exclude_tags", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "tag": {"name": "tag", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"rss_feeds_firecrawl_options_exclude_tags_order_idx": {"name": "rss_feeds_firecrawl_options_exclude_tags_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "rss_feeds_firecrawl_options_exclude_tags_parent_id_idx": {"name": "rss_feeds_firecrawl_options_exclude_tags_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"rss_feeds_firecrawl_options_exclude_tags_parent_id_fk": {"name": "rss_feeds_firecrawl_options_exclude_tags_parent_id_fk", "tableFrom": "rss_feeds_firecrawl_options_exclude_tags", "tableTo": "rss_feeds", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.rss_feeds_firecrawl_options_include_tags": {"name": "rss_feeds_firecrawl_options_include_tags", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "tag": {"name": "tag", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"rss_feeds_firecrawl_options_include_tags_order_idx": {"name": "rss_feeds_firecrawl_options_include_tags_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "rss_feeds_firecrawl_options_include_tags_parent_id_idx": {"name": "rss_feeds_firecrawl_options_include_tags_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"rss_feeds_firecrawl_options_include_tags_parent_id_fk": {"name": "rss_feeds_firecrawl_options_include_tags_parent_id_fk", "tableFrom": "rss_feeds_firecrawl_options_include_tags", "tableTo": "rss_feeds", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.rss_feeds_keyword_filtering_custom_keywords": {"name": "rss_feeds_keyword_filtering_custom_keywords", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "keyword": {"name": "keyword", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "english_keyword": {"name": "english_keyword", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "weight": {"name": "weight", "type": "numeric", "primaryKey": false, "notNull": false, "default": 5}}, "indexes": {"rss_feeds_keyword_filtering_custom_keywords_order_idx": {"name": "rss_feeds_keyword_filtering_custom_keywords_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "rss_feeds_keyword_filtering_custom_keywords_parent_id_idx": {"name": "rss_feeds_keyword_filtering_custom_keywords_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"rss_feeds_keyword_filtering_custom_keywords_parent_id_fk": {"name": "rss_feeds_keyword_filtering_custom_keywords_parent_id_fk", "tableFrom": "rss_feeds_keyword_filtering_custom_keywords", "tableTo": "rss_feeds", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.rss_feeds": {"name": "rss_feeds", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "language": {"name": "language", "type": "enum_rss_feeds_language", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'de'"}, "last_processed": {"name": "last_processed", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "last_checked": {"name": "last_checked", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "last_successful_check": {"name": "last_successful_check", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "items_processed": {"name": "items_processed", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "items_accepted": {"name": "items_accepted", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "articles_found_since_last_successful": {"name": "articles_found_since_last_successful", "type": "numeric", "primaryKey": false, "notNull": false}, "total_articles_accepted": {"name": "total_articles_accepted", "type": "numeric", "primaryKey": false, "notNull": false}, "error_count": {"name": "error_count", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "last_error_message": {"name": "last_error_message", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "processing_frequency": {"name": "processing_frequency", "type": "numeric", "primaryKey": false, "notNull": false, "default": 60}, "priority": {"name": "priority", "type": "enum_rss_feeds_priority", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'medium'"}, "firecrawl_options_remove_base64_images": {"name": "firecrawl_options_remove_base64_images", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "firecrawl_options_block_ads": {"name": "firecrawl_options_block_ads", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "keyword_filtering_strict_keyword_matching": {"name": "keyword_filtering_strict_keyword_matching", "type": "boolean", "primaryKey": false, "notNull": false}, "processing_options_max_firecrawl_scrape": {"name": "processing_options_max_firecrawl_scrape", "type": "numeric", "primaryKey": false, "notNull": false}, "processing_options_max_articles_per_run": {"name": "processing_options_max_articles_per_run", "type": "numeric", "primaryKey": false, "notNull": false}, "processing_options_skip_translation": {"name": "processing_options_skip_translation", "type": "boolean", "primaryKey": false, "notNull": false}, "processing_options_skip_enhancement": {"name": "processing_options_skip_enhancement", "type": "boolean", "primaryKey": false, "notNull": false}, "processing_options_custom_timeout": {"name": "processing_options_custom_timeout", "type": "numeric", "primaryKey": false, "notNull": false}, "processing_options_enable_stealth": {"name": "processing_options_enable_stealth", "type": "boolean", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"rss_feeds_url_idx": {"name": "rss_feeds_url_idx", "columns": [{"expression": "url", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "rss_feeds_updated_at_idx": {"name": "rss_feeds_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "rss_feeds_created_at_idx": {"name": "rss_feeds_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.media": {"name": "media", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "alt": {"name": "alt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "thumbnail_u_r_l": {"name": "thumbnail_u_r_l", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filesize": {"name": "filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "height": {"name": "height", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_x": {"name": "focal_x", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_y": {"name": "focal_y", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_url": {"name": "sizes_thumbnail_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_thumbnail_width": {"name": "sizes_thumbnail_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_height": {"name": "sizes_thumbnail_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_mime_type": {"name": "sizes_thumbnail_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_thumbnail_filesize": {"name": "sizes_thumbnail_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_filename": {"name": "sizes_thumbnail_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_horizontal_url": {"name": "sizes_horizontal_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_horizontal_width": {"name": "sizes_horizontal_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_horizontal_height": {"name": "sizes_horizontal_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_horizontal_mime_type": {"name": "sizes_horizontal_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_horizontal_filesize": {"name": "sizes_horizontal_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_horizontal_filename": {"name": "sizes_horizontal_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_card_url": {"name": "sizes_card_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_card_width": {"name": "sizes_card_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_card_height": {"name": "sizes_card_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_card_mime_type": {"name": "sizes_card_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_card_filesize": {"name": "sizes_card_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_card_filename": {"name": "sizes_card_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_social_url": {"name": "sizes_social_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_social_width": {"name": "sizes_social_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_social_height": {"name": "sizes_social_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_social_mime_type": {"name": "sizes_social_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_social_filesize": {"name": "sizes_social_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_social_filename": {"name": "sizes_social_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_hero_url": {"name": "sizes_hero_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_hero_width": {"name": "sizes_hero_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_hero_height": {"name": "sizes_hero_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_hero_mime_type": {"name": "sizes_hero_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_hero_filesize": {"name": "sizes_hero_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_hero_filename": {"name": "sizes_hero_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"media_updated_at_idx": {"name": "media_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_created_at_idx": {"name": "media_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_filename_idx": {"name": "media_filename_idx", "columns": [{"expression": "filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_thumbnail_sizes_thumbnail_filename_idx": {"name": "media_sizes_thumbnail_sizes_thumbnail_filename_idx", "columns": [{"expression": "sizes_thumbnail_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_horizontal_sizes_horizontal_filename_idx": {"name": "media_sizes_horizontal_sizes_horizontal_filename_idx", "columns": [{"expression": "sizes_horizontal_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_card_sizes_card_filename_idx": {"name": "media_sizes_card_sizes_card_filename_idx", "columns": [{"expression": "sizes_card_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_social_sizes_social_filename_idx": {"name": "media_sizes_social_sizes_social_filename_idx", "columns": [{"expression": "sizes_social_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_hero_sizes_hero_filename_idx": {"name": "media_sizes_hero_sizes_hero_filename_idx", "columns": [{"expression": "sizes_hero_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_locked_documents": {"name": "payload_locked_documents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "global_slug": {"name": "global_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_locked_documents_global_slug_idx": {"name": "payload_locked_documents_global_slug_idx", "columns": [{"expression": "global_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_updated_at_idx": {"name": "payload_locked_documents_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_created_at_idx": {"name": "payload_locked_documents_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_locked_documents_rels": {"name": "payload_locked_documents_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}, "articles_id": {"name": "articles_id", "type": "integer", "primaryKey": false, "notNull": false}, "categories_id": {"name": "categories_id", "type": "integer", "primaryKey": false, "notNull": false}, "pages_id": {"name": "pages_id", "type": "integer", "primaryKey": false, "notNull": false}, "keywords_id": {"name": "keywords_id", "type": "integer", "primaryKey": false, "notNull": false}, "processed_urls_id": {"name": "processed_urls_id", "type": "integer", "primaryKey": false, "notNull": false}, "rss_feeds_id": {"name": "rss_feeds_id", "type": "integer", "primaryKey": false, "notNull": false}, "media_id": {"name": "media_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"payload_locked_documents_rels_order_idx": {"name": "payload_locked_documents_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_parent_idx": {"name": "payload_locked_documents_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_path_idx": {"name": "payload_locked_documents_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_users_id_idx": {"name": "payload_locked_documents_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_articles_id_idx": {"name": "payload_locked_documents_rels_articles_id_idx", "columns": [{"expression": "articles_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_categories_id_idx": {"name": "payload_locked_documents_rels_categories_id_idx", "columns": [{"expression": "categories_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_pages_id_idx": {"name": "payload_locked_documents_rels_pages_id_idx", "columns": [{"expression": "pages_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_keywords_id_idx": {"name": "payload_locked_documents_rels_keywords_id_idx", "columns": [{"expression": "keywords_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_processed_urls_id_idx": {"name": "payload_locked_documents_rels_processed_urls_id_idx", "columns": [{"expression": "processed_urls_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_rss_feeds_id_idx": {"name": "payload_locked_documents_rels_rss_feeds_id_idx", "columns": [{"expression": "rss_feeds_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_media_id_idx": {"name": "payload_locked_documents_rels_media_id_idx", "columns": [{"expression": "media_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_locked_documents_rels_parent_fk": {"name": "payload_locked_documents_rels_parent_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "payload_locked_documents", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_users_fk": {"name": "payload_locked_documents_rels_users_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_articles_fk": {"name": "payload_locked_documents_rels_articles_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "articles", "columnsFrom": ["articles_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_categories_fk": {"name": "payload_locked_documents_rels_categories_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "categories", "columnsFrom": ["categories_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_pages_fk": {"name": "payload_locked_documents_rels_pages_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "pages", "columnsFrom": ["pages_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_keywords_fk": {"name": "payload_locked_documents_rels_keywords_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "keywords", "columnsFrom": ["keywords_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_processed_urls_fk": {"name": "payload_locked_documents_rels_processed_urls_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "processed_urls", "columnsFrom": ["processed_urls_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_rss_feeds_fk": {"name": "payload_locked_documents_rels_rss_feeds_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "rss_feeds", "columnsFrom": ["rss_feeds_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_media_fk": {"name": "payload_locked_documents_rels_media_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "media", "columnsFrom": ["media_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_preferences": {"name": "payload_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_preferences_key_idx": {"name": "payload_preferences_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_updated_at_idx": {"name": "payload_preferences_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_created_at_idx": {"name": "payload_preferences_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_preferences_rels": {"name": "payload_preferences_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"payload_preferences_rels_order_idx": {"name": "payload_preferences_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_parent_idx": {"name": "payload_preferences_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_path_idx": {"name": "payload_preferences_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_users_id_idx": {"name": "payload_preferences_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_preferences_rels_parent_fk": {"name": "payload_preferences_rels_parent_fk", "tableFrom": "payload_preferences_rels", "tableTo": "payload_preferences", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_preferences_rels_users_fk": {"name": "payload_preferences_rels_users_fk", "tableFrom": "payload_preferences_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_migrations": {"name": "payload_migrations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "batch": {"name": "batch", "type": "numeric", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_migrations_updated_at_idx": {"name": "payload_migrations_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_migrations_created_at_idx": {"name": "payload_migrations_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.header_nav_items": {"name": "header_nav_items", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "link_type": {"name": "link_type", "type": "enum_header_nav_items_link_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'reference'"}, "link_new_tab": {"name": "link_new_tab", "type": "boolean", "primaryKey": false, "notNull": false}, "link_url": {"name": "link_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_label": {"name": "link_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"header_nav_items_order_idx": {"name": "header_nav_items_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "header_nav_items_parent_id_idx": {"name": "header_nav_items_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"header_nav_items_parent_id_fk": {"name": "header_nav_items_parent_id_fk", "tableFrom": "header_nav_items", "tableTo": "header", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.header": {"name": "header", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.header_rels": {"name": "header_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "articles_id": {"name": "articles_id", "type": "integer", "primaryKey": false, "notNull": false}, "categories_id": {"name": "categories_id", "type": "integer", "primaryKey": false, "notNull": false}, "pages_id": {"name": "pages_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"header_rels_order_idx": {"name": "header_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "header_rels_parent_idx": {"name": "header_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "header_rels_path_idx": {"name": "header_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "header_rels_articles_id_idx": {"name": "header_rels_articles_id_idx", "columns": [{"expression": "articles_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "header_rels_categories_id_idx": {"name": "header_rels_categories_id_idx", "columns": [{"expression": "categories_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "header_rels_pages_id_idx": {"name": "header_rels_pages_id_idx", "columns": [{"expression": "pages_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"header_rels_parent_fk": {"name": "header_rels_parent_fk", "tableFrom": "header_rels", "tableTo": "header", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "header_rels_articles_fk": {"name": "header_rels_articles_fk", "tableFrom": "header_rels", "tableTo": "articles", "columnsFrom": ["articles_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "header_rels_categories_fk": {"name": "header_rels_categories_fk", "tableFrom": "header_rels", "tableTo": "categories", "columnsFrom": ["categories_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "header_rels_pages_fk": {"name": "header_rels_pages_fk", "tableFrom": "header_rels", "tableTo": "pages", "columnsFrom": ["pages_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.footer_navigation_sections_links": {"name": "footer_navigation_sections_links", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "link_type": {"name": "link_type", "type": "enum_footer_navigation_sections_links_link_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'reference'"}, "link_new_tab": {"name": "link_new_tab", "type": "boolean", "primaryKey": false, "notNull": false}, "link_url": {"name": "link_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_label": {"name": "link_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"footer_navigation_sections_links_order_idx": {"name": "footer_navigation_sections_links_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "footer_navigation_sections_links_parent_id_idx": {"name": "footer_navigation_sections_links_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"footer_navigation_sections_links_parent_id_fk": {"name": "footer_navigation_sections_links_parent_id_fk", "tableFrom": "footer_navigation_sections_links", "tableTo": "footer_navigation_sections", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.footer_navigation_sections": {"name": "footer_navigation_sections", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"footer_navigation_sections_order_idx": {"name": "footer_navigation_sections_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "footer_navigation_sections_parent_id_idx": {"name": "footer_navigation_sections_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"footer_navigation_sections_parent_id_fk": {"name": "footer_navigation_sections_parent_id_fk", "tableFrom": "footer_navigation_sections", "tableTo": "footer", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.footer_social_links": {"name": "footer_social_links", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "platform": {"name": "platform", "type": "enum_footer_social_links_platform", "typeSchema": "public", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"footer_social_links_order_idx": {"name": "footer_social_links_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "footer_social_links_parent_id_idx": {"name": "footer_social_links_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"footer_social_links_parent_id_fk": {"name": "footer_social_links_parent_id_fk", "tableFrom": "footer_social_links", "tableTo": "footer", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.footer_legal_links": {"name": "footer_legal_links", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "link_type": {"name": "link_type", "type": "enum_footer_legal_links_link_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'reference'"}, "link_new_tab": {"name": "link_new_tab", "type": "boolean", "primaryKey": false, "notNull": false}, "link_url": {"name": "link_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_label": {"name": "link_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"footer_legal_links_order_idx": {"name": "footer_legal_links_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "footer_legal_links_parent_id_idx": {"name": "footer_legal_links_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"footer_legal_links_parent_id_fk": {"name": "footer_legal_links_parent_id_fk", "tableFrom": "footer_legal_links", "tableTo": "footer", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.footer": {"name": "footer", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "logo_image_id": {"name": "logo_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "logo_title": {"name": "logo_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'<PERSON><PERSON><PERSON>'"}, "logo_url": {"name": "logo_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'/'"}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "copyright_company_name": {"name": "copyright_company_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'börsenblick.de'"}, "copyright_custom_text": {"name": "copyright_custom_text", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"footer_logo_logo_image_idx": {"name": "footer_logo_logo_image_idx", "columns": [{"expression": "logo_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"footer_logo_image_id_media_id_fk": {"name": "footer_logo_image_id_media_id_fk", "tableFrom": "footer", "tableTo": "media", "columnsFrom": ["logo_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.footer_rels": {"name": "footer_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "articles_id": {"name": "articles_id", "type": "integer", "primaryKey": false, "notNull": false}, "categories_id": {"name": "categories_id", "type": "integer", "primaryKey": false, "notNull": false}, "pages_id": {"name": "pages_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"footer_rels_order_idx": {"name": "footer_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "footer_rels_parent_idx": {"name": "footer_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "footer_rels_path_idx": {"name": "footer_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "footer_rels_articles_id_idx": {"name": "footer_rels_articles_id_idx", "columns": [{"expression": "articles_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "footer_rels_categories_id_idx": {"name": "footer_rels_categories_id_idx", "columns": [{"expression": "categories_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "footer_rels_pages_id_idx": {"name": "footer_rels_pages_id_idx", "columns": [{"expression": "pages_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"footer_rels_parent_fk": {"name": "footer_rels_parent_fk", "tableFrom": "footer_rels", "tableTo": "footer", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "footer_rels_articles_fk": {"name": "footer_rels_articles_fk", "tableFrom": "footer_rels", "tableTo": "articles", "columnsFrom": ["articles_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "footer_rels_categories_fk": {"name": "footer_rels_categories_fk", "tableFrom": "footer_rels", "tableTo": "categories", "columnsFrom": ["categories_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "footer_rels_pages_fk": {"name": "footer_rels_pages_fk", "tableFrom": "footer_rels", "tableTo": "pages", "columnsFrom": ["pages_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.enum_articles_related_companies_relevance": {"name": "enum_articles_related_companies_relevance", "schema": "public", "values": ["high", "medium", "low"]}, "public.enum_articles_article_type": {"name": "enum_articles_article_type", "schema": "public", "values": ["generated", "curated"]}, "public.enum_articles_workflow_stage": {"name": "enum_articles_workflow_stage", "schema": "public", "values": ["curated-draft", "candidate-article", "translated", "ready-for-review"]}, "public.enum_articles_placement": {"name": "enum_articles_placement", "schema": "public", "values": ["tier-1", "tier-2", "tier-3"]}, "public.enum_articles_status": {"name": "enum_articles_status", "schema": "public", "values": ["draft", "published"]}, "public.enum__articles_v_version_related_companies_relevance": {"name": "enum__articles_v_version_related_companies_relevance", "schema": "public", "values": ["high", "medium", "low"]}, "public.enum__articles_v_version_article_type": {"name": "enum__articles_v_version_article_type", "schema": "public", "values": ["generated", "curated"]}, "public.enum__articles_v_version_workflow_stage": {"name": "enum__articles_v_version_workflow_stage", "schema": "public", "values": ["curated-draft", "candidate-article", "translated", "ready-for-review"]}, "public.enum__articles_v_version_placement": {"name": "enum__articles_v_version_placement", "schema": "public", "values": ["tier-1", "tier-2", "tier-3"]}, "public.enum__articles_v_version_status": {"name": "enum__articles_v_version_status", "schema": "public", "values": ["draft", "published"]}, "public.enum_pages_status": {"name": "enum_pages_status", "schema": "public", "values": ["draft", "published"]}, "public.enum__pages_v_version_status": {"name": "enum__pages_v_version_status", "schema": "public", "values": ["draft", "published"]}, "public.enum_processed_urls_status": {"name": "enum_processed_urls_status", "schema": "public", "values": ["pending", "accepted", "rejected", "error"]}, "public.enum_rss_feeds_language": {"name": "enum_rss_feeds_language", "schema": "public", "values": ["de", "en"]}, "public.enum_rss_feeds_priority": {"name": "enum_rss_feeds_priority", "schema": "public", "values": ["low", "medium", "high"]}, "public.enum_header_nav_items_link_type": {"name": "enum_header_nav_items_link_type", "schema": "public", "values": ["reference", "custom"]}, "public.enum_footer_navigation_sections_links_link_type": {"name": "enum_footer_navigation_sections_links_link_type", "schema": "public", "values": ["reference", "custom"]}, "public.enum_footer_social_links_platform": {"name": "enum_footer_social_links_platform", "schema": "public", "values": ["instagram", "facebook", "twitter", "linkedin", "youtube", "github", "threads", "email"]}, "public.enum_footer_legal_links_link_type": {"name": "enum_footer_legal_links_link_type", "schema": "public", "values": ["reference", "custom"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}