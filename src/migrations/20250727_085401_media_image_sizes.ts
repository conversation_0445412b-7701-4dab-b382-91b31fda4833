import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TYPE "public"."enum_articles_related_companies_relevance" AS ENUM('high', 'medium', 'low');
  CREATE TYPE "public"."enum_articles_article_type" AS ENUM('generated', 'curated');
  CREATE TYPE "public"."enum_articles_workflow_stage" AS ENUM('curated-draft', 'candidate-article', 'translated', 'ready-for-review');
  CREATE TYPE "public"."enum_articles_placement" AS ENUM('tier-1', 'tier-2', 'tier-3');
  CREATE TYPE "public"."enum_articles_status" AS ENUM('draft', 'published');
  CREATE TYPE "public"."enum__articles_v_version_related_companies_relevance" AS ENUM('high', 'medium', 'low');
  CREATE TYPE "public"."enum__articles_v_version_article_type" AS ENUM('generated', 'curated');
  CREATE TYPE "public"."enum__articles_v_version_workflow_stage" AS ENUM('curated-draft', 'candidate-article', 'translated', 'ready-for-review');
  CREATE TYPE "public"."enum__articles_v_version_placement" AS ENUM('tier-1', 'tier-2', 'tier-3');
  CREATE TYPE "public"."enum__articles_v_version_status" AS ENUM('draft', 'published');
  CREATE TYPE "public"."enum_pages_status" AS ENUM('draft', 'published');
  CREATE TYPE "public"."enum__pages_v_version_status" AS ENUM('draft', 'published');
  CREATE TYPE "public"."enum_processed_urls_status" AS ENUM('pending', 'accepted', 'rejected', 'error');
  CREATE TYPE "public"."enum_rss_feeds_language" AS ENUM('de', 'en');
  CREATE TYPE "public"."enum_rss_feeds_priority" AS ENUM('low', 'medium', 'high');
  CREATE TYPE "public"."enum_header_nav_items_link_type" AS ENUM('reference', 'custom');
  CREATE TYPE "public"."enum_footer_navigation_sections_links_link_type" AS ENUM('reference', 'custom');
  CREATE TYPE "public"."enum_footer_social_links_platform" AS ENUM('instagram', 'facebook', 'twitter', 'linkedin', 'youtube', 'github', 'threads', 'email');
  CREATE TYPE "public"."enum_footer_legal_links_link_type" AS ENUM('reference', 'custom');
  CREATE TABLE "users" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"name" varchar,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"email" varchar NOT NULL,
  	"reset_password_token" varchar,
  	"reset_password_expiration" timestamp(3) with time zone,
  	"salt" varchar,
  	"hash" varchar,
  	"login_attempts" numeric DEFAULT 0,
  	"lock_until" timestamp(3) with time zone
  );
  
  CREATE TABLE "articles_related_companies" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"name" varchar,
  	"ticker" varchar,
  	"exchange" varchar,
  	"relevance" "enum_articles_related_companies_relevance" DEFAULT 'medium',
  	"confidence" numeric DEFAULT 100,
  	"featured" boolean DEFAULT false
  );
  
  CREATE TABLE "articles_english_tab_keywords" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"keyword" varchar
  );
  
  CREATE TABLE "articles_english_tab_enhanced_key_insights" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"insight" varchar
  );
  
  CREATE TABLE "articles_german_tab_german_keywords" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"keyword" varchar
  );
  
  CREATE TABLE "articles_german_tab_german_key_insights" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"insight" varchar
  );
  
  CREATE TABLE "articles" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"title" varchar,
  	"slug" varchar,
  	"featured_image_id" integer,
  	"article_type" "enum_articles_article_type" DEFAULT 'curated',
  	"workflow_stage" "enum_articles_workflow_stage" DEFAULT 'curated-draft',
  	"placement" "enum_articles_placement",
  	"pinned" boolean DEFAULT false,
  	"trending" boolean DEFAULT false,
  	"read_time_minutes" numeric,
  	"published_by_id" integer,
  	"published_at" timestamp(3) with time zone,
  	"has_been_enhanced" boolean DEFAULT false,
  	"has_german_translation" boolean DEFAULT false,
  	"has_original_source" boolean DEFAULT false,
  	"english_tab_enhanced_title" varchar,
  	"english_tab_enhanced_summary" varchar,
  	"english_tab_enhanced_content" jsonb,
  	"sources_tab_source_feed_id" integer,
  	"sources_tab_source_url" varchar,
  	"sources_tab_original_published_at" timestamp(3) with time zone,
  	"sources_tab_original_title" varchar,
  	"sources_tab_original_summary" varchar,
  	"sources_tab_original_content" jsonb,
  	"german_tab_german_title" varchar,
  	"german_tab_german_summary" varchar,
  	"german_tab_german_content" jsonb,
  	"meta_title" varchar,
  	"meta_description" varchar,
  	"meta_image_id" integer,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"_status" "enum_articles_status" DEFAULT 'draft'
  );
  
  CREATE TABLE "articles_rels" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"order" integer,
  	"parent_id" integer NOT NULL,
  	"path" varchar NOT NULL,
  	"categories_id" integer
  );
  
  CREATE TABLE "_articles_v_version_related_companies" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"name" varchar,
  	"ticker" varchar,
  	"exchange" varchar,
  	"relevance" "enum__articles_v_version_related_companies_relevance" DEFAULT 'medium',
  	"confidence" numeric DEFAULT 100,
  	"featured" boolean DEFAULT false,
  	"_uuid" varchar
  );
  
  CREATE TABLE "_articles_v_version_english_tab_keywords" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"keyword" varchar,
  	"_uuid" varchar
  );
  
  CREATE TABLE "_articles_v_version_english_tab_enhanced_key_insights" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"insight" varchar,
  	"_uuid" varchar
  );
  
  CREATE TABLE "_articles_v_version_german_tab_german_keywords" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"keyword" varchar,
  	"_uuid" varchar
  );
  
  CREATE TABLE "_articles_v_version_german_tab_german_key_insights" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"insight" varchar,
  	"_uuid" varchar
  );
  
  CREATE TABLE "_articles_v" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"parent_id" integer,
  	"version_title" varchar,
  	"version_slug" varchar,
  	"version_featured_image_id" integer,
  	"version_article_type" "enum__articles_v_version_article_type" DEFAULT 'curated',
  	"version_workflow_stage" "enum__articles_v_version_workflow_stage" DEFAULT 'curated-draft',
  	"version_placement" "enum__articles_v_version_placement",
  	"version_pinned" boolean DEFAULT false,
  	"version_trending" boolean DEFAULT false,
  	"version_read_time_minutes" numeric,
  	"version_published_by_id" integer,
  	"version_published_at" timestamp(3) with time zone,
  	"version_has_been_enhanced" boolean DEFAULT false,
  	"version_has_german_translation" boolean DEFAULT false,
  	"version_has_original_source" boolean DEFAULT false,
  	"version_english_tab_enhanced_title" varchar,
  	"version_english_tab_enhanced_summary" varchar,
  	"version_english_tab_enhanced_content" jsonb,
  	"version_sources_tab_source_feed_id" integer,
  	"version_sources_tab_source_url" varchar,
  	"version_sources_tab_original_published_at" timestamp(3) with time zone,
  	"version_sources_tab_original_title" varchar,
  	"version_sources_tab_original_summary" varchar,
  	"version_sources_tab_original_content" jsonb,
  	"version_german_tab_german_title" varchar,
  	"version_german_tab_german_summary" varchar,
  	"version_german_tab_german_content" jsonb,
  	"version_meta_title" varchar,
  	"version_meta_description" varchar,
  	"version_meta_image_id" integer,
  	"version_updated_at" timestamp(3) with time zone,
  	"version_created_at" timestamp(3) with time zone,
  	"version__status" "enum__articles_v_version_status" DEFAULT 'draft',
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"latest" boolean
  );
  
  CREATE TABLE "_articles_v_rels" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"order" integer,
  	"parent_id" integer NOT NULL,
  	"path" varchar NOT NULL,
  	"categories_id" integer
  );
  
  CREATE TABLE "categories" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"title" varchar NOT NULL,
  	"english" varchar NOT NULL,
  	"slug" varchar,
  	"slug_lock" boolean DEFAULT true,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE "pages" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"title" varchar,
  	"featured_image_id" integer,
  	"english_tab_title" varchar,
  	"english_tab_content" jsonb,
  	"german_tab_german_title" varchar,
  	"german_tab_german_content" jsonb,
  	"meta_title" varchar,
  	"meta_image_id" integer,
  	"meta_description" varchar,
  	"published_at" timestamp(3) with time zone,
  	"slug" varchar,
  	"slug_lock" boolean DEFAULT true,
  	"parent_id" integer,
  	"enable_breadcrumbs" boolean DEFAULT true,
  	"has_german_translation" boolean DEFAULT false,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"_status" "enum_pages_status" DEFAULT 'draft'
  );
  
  CREATE TABLE "_pages_v" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"parent_id" integer,
  	"version_title" varchar,
  	"version_featured_image_id" integer,
  	"version_english_tab_title" varchar,
  	"version_english_tab_content" jsonb,
  	"version_german_tab_german_title" varchar,
  	"version_german_tab_german_content" jsonb,
  	"version_meta_title" varchar,
  	"version_meta_image_id" integer,
  	"version_meta_description" varchar,
  	"version_published_at" timestamp(3) with time zone,
  	"version_slug" varchar,
  	"version_slug_lock" boolean DEFAULT true,
  	"version_parent_id" integer,
  	"version_enable_breadcrumbs" boolean DEFAULT true,
  	"version_has_german_translation" boolean DEFAULT false,
  	"version_updated_at" timestamp(3) with time zone,
  	"version_created_at" timestamp(3) with time zone,
  	"version__status" "enum__pages_v_version_status" DEFAULT 'draft',
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"latest" boolean
  );
  
  CREATE TABLE "keywords" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"keyword" varchar NOT NULL,
  	"english_keyword" varchar NOT NULL,
  	"is_active" boolean DEFAULT true,
  	"usage_count" numeric DEFAULT 0,
  	"description" varchar,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE "processed_urls" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"url" varchar NOT NULL,
  	"status" "enum_processed_urls_status" DEFAULT 'pending' NOT NULL,
  	"title" varchar,
  	"publication_date" timestamp(3) with time zone,
  	"feed_id_id" integer,
  	"processed_at" timestamp(3) with time zone,
  	"reason" varchar,
  	"article_id_id" integer,
  	"attempt_count" numeric DEFAULT 0,
  	"last_attempt_at" timestamp(3) with time zone,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE "rss_feeds_firecrawl_options_exclude_tags" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"tag" varchar NOT NULL
  );
  
  CREATE TABLE "rss_feeds_firecrawl_options_include_tags" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"tag" varchar NOT NULL
  );
  
  CREATE TABLE "rss_feeds_keyword_filtering_custom_keywords" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"keyword" varchar NOT NULL,
  	"english_keyword" varchar NOT NULL,
  	"weight" numeric DEFAULT 5
  );
  
  CREATE TABLE "rss_feeds" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"name" varchar NOT NULL,
  	"url" varchar NOT NULL,
  	"is_active" boolean DEFAULT true,
  	"language" "enum_rss_feeds_language" DEFAULT 'de',
  	"last_processed" timestamp(3) with time zone,
  	"last_checked" timestamp(3) with time zone,
  	"last_successful_check" timestamp(3) with time zone,
  	"items_processed" numeric DEFAULT 0,
  	"items_accepted" numeric DEFAULT 0,
  	"articles_found_since_last_successful" numeric,
  	"total_articles_accepted" numeric,
  	"error_count" numeric DEFAULT 0,
  	"last_error_message" varchar,
  	"processing_frequency" numeric DEFAULT 60,
  	"priority" "enum_rss_feeds_priority" DEFAULT 'medium',
  	"firecrawl_options_remove_base64_images" boolean DEFAULT true,
  	"firecrawl_options_block_ads" boolean DEFAULT true,
  	"keyword_filtering_strict_keyword_matching" boolean,
  	"processing_options_max_firecrawl_scrape" numeric,
  	"processing_options_max_articles_per_run" numeric,
  	"processing_options_skip_translation" boolean,
  	"processing_options_skip_enhancement" boolean,
  	"processing_options_custom_timeout" numeric,
  	"processing_options_enable_stealth" boolean,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE "media" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"alt" varchar NOT NULL,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"url" varchar,
  	"thumbnail_u_r_l" varchar,
  	"filename" varchar,
  	"mime_type" varchar,
  	"filesize" numeric,
  	"width" numeric,
  	"height" numeric,
  	"focal_x" numeric,
  	"focal_y" numeric,
  	"sizes_thumbnail_url" varchar,
  	"sizes_thumbnail_width" numeric,
  	"sizes_thumbnail_height" numeric,
  	"sizes_thumbnail_mime_type" varchar,
  	"sizes_thumbnail_filesize" numeric,
  	"sizes_thumbnail_filename" varchar,
  	"sizes_horizontal_url" varchar,
  	"sizes_horizontal_width" numeric,
  	"sizes_horizontal_height" numeric,
  	"sizes_horizontal_mime_type" varchar,
  	"sizes_horizontal_filesize" numeric,
  	"sizes_horizontal_filename" varchar,
  	"sizes_card_url" varchar,
  	"sizes_card_width" numeric,
  	"sizes_card_height" numeric,
  	"sizes_card_mime_type" varchar,
  	"sizes_card_filesize" numeric,
  	"sizes_card_filename" varchar,
  	"sizes_social_url" varchar,
  	"sizes_social_width" numeric,
  	"sizes_social_height" numeric,
  	"sizes_social_mime_type" varchar,
  	"sizes_social_filesize" numeric,
  	"sizes_social_filename" varchar,
  	"sizes_hero_url" varchar,
  	"sizes_hero_width" numeric,
  	"sizes_hero_height" numeric,
  	"sizes_hero_mime_type" varchar,
  	"sizes_hero_filesize" numeric,
  	"sizes_hero_filename" varchar
  );
  
  CREATE TABLE "payload_locked_documents" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"global_slug" varchar,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE "payload_locked_documents_rels" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"order" integer,
  	"parent_id" integer NOT NULL,
  	"path" varchar NOT NULL,
  	"users_id" integer,
  	"articles_id" integer,
  	"categories_id" integer,
  	"pages_id" integer,
  	"keywords_id" integer,
  	"processed_urls_id" integer,
  	"rss_feeds_id" integer,
  	"media_id" integer
  );
  
  CREATE TABLE "payload_preferences" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"key" varchar,
  	"value" jsonb,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE "payload_preferences_rels" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"order" integer,
  	"parent_id" integer NOT NULL,
  	"path" varchar NOT NULL,
  	"users_id" integer
  );
  
  CREATE TABLE "payload_migrations" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"name" varchar,
  	"batch" numeric,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE "header_nav_items" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"link_type" "enum_header_nav_items_link_type" DEFAULT 'reference',
  	"link_new_tab" boolean,
  	"link_url" varchar,
  	"link_label" varchar NOT NULL
  );
  
  CREATE TABLE "header" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"updated_at" timestamp(3) with time zone,
  	"created_at" timestamp(3) with time zone
  );
  
  CREATE TABLE "header_rels" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"order" integer,
  	"parent_id" integer NOT NULL,
  	"path" varchar NOT NULL,
  	"articles_id" integer,
  	"categories_id" integer,
  	"pages_id" integer
  );
  
  CREATE TABLE "footer_navigation_sections_links" (
  	"_order" integer NOT NULL,
  	"_parent_id" varchar NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"link_type" "enum_footer_navigation_sections_links_link_type" DEFAULT 'reference',
  	"link_new_tab" boolean,
  	"link_url" varchar,
  	"link_label" varchar NOT NULL
  );
  
  CREATE TABLE "footer_navigation_sections" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"title" varchar NOT NULL
  );
  
  CREATE TABLE "footer_social_links" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"platform" "enum_footer_social_links_platform" NOT NULL,
  	"url" varchar NOT NULL,
  	"label" varchar NOT NULL
  );
  
  CREATE TABLE "footer_legal_links" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"link_type" "enum_footer_legal_links_link_type" DEFAULT 'reference',
  	"link_new_tab" boolean,
  	"link_url" varchar,
  	"link_label" varchar NOT NULL
  );
  
  CREATE TABLE "footer" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"logo_image_id" integer,
  	"logo_title" varchar DEFAULT 'Börsen Blick' NOT NULL,
  	"logo_url" varchar DEFAULT '/',
  	"description" varchar,
  	"copyright_company_name" varchar DEFAULT 'börsenblick.de' NOT NULL,
  	"copyright_custom_text" varchar,
  	"updated_at" timestamp(3) with time zone,
  	"created_at" timestamp(3) with time zone
  );
  
  CREATE TABLE "footer_rels" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"order" integer,
  	"parent_id" integer NOT NULL,
  	"path" varchar NOT NULL,
  	"articles_id" integer,
  	"categories_id" integer,
  	"pages_id" integer
  );
  
  ALTER TABLE "articles_related_companies" ADD CONSTRAINT "articles_related_companies_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."articles"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "articles_english_tab_keywords" ADD CONSTRAINT "articles_english_tab_keywords_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."articles"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "articles_english_tab_enhanced_key_insights" ADD CONSTRAINT "articles_english_tab_enhanced_key_insights_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."articles"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "articles_german_tab_german_keywords" ADD CONSTRAINT "articles_german_tab_german_keywords_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."articles"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "articles_german_tab_german_key_insights" ADD CONSTRAINT "articles_german_tab_german_key_insights_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."articles"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "articles" ADD CONSTRAINT "articles_featured_image_id_media_id_fk" FOREIGN KEY ("featured_image_id") REFERENCES "public"."media"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "articles" ADD CONSTRAINT "articles_published_by_id_users_id_fk" FOREIGN KEY ("published_by_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "articles" ADD CONSTRAINT "articles_sources_tab_source_feed_id_rss_feeds_id_fk" FOREIGN KEY ("sources_tab_source_feed_id") REFERENCES "public"."rss_feeds"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "articles" ADD CONSTRAINT "articles_meta_image_id_media_id_fk" FOREIGN KEY ("meta_image_id") REFERENCES "public"."media"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "articles_rels" ADD CONSTRAINT "articles_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."articles"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "articles_rels" ADD CONSTRAINT "articles_rels_categories_fk" FOREIGN KEY ("categories_id") REFERENCES "public"."categories"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_articles_v_version_related_companies" ADD CONSTRAINT "_articles_v_version_related_companies_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_articles_v"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_articles_v_version_english_tab_keywords" ADD CONSTRAINT "_articles_v_version_english_tab_keywords_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_articles_v"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_articles_v_version_english_tab_enhanced_key_insights" ADD CONSTRAINT "_articles_v_version_english_tab_enhanced_key_insights_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_articles_v"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_articles_v_version_german_tab_german_keywords" ADD CONSTRAINT "_articles_v_version_german_tab_german_keywords_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_articles_v"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_articles_v_version_german_tab_german_key_insights" ADD CONSTRAINT "_articles_v_version_german_tab_german_key_insights_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_articles_v"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_articles_v" ADD CONSTRAINT "_articles_v_parent_id_articles_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."articles"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "_articles_v" ADD CONSTRAINT "_articles_v_version_featured_image_id_media_id_fk" FOREIGN KEY ("version_featured_image_id") REFERENCES "public"."media"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "_articles_v" ADD CONSTRAINT "_articles_v_version_published_by_id_users_id_fk" FOREIGN KEY ("version_published_by_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "_articles_v" ADD CONSTRAINT "_articles_v_version_sources_tab_source_feed_id_rss_feeds_id_fk" FOREIGN KEY ("version_sources_tab_source_feed_id") REFERENCES "public"."rss_feeds"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "_articles_v" ADD CONSTRAINT "_articles_v_version_meta_image_id_media_id_fk" FOREIGN KEY ("version_meta_image_id") REFERENCES "public"."media"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "_articles_v_rels" ADD CONSTRAINT "_articles_v_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."_articles_v"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_articles_v_rels" ADD CONSTRAINT "_articles_v_rels_categories_fk" FOREIGN KEY ("categories_id") REFERENCES "public"."categories"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "pages" ADD CONSTRAINT "pages_featured_image_id_media_id_fk" FOREIGN KEY ("featured_image_id") REFERENCES "public"."media"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "pages" ADD CONSTRAINT "pages_meta_image_id_media_id_fk" FOREIGN KEY ("meta_image_id") REFERENCES "public"."media"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "pages" ADD CONSTRAINT "pages_parent_id_pages_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."pages"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "_pages_v" ADD CONSTRAINT "_pages_v_parent_id_pages_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."pages"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "_pages_v" ADD CONSTRAINT "_pages_v_version_featured_image_id_media_id_fk" FOREIGN KEY ("version_featured_image_id") REFERENCES "public"."media"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "_pages_v" ADD CONSTRAINT "_pages_v_version_meta_image_id_media_id_fk" FOREIGN KEY ("version_meta_image_id") REFERENCES "public"."media"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "_pages_v" ADD CONSTRAINT "_pages_v_version_parent_id_pages_id_fk" FOREIGN KEY ("version_parent_id") REFERENCES "public"."pages"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "processed_urls" ADD CONSTRAINT "processed_urls_feed_id_id_rss_feeds_id_fk" FOREIGN KEY ("feed_id_id") REFERENCES "public"."rss_feeds"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "processed_urls" ADD CONSTRAINT "processed_urls_article_id_id_articles_id_fk" FOREIGN KEY ("article_id_id") REFERENCES "public"."articles"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "rss_feeds_firecrawl_options_exclude_tags" ADD CONSTRAINT "rss_feeds_firecrawl_options_exclude_tags_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."rss_feeds"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "rss_feeds_firecrawl_options_include_tags" ADD CONSTRAINT "rss_feeds_firecrawl_options_include_tags_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."rss_feeds"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "rss_feeds_keyword_filtering_custom_keywords" ADD CONSTRAINT "rss_feeds_keyword_filtering_custom_keywords_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."rss_feeds"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."payload_locked_documents"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_users_fk" FOREIGN KEY ("users_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_articles_fk" FOREIGN KEY ("articles_id") REFERENCES "public"."articles"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_categories_fk" FOREIGN KEY ("categories_id") REFERENCES "public"."categories"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_pages_fk" FOREIGN KEY ("pages_id") REFERENCES "public"."pages"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_keywords_fk" FOREIGN KEY ("keywords_id") REFERENCES "public"."keywords"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_processed_urls_fk" FOREIGN KEY ("processed_urls_id") REFERENCES "public"."processed_urls"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_rss_feeds_fk" FOREIGN KEY ("rss_feeds_id") REFERENCES "public"."rss_feeds"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_media_fk" FOREIGN KEY ("media_id") REFERENCES "public"."media"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_preferences_rels" ADD CONSTRAINT "payload_preferences_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."payload_preferences"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_preferences_rels" ADD CONSTRAINT "payload_preferences_rels_users_fk" FOREIGN KEY ("users_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "header_nav_items" ADD CONSTRAINT "header_nav_items_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."header"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "header_rels" ADD CONSTRAINT "header_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."header"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "header_rels" ADD CONSTRAINT "header_rels_articles_fk" FOREIGN KEY ("articles_id") REFERENCES "public"."articles"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "header_rels" ADD CONSTRAINT "header_rels_categories_fk" FOREIGN KEY ("categories_id") REFERENCES "public"."categories"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "header_rels" ADD CONSTRAINT "header_rels_pages_fk" FOREIGN KEY ("pages_id") REFERENCES "public"."pages"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "footer_navigation_sections_links" ADD CONSTRAINT "footer_navigation_sections_links_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."footer_navigation_sections"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "footer_navigation_sections" ADD CONSTRAINT "footer_navigation_sections_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."footer"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "footer_social_links" ADD CONSTRAINT "footer_social_links_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."footer"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "footer_legal_links" ADD CONSTRAINT "footer_legal_links_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."footer"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "footer" ADD CONSTRAINT "footer_logo_image_id_media_id_fk" FOREIGN KEY ("logo_image_id") REFERENCES "public"."media"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "footer_rels" ADD CONSTRAINT "footer_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."footer"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "footer_rels" ADD CONSTRAINT "footer_rels_articles_fk" FOREIGN KEY ("articles_id") REFERENCES "public"."articles"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "footer_rels" ADD CONSTRAINT "footer_rels_categories_fk" FOREIGN KEY ("categories_id") REFERENCES "public"."categories"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "footer_rels" ADD CONSTRAINT "footer_rels_pages_fk" FOREIGN KEY ("pages_id") REFERENCES "public"."pages"("id") ON DELETE cascade ON UPDATE no action;
  CREATE INDEX "users_updated_at_idx" ON "users" USING btree ("updated_at");
  CREATE INDEX "users_created_at_idx" ON "users" USING btree ("created_at");
  CREATE UNIQUE INDEX "users_email_idx" ON "users" USING btree ("email");
  CREATE INDEX "articles_related_companies_order_idx" ON "articles_related_companies" USING btree ("_order");
  CREATE INDEX "articles_related_companies_parent_id_idx" ON "articles_related_companies" USING btree ("_parent_id");
  CREATE INDEX "articles_english_tab_keywords_order_idx" ON "articles_english_tab_keywords" USING btree ("_order");
  CREATE INDEX "articles_english_tab_keywords_parent_id_idx" ON "articles_english_tab_keywords" USING btree ("_parent_id");
  CREATE INDEX "articles_english_tab_enhanced_key_insights_order_idx" ON "articles_english_tab_enhanced_key_insights" USING btree ("_order");
  CREATE INDEX "articles_english_tab_enhanced_key_insights_parent_id_idx" ON "articles_english_tab_enhanced_key_insights" USING btree ("_parent_id");
  CREATE INDEX "articles_german_tab_german_keywords_order_idx" ON "articles_german_tab_german_keywords" USING btree ("_order");
  CREATE INDEX "articles_german_tab_german_keywords_parent_id_idx" ON "articles_german_tab_german_keywords" USING btree ("_parent_id");
  CREATE INDEX "articles_german_tab_german_key_insights_order_idx" ON "articles_german_tab_german_key_insights" USING btree ("_order");
  CREATE INDEX "articles_german_tab_german_key_insights_parent_id_idx" ON "articles_german_tab_german_key_insights" USING btree ("_parent_id");
  CREATE INDEX "articles_featured_image_idx" ON "articles" USING btree ("featured_image_id");
  CREATE INDEX "articles_trending_idx" ON "articles" USING btree ("trending");
  CREATE INDEX "articles_published_by_idx" ON "articles" USING btree ("published_by_id");
  CREATE INDEX "articles_sources_tab_sources_tab_source_feed_idx" ON "articles" USING btree ("sources_tab_source_feed_id");
  CREATE INDEX "articles_meta_meta_image_idx" ON "articles" USING btree ("meta_image_id");
  CREATE INDEX "articles_updated_at_idx" ON "articles" USING btree ("updated_at");
  CREATE INDEX "articles_created_at_idx" ON "articles" USING btree ("created_at");
  CREATE INDEX "articles__status_idx" ON "articles" USING btree ("_status");
  CREATE INDEX "articles_rels_order_idx" ON "articles_rels" USING btree ("order");
  CREATE INDEX "articles_rels_parent_idx" ON "articles_rels" USING btree ("parent_id");
  CREATE INDEX "articles_rels_path_idx" ON "articles_rels" USING btree ("path");
  CREATE INDEX "articles_rels_categories_id_idx" ON "articles_rels" USING btree ("categories_id");
  CREATE INDEX "_articles_v_version_related_companies_order_idx" ON "_articles_v_version_related_companies" USING btree ("_order");
  CREATE INDEX "_articles_v_version_related_companies_parent_id_idx" ON "_articles_v_version_related_companies" USING btree ("_parent_id");
  CREATE INDEX "_articles_v_version_english_tab_keywords_order_idx" ON "_articles_v_version_english_tab_keywords" USING btree ("_order");
  CREATE INDEX "_articles_v_version_english_tab_keywords_parent_id_idx" ON "_articles_v_version_english_tab_keywords" USING btree ("_parent_id");
  CREATE INDEX "_articles_v_version_english_tab_enhanced_key_insights_order_idx" ON "_articles_v_version_english_tab_enhanced_key_insights" USING btree ("_order");
  CREATE INDEX "_articles_v_version_english_tab_enhanced_key_insights_parent_id_idx" ON "_articles_v_version_english_tab_enhanced_key_insights" USING btree ("_parent_id");
  CREATE INDEX "_articles_v_version_german_tab_german_keywords_order_idx" ON "_articles_v_version_german_tab_german_keywords" USING btree ("_order");
  CREATE INDEX "_articles_v_version_german_tab_german_keywords_parent_id_idx" ON "_articles_v_version_german_tab_german_keywords" USING btree ("_parent_id");
  CREATE INDEX "_articles_v_version_german_tab_german_key_insights_order_idx" ON "_articles_v_version_german_tab_german_key_insights" USING btree ("_order");
  CREATE INDEX "_articles_v_version_german_tab_german_key_insights_parent_id_idx" ON "_articles_v_version_german_tab_german_key_insights" USING btree ("_parent_id");
  CREATE INDEX "_articles_v_parent_idx" ON "_articles_v" USING btree ("parent_id");
  CREATE INDEX "_articles_v_version_version_featured_image_idx" ON "_articles_v" USING btree ("version_featured_image_id");
  CREATE INDEX "_articles_v_version_version_trending_idx" ON "_articles_v" USING btree ("version_trending");
  CREATE INDEX "_articles_v_version_version_published_by_idx" ON "_articles_v" USING btree ("version_published_by_id");
  CREATE INDEX "_articles_v_version_sources_tab_version_sources_tab_source_feed_idx" ON "_articles_v" USING btree ("version_sources_tab_source_feed_id");
  CREATE INDEX "_articles_v_version_meta_version_meta_image_idx" ON "_articles_v" USING btree ("version_meta_image_id");
  CREATE INDEX "_articles_v_version_version_updated_at_idx" ON "_articles_v" USING btree ("version_updated_at");
  CREATE INDEX "_articles_v_version_version_created_at_idx" ON "_articles_v" USING btree ("version_created_at");
  CREATE INDEX "_articles_v_version_version__status_idx" ON "_articles_v" USING btree ("version__status");
  CREATE INDEX "_articles_v_created_at_idx" ON "_articles_v" USING btree ("created_at");
  CREATE INDEX "_articles_v_updated_at_idx" ON "_articles_v" USING btree ("updated_at");
  CREATE INDEX "_articles_v_latest_idx" ON "_articles_v" USING btree ("latest");
  CREATE INDEX "_articles_v_rels_order_idx" ON "_articles_v_rels" USING btree ("order");
  CREATE INDEX "_articles_v_rels_parent_idx" ON "_articles_v_rels" USING btree ("parent_id");
  CREATE INDEX "_articles_v_rels_path_idx" ON "_articles_v_rels" USING btree ("path");
  CREATE INDEX "_articles_v_rels_categories_id_idx" ON "_articles_v_rels" USING btree ("categories_id");
  CREATE UNIQUE INDEX "categories_slug_idx" ON "categories" USING btree ("slug");
  CREATE INDEX "categories_updated_at_idx" ON "categories" USING btree ("updated_at");
  CREATE INDEX "categories_created_at_idx" ON "categories" USING btree ("created_at");
  CREATE INDEX "pages_featured_image_idx" ON "pages" USING btree ("featured_image_id");
  CREATE INDEX "pages_meta_meta_image_idx" ON "pages" USING btree ("meta_image_id");
  CREATE UNIQUE INDEX "pages_slug_idx" ON "pages" USING btree ("slug");
  CREATE INDEX "pages_parent_idx" ON "pages" USING btree ("parent_id");
  CREATE INDEX "pages_updated_at_idx" ON "pages" USING btree ("updated_at");
  CREATE INDEX "pages_created_at_idx" ON "pages" USING btree ("created_at");
  CREATE INDEX "pages__status_idx" ON "pages" USING btree ("_status");
  CREATE INDEX "_pages_v_parent_idx" ON "_pages_v" USING btree ("parent_id");
  CREATE INDEX "_pages_v_version_version_featured_image_idx" ON "_pages_v" USING btree ("version_featured_image_id");
  CREATE INDEX "_pages_v_version_meta_version_meta_image_idx" ON "_pages_v" USING btree ("version_meta_image_id");
  CREATE INDEX "_pages_v_version_version_slug_idx" ON "_pages_v" USING btree ("version_slug");
  CREATE INDEX "_pages_v_version_version_parent_idx" ON "_pages_v" USING btree ("version_parent_id");
  CREATE INDEX "_pages_v_version_version_updated_at_idx" ON "_pages_v" USING btree ("version_updated_at");
  CREATE INDEX "_pages_v_version_version_created_at_idx" ON "_pages_v" USING btree ("version_created_at");
  CREATE INDEX "_pages_v_version_version__status_idx" ON "_pages_v" USING btree ("version__status");
  CREATE INDEX "_pages_v_created_at_idx" ON "_pages_v" USING btree ("created_at");
  CREATE INDEX "_pages_v_updated_at_idx" ON "_pages_v" USING btree ("updated_at");
  CREATE INDEX "_pages_v_latest_idx" ON "_pages_v" USING btree ("latest");
  CREATE UNIQUE INDEX "keywords_keyword_idx" ON "keywords" USING btree ("keyword");
  CREATE INDEX "keywords_updated_at_idx" ON "keywords" USING btree ("updated_at");
  CREATE INDEX "keywords_created_at_idx" ON "keywords" USING btree ("created_at");
  CREATE UNIQUE INDEX "processed_urls_url_idx" ON "processed_urls" USING btree ("url");
  CREATE INDEX "processed_urls_feed_id_idx" ON "processed_urls" USING btree ("feed_id_id");
  CREATE INDEX "processed_urls_article_id_idx" ON "processed_urls" USING btree ("article_id_id");
  CREATE INDEX "processed_urls_updated_at_idx" ON "processed_urls" USING btree ("updated_at");
  CREATE INDEX "processed_urls_created_at_idx" ON "processed_urls" USING btree ("created_at");
  CREATE INDEX "rss_feeds_firecrawl_options_exclude_tags_order_idx" ON "rss_feeds_firecrawl_options_exclude_tags" USING btree ("_order");
  CREATE INDEX "rss_feeds_firecrawl_options_exclude_tags_parent_id_idx" ON "rss_feeds_firecrawl_options_exclude_tags" USING btree ("_parent_id");
  CREATE INDEX "rss_feeds_firecrawl_options_include_tags_order_idx" ON "rss_feeds_firecrawl_options_include_tags" USING btree ("_order");
  CREATE INDEX "rss_feeds_firecrawl_options_include_tags_parent_id_idx" ON "rss_feeds_firecrawl_options_include_tags" USING btree ("_parent_id");
  CREATE INDEX "rss_feeds_keyword_filtering_custom_keywords_order_idx" ON "rss_feeds_keyword_filtering_custom_keywords" USING btree ("_order");
  CREATE INDEX "rss_feeds_keyword_filtering_custom_keywords_parent_id_idx" ON "rss_feeds_keyword_filtering_custom_keywords" USING btree ("_parent_id");
  CREATE UNIQUE INDEX "rss_feeds_url_idx" ON "rss_feeds" USING btree ("url");
  CREATE INDEX "rss_feeds_updated_at_idx" ON "rss_feeds" USING btree ("updated_at");
  CREATE INDEX "rss_feeds_created_at_idx" ON "rss_feeds" USING btree ("created_at");
  CREATE INDEX "media_updated_at_idx" ON "media" USING btree ("updated_at");
  CREATE INDEX "media_created_at_idx" ON "media" USING btree ("created_at");
  CREATE UNIQUE INDEX "media_filename_idx" ON "media" USING btree ("filename");
  CREATE INDEX "media_sizes_thumbnail_sizes_thumbnail_filename_idx" ON "media" USING btree ("sizes_thumbnail_filename");
  CREATE INDEX "media_sizes_horizontal_sizes_horizontal_filename_idx" ON "media" USING btree ("sizes_horizontal_filename");
  CREATE INDEX "media_sizes_card_sizes_card_filename_idx" ON "media" USING btree ("sizes_card_filename");
  CREATE INDEX "media_sizes_social_sizes_social_filename_idx" ON "media" USING btree ("sizes_social_filename");
  CREATE INDEX "media_sizes_hero_sizes_hero_filename_idx" ON "media" USING btree ("sizes_hero_filename");
  CREATE INDEX "payload_locked_documents_global_slug_idx" ON "payload_locked_documents" USING btree ("global_slug");
  CREATE INDEX "payload_locked_documents_updated_at_idx" ON "payload_locked_documents" USING btree ("updated_at");
  CREATE INDEX "payload_locked_documents_created_at_idx" ON "payload_locked_documents" USING btree ("created_at");
  CREATE INDEX "payload_locked_documents_rels_order_idx" ON "payload_locked_documents_rels" USING btree ("order");
  CREATE INDEX "payload_locked_documents_rels_parent_idx" ON "payload_locked_documents_rels" USING btree ("parent_id");
  CREATE INDEX "payload_locked_documents_rels_path_idx" ON "payload_locked_documents_rels" USING btree ("path");
  CREATE INDEX "payload_locked_documents_rels_users_id_idx" ON "payload_locked_documents_rels" USING btree ("users_id");
  CREATE INDEX "payload_locked_documents_rels_articles_id_idx" ON "payload_locked_documents_rels" USING btree ("articles_id");
  CREATE INDEX "payload_locked_documents_rels_categories_id_idx" ON "payload_locked_documents_rels" USING btree ("categories_id");
  CREATE INDEX "payload_locked_documents_rels_pages_id_idx" ON "payload_locked_documents_rels" USING btree ("pages_id");
  CREATE INDEX "payload_locked_documents_rels_keywords_id_idx" ON "payload_locked_documents_rels" USING btree ("keywords_id");
  CREATE INDEX "payload_locked_documents_rels_processed_urls_id_idx" ON "payload_locked_documents_rels" USING btree ("processed_urls_id");
  CREATE INDEX "payload_locked_documents_rels_rss_feeds_id_idx" ON "payload_locked_documents_rels" USING btree ("rss_feeds_id");
  CREATE INDEX "payload_locked_documents_rels_media_id_idx" ON "payload_locked_documents_rels" USING btree ("media_id");
  CREATE INDEX "payload_preferences_key_idx" ON "payload_preferences" USING btree ("key");
  CREATE INDEX "payload_preferences_updated_at_idx" ON "payload_preferences" USING btree ("updated_at");
  CREATE INDEX "payload_preferences_created_at_idx" ON "payload_preferences" USING btree ("created_at");
  CREATE INDEX "payload_preferences_rels_order_idx" ON "payload_preferences_rels" USING btree ("order");
  CREATE INDEX "payload_preferences_rels_parent_idx" ON "payload_preferences_rels" USING btree ("parent_id");
  CREATE INDEX "payload_preferences_rels_path_idx" ON "payload_preferences_rels" USING btree ("path");
  CREATE INDEX "payload_preferences_rels_users_id_idx" ON "payload_preferences_rels" USING btree ("users_id");
  CREATE INDEX "payload_migrations_updated_at_idx" ON "payload_migrations" USING btree ("updated_at");
  CREATE INDEX "payload_migrations_created_at_idx" ON "payload_migrations" USING btree ("created_at");
  CREATE INDEX "header_nav_items_order_idx" ON "header_nav_items" USING btree ("_order");
  CREATE INDEX "header_nav_items_parent_id_idx" ON "header_nav_items" USING btree ("_parent_id");
  CREATE INDEX "header_rels_order_idx" ON "header_rels" USING btree ("order");
  CREATE INDEX "header_rels_parent_idx" ON "header_rels" USING btree ("parent_id");
  CREATE INDEX "header_rels_path_idx" ON "header_rels" USING btree ("path");
  CREATE INDEX "header_rels_articles_id_idx" ON "header_rels" USING btree ("articles_id");
  CREATE INDEX "header_rels_categories_id_idx" ON "header_rels" USING btree ("categories_id");
  CREATE INDEX "header_rels_pages_id_idx" ON "header_rels" USING btree ("pages_id");
  CREATE INDEX "footer_navigation_sections_links_order_idx" ON "footer_navigation_sections_links" USING btree ("_order");
  CREATE INDEX "footer_navigation_sections_links_parent_id_idx" ON "footer_navigation_sections_links" USING btree ("_parent_id");
  CREATE INDEX "footer_navigation_sections_order_idx" ON "footer_navigation_sections" USING btree ("_order");
  CREATE INDEX "footer_navigation_sections_parent_id_idx" ON "footer_navigation_sections" USING btree ("_parent_id");
  CREATE INDEX "footer_social_links_order_idx" ON "footer_social_links" USING btree ("_order");
  CREATE INDEX "footer_social_links_parent_id_idx" ON "footer_social_links" USING btree ("_parent_id");
  CREATE INDEX "footer_legal_links_order_idx" ON "footer_legal_links" USING btree ("_order");
  CREATE INDEX "footer_legal_links_parent_id_idx" ON "footer_legal_links" USING btree ("_parent_id");
  CREATE INDEX "footer_logo_logo_image_idx" ON "footer" USING btree ("logo_image_id");
  CREATE INDEX "footer_rels_order_idx" ON "footer_rels" USING btree ("order");
  CREATE INDEX "footer_rels_parent_idx" ON "footer_rels" USING btree ("parent_id");
  CREATE INDEX "footer_rels_path_idx" ON "footer_rels" USING btree ("path");
  CREATE INDEX "footer_rels_articles_id_idx" ON "footer_rels" USING btree ("articles_id");
  CREATE INDEX "footer_rels_categories_id_idx" ON "footer_rels" USING btree ("categories_id");
  CREATE INDEX "footer_rels_pages_id_idx" ON "footer_rels" USING btree ("pages_id");`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   DROP TABLE "users" CASCADE;
  DROP TABLE "articles_related_companies" CASCADE;
  DROP TABLE "articles_english_tab_keywords" CASCADE;
  DROP TABLE "articles_english_tab_enhanced_key_insights" CASCADE;
  DROP TABLE "articles_german_tab_german_keywords" CASCADE;
  DROP TABLE "articles_german_tab_german_key_insights" CASCADE;
  DROP TABLE "articles" CASCADE;
  DROP TABLE "articles_rels" CASCADE;
  DROP TABLE "_articles_v_version_related_companies" CASCADE;
  DROP TABLE "_articles_v_version_english_tab_keywords" CASCADE;
  DROP TABLE "_articles_v_version_english_tab_enhanced_key_insights" CASCADE;
  DROP TABLE "_articles_v_version_german_tab_german_keywords" CASCADE;
  DROP TABLE "_articles_v_version_german_tab_german_key_insights" CASCADE;
  DROP TABLE "_articles_v" CASCADE;
  DROP TABLE "_articles_v_rels" CASCADE;
  DROP TABLE "categories" CASCADE;
  DROP TABLE "pages" CASCADE;
  DROP TABLE "_pages_v" CASCADE;
  DROP TABLE "keywords" CASCADE;
  DROP TABLE "processed_urls" CASCADE;
  DROP TABLE "rss_feeds_firecrawl_options_exclude_tags" CASCADE;
  DROP TABLE "rss_feeds_firecrawl_options_include_tags" CASCADE;
  DROP TABLE "rss_feeds_keyword_filtering_custom_keywords" CASCADE;
  DROP TABLE "rss_feeds" CASCADE;
  DROP TABLE "media" CASCADE;
  DROP TABLE "payload_locked_documents" CASCADE;
  DROP TABLE "payload_locked_documents_rels" CASCADE;
  DROP TABLE "payload_preferences" CASCADE;
  DROP TABLE "payload_preferences_rels" CASCADE;
  DROP TABLE "payload_migrations" CASCADE;
  DROP TABLE "header_nav_items" CASCADE;
  DROP TABLE "header" CASCADE;
  DROP TABLE "header_rels" CASCADE;
  DROP TABLE "footer_navigation_sections_links" CASCADE;
  DROP TABLE "footer_navigation_sections" CASCADE;
  DROP TABLE "footer_social_links" CASCADE;
  DROP TABLE "footer_legal_links" CASCADE;
  DROP TABLE "footer" CASCADE;
  DROP TABLE "footer_rels" CASCADE;
  DROP TYPE "public"."enum_articles_related_companies_relevance";
  DROP TYPE "public"."enum_articles_article_type";
  DROP TYPE "public"."enum_articles_workflow_stage";
  DROP TYPE "public"."enum_articles_placement";
  DROP TYPE "public"."enum_articles_status";
  DROP TYPE "public"."enum__articles_v_version_related_companies_relevance";
  DROP TYPE "public"."enum__articles_v_version_article_type";
  DROP TYPE "public"."enum__articles_v_version_workflow_stage";
  DROP TYPE "public"."enum__articles_v_version_placement";
  DROP TYPE "public"."enum__articles_v_version_status";
  DROP TYPE "public"."enum_pages_status";
  DROP TYPE "public"."enum__pages_v_version_status";
  DROP TYPE "public"."enum_processed_urls_status";
  DROP TYPE "public"."enum_rss_feeds_language";
  DROP TYPE "public"."enum_rss_feeds_priority";
  DROP TYPE "public"."enum_header_nav_items_link_type";
  DROP TYPE "public"."enum_footer_navigation_sections_links_link_type";
  DROP TYPE "public"."enum_footer_social_links_platform";
  DROP TYPE "public"."enum_footer_legal_links_link_type";`)
}
