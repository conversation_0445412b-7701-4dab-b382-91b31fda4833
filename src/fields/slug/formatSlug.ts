import type { FieldHook } from 'payload';

export const formatSlug = (val: string): string =>
  val
    .replace(/ /g, '-')
    .replace(/[^\w-]+/g, '')
    .toLowerCase();

/**
 * Generates a UTC timestamp string for unique slug suffixes
 * Format: YYYYMMDDHHMMSS (e.g., 20250103143027)
 * This provides year, month, day, hour, minute, and second precision
 */
export const generateTimestampSuffix = (): string => {
  const now = new Date();
  const year = now.getUTCFullYear().toString();
  const month = (now.getUTCMonth() + 1).toString().padStart(2, '0');
  const day = now.getUTCDate().toString().padStart(2, '0');
  const hour = now.getUTCHours().toString().padStart(2, '0');
  const minute = now.getUTCMinutes().toString().padStart(2, '0');
  const second = now.getUTCSeconds().toString().padStart(2, '0');

  return `${year}${month}${day}${hour}${minute}${second}`;
};

/**
 * Formats a slug with optional timestamp suffix for uniqueness
 * @param val - The base slug value
 * @param addTimestamp - Whether to add a timestamp suffix
 * @returns Formatted slug with optional timestamp
 */
export const formatSlugWithTimestamp = (
  val: string,
  addTimestamp: boolean = false
): string => {
  const baseSlug = formatSlug(val);

  if (addTimestamp && baseSlug) {
    return `${baseSlug}-${generateTimestampSuffix()}`;
  }

  return baseSlug;
};

export const formatSlugHook =
  (fallback: string): FieldHook =>
  ({ data, operation, value }) => {
    if (typeof value === 'string') {
      return formatSlug(value);
    }

    if (operation === 'create' || !data?.slug) {
      const fallbackData = data?.[fallback] || data?.[fallback];

      if (fallbackData && typeof fallbackData === 'string') {
        return formatSlug(fallbackData);
      }
    }

    return value;
  };

/**
 * Hook that formats slugs with timestamps for guaranteed uniqueness
 * This is particularly useful for fields with unique constraints
 */
export const formatSlugWithTimestampHook =
  (fallback: string, alwaysAddTimestamp: boolean = false): FieldHook =>
  ({ data, operation, value }) => {
    // If we have a direct value, format it and optionally add timestamp
    if (typeof value === 'string') {
      return formatSlugWithTimestamp(value, alwaysAddTimestamp);
    }

    // For create operations or when no slug exists, generate from fallback
    if (operation === 'create' || !data?.slug) {
      const fallbackData = data?.[fallback] || data?.[fallback];

      if (fallbackData && typeof fallbackData === 'string') {
        // Always add timestamp on create to ensure uniqueness
        return formatSlugWithTimestamp(fallbackData, true);
      }
    }

    return value;
  };
