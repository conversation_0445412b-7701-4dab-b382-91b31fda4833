import type { CheckboxField, TextField } from 'payload';

import {
  formatSlugHook,
  formatSlugWithTimestamp,
  formatSlugWithTimestampHook,
} from './formatSlug';

type Overrides = {
  slugOverrides?: Partial<TextField>;
  checkboxOverrides?: Partial<CheckboxField>;
};

type SlugOptions = {
  useTimestamp?: boolean;
  alwaysAddTimestamp?: boolean;
};

type Slug = (
  fieldToUse?: string,
  overrides?: Overrides,
  options?: SlugOptions
) => [TextField, CheckboxField];

export const slugField: Slug = (
  fieldToUse = 'title',
  overrides = {},
  options = {}
) => {
  const { slugOverrides, checkboxOverrides } = overrides;
  const { useTimestamp = false, alwaysAddTimestamp = false } = options;

  const checkBoxField: CheckboxField = {
    name: 'slugLock',
    type: 'checkbox',
    defaultValue: true,
    admin: {
      hidden: true,
      position: 'sidebar',
    },
    ...checkboxOverrides,
  };

  // Choose the appropriate hook based on timestamp option
  const slugHook = useTimestamp
    ? formatSlugWithTimestampHook(fieldToUse, alwaysAddTimestamp)
    : formatSlugHook(fieldToUse);

  // @ts-expect-error - Complex type merging requires assertion
  const slugFieldConfig: TextField = {
    name: 'slug',
    type: 'text',
    index: true,
    label: 'Slug',
    unique: true, // Ensure uniqueness at database level
    ...slugOverrides,
    hooks: {
      // Use the appropriate hook based on timestamp option
      beforeValidate: [slugHook],
      // Add beforeDuplicate hook for handling document duplication
      beforeDuplicate: [
        ({ value }: any) => {
          // Always add timestamp when duplicating to ensure uniqueness
          if (typeof value === 'string') {
            return formatSlugWithTimestamp(value, true);
          }
          return value;
        },
      ],
      // Merge any additional hooks from overrides
      ...(slugOverrides?.hooks || {}),
    },
    admin: {
      position: 'sidebar',
      description: useTimestamp
        ? 'URL-friendly identifier with timestamp for uniqueness'
        : 'URL-friendly identifier',
      ...(slugOverrides?.admin || {}),
      components: {
        Field: {
          path: '@/fields/slug/SlugComponent#SlugComponent',
          clientProps: {
            fieldToUse,
            checkboxFieldPath: checkBoxField.name,
          },
        },
      },
    },
  };

  return [slugFieldConfig, checkBoxField];
};
