PAYLOAD_SECRET=XXXXXX
DATABASE_URI=XXXXXX
OPENAI_API_KEY=XXXXXX
FIRECRAWL_API_KEY=XXXXXX

# Image Upload Configuration
MAX_IMAGE_FILE_SIZE=10485760
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/webp

# Cost Monitoring and Alerts
MONTHLY_IMAGE_BUDGET_USD=100
ALERT_THRESHOLD_USD=80

# PayloadCMS Draft Preview Configuration
NEXT_PUBLIC_SERVER_URL=XXXXXX
PAYLOAD_PUBLIC_DRAFT_SECRET=XXXXXX

# Note: PAYLOAD_PUBLIC_DRAFT_SECRET is a secure random string used to authenticate preview requests
# Generated with: openssl rand -hex 32
# For production, use a different secure random string

# Resend Email Configuration
RESEND_API_KEY=XXXXXX
RESEND_DEFAULT_FROM_ADDRESS=<EMAIL>
RESEND_DEFAULT_FROM_NAME=System Alerts - <PERSON><PERSON><PERSON> Blick

# Pipeline Email Notifications
PIPELINE_NOTIFICATION_EMAIL=XXXXXX


