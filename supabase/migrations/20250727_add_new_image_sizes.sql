-- Add new image size columns to media table
-- This adds support for horizontal, social, and hero image sizes

ALTER TABLE public.media 
ADD COLUMN IF NOT EXISTS sizes_horizontal_url varchar,
ADD COLUMN IF NOT EXISTS sizes_horizontal_width numeric,
ADD COLUMN IF NOT EXISTS sizes_horizontal_height numeric,
ADD COLUMN IF NOT EXISTS sizes_horizontal_mime_type varchar,
ADD COLUMN IF NOT EXISTS sizes_horizontal_filesize numeric,
ADD COLUMN IF NOT EXISTS sizes_horizontal_filename varchar,

ADD COLUMN IF NOT EXISTS sizes_social_url varchar,
ADD COLUMN IF NOT EXISTS sizes_social_width numeric,
ADD COLUMN IF NOT EXISTS sizes_social_height numeric,
ADD COLUMN IF NOT EXISTS sizes_social_mime_type varchar,
ADD COLUMN IF NOT EXISTS sizes_social_filesize numeric,
ADD COLUMN IF NOT EXISTS sizes_social_filename varchar,

ADD COLUMN IF NOT EXISTS sizes_hero_url varchar,
ADD COLUMN IF NOT EXISTS sizes_hero_width numeric,
ADD COLUMN IF NOT EXISTS sizes_hero_height numeric,
ADD COLUMN IF NOT EXISTS sizes_hero_mime_type varchar,
ADD COLUMN IF NOT EXISTS sizes_hero_filesize numeric,
ADD COLUMN IF NOT EXISTS sizes_hero_filename varchar;

-- Add indexes for the new filename columns (following existing pattern)
CREATE INDEX IF NOT EXISTS media_sizes_horizontal_sizes_horizontal_filename_idx 
ON public.media USING btree (sizes_horizontal_filename);

CREATE INDEX IF NOT EXISTS media_sizes_social_sizes_social_filename_idx 
ON public.media USING btree (sizes_social_filename);

CREATE INDEX IF NOT EXISTS media_sizes_hero_sizes_hero_filename_idx 
ON public.media USING btree (sizes_hero_filename);
