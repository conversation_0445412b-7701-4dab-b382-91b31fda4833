

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE SCHEMA IF NOT EXISTS "public";


ALTER SCHEMA "public" OWNER TO "pg_database_owner";


COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE TYPE "public"."enum__articles_v_version_article_type" AS ENUM (
    'generated',
    'curated'
);


ALTER TYPE "public"."enum__articles_v_version_article_type" OWNER TO "postgres";


CREATE TYPE "public"."enum__articles_v_version_placement" AS ENUM (
    'tier-1',
    'tier-2',
    'tier-3'
);


ALTER TYPE "public"."enum__articles_v_version_placement" OWNER TO "postgres";


CREATE TYPE "public"."enum__articles_v_version_related_companies_relevance" AS ENUM (
    'high',
    'medium',
    'low'
);


ALTER TYPE "public"."enum__articles_v_version_related_companies_relevance" OWNER TO "postgres";


CREATE TYPE "public"."enum__articles_v_version_status" AS ENUM (
    'draft',
    'published'
);


ALTER TYPE "public"."enum__articles_v_version_status" OWNER TO "postgres";


CREATE TYPE "public"."enum__articles_v_version_workflow_stage" AS ENUM (
    'curated-draft',
    'candidate-article',
    'translated',
    'ready-for-review'
);


ALTER TYPE "public"."enum__articles_v_version_workflow_stage" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_version_status" AS ENUM (
    'draft',
    'published'
);


ALTER TYPE "public"."enum__pages_v_version_status" OWNER TO "postgres";


CREATE TYPE "public"."enum_articles_article_type" AS ENUM (
    'generated',
    'curated'
);


ALTER TYPE "public"."enum_articles_article_type" OWNER TO "postgres";


CREATE TYPE "public"."enum_articles_placement" AS ENUM (
    'tier-1',
    'tier-2',
    'tier-3'
);


ALTER TYPE "public"."enum_articles_placement" OWNER TO "postgres";


CREATE TYPE "public"."enum_articles_related_companies_relevance" AS ENUM (
    'high',
    'medium',
    'low'
);


ALTER TYPE "public"."enum_articles_related_companies_relevance" OWNER TO "postgres";


CREATE TYPE "public"."enum_articles_status" AS ENUM (
    'draft',
    'published'
);


ALTER TYPE "public"."enum_articles_status" OWNER TO "postgres";


CREATE TYPE "public"."enum_articles_workflow_stage" AS ENUM (
    'curated-draft',
    'candidate-article',
    'translated',
    'ready-for-review'
);


ALTER TYPE "public"."enum_articles_workflow_stage" OWNER TO "postgres";


CREATE TYPE "public"."enum_footer_legal_links_link_type" AS ENUM (
    'reference',
    'custom'
);


ALTER TYPE "public"."enum_footer_legal_links_link_type" OWNER TO "postgres";


CREATE TYPE "public"."enum_footer_navigation_sections_links_link_type" AS ENUM (
    'reference',
    'custom'
);


ALTER TYPE "public"."enum_footer_navigation_sections_links_link_type" OWNER TO "postgres";


CREATE TYPE "public"."enum_footer_social_links_platform" AS ENUM (
    'instagram',
    'facebook',
    'twitter',
    'linkedin',
    'youtube',
    'github',
    'threads',
    'email'
);


ALTER TYPE "public"."enum_footer_social_links_platform" OWNER TO "postgres";


CREATE TYPE "public"."enum_header_nav_items_link_type" AS ENUM (
    'reference',
    'custom'
);


ALTER TYPE "public"."enum_header_nav_items_link_type" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_status" AS ENUM (
    'draft',
    'published'
);


ALTER TYPE "public"."enum_pages_status" OWNER TO "postgres";


CREATE TYPE "public"."enum_processed_urls_status" AS ENUM (
    'pending',
    'accepted',
    'rejected',
    'error'
);


ALTER TYPE "public"."enum_processed_urls_status" OWNER TO "postgres";


CREATE TYPE "public"."enum_rss_feeds_language" AS ENUM (
    'de',
    'en'
);


ALTER TYPE "public"."enum_rss_feeds_language" OWNER TO "postgres";


CREATE TYPE "public"."enum_rss_feeds_priority" AS ENUM (
    'low',
    'medium',
    'high'
);


ALTER TYPE "public"."enum_rss_feeds_priority" OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."_articles_v" (
    "id" integer NOT NULL,
    "parent_id" integer,
    "version_title" character varying,
    "version_slug" character varying,
    "version_featured_image_id" integer,
    "version_article_type" "public"."enum__articles_v_version_article_type" DEFAULT 'curated'::"public"."enum__articles_v_version_article_type",
    "version_workflow_stage" "public"."enum__articles_v_version_workflow_stage" DEFAULT 'curated-draft'::"public"."enum__articles_v_version_workflow_stage",
    "version_placement" "public"."enum__articles_v_version_placement",
    "version_pinned" boolean DEFAULT false,
    "version_trending" boolean DEFAULT false,
    "version_read_time_minutes" numeric,
    "version_published_by_id" integer,
    "version_published_at" timestamp(3) with time zone,
    "version_has_been_enhanced" boolean DEFAULT false,
    "version_has_german_translation" boolean DEFAULT false,
    "version_has_original_source" boolean DEFAULT false,
    "version_english_tab_enhanced_title" character varying,
    "version_english_tab_enhanced_summary" character varying,
    "version_english_tab_enhanced_content" "jsonb",
    "version_sources_tab_source_feed_id" integer,
    "version_sources_tab_source_url" character varying,
    "version_sources_tab_original_published_at" timestamp(3) with time zone,
    "version_sources_tab_original_title" character varying,
    "version_sources_tab_original_summary" character varying,
    "version_sources_tab_original_content" "jsonb",
    "version_german_tab_german_title" character varying,
    "version_german_tab_german_summary" character varying,
    "version_german_tab_german_content" "jsonb",
    "version_meta_title" character varying,
    "version_meta_description" character varying,
    "version_meta_image_id" integer,
    "version_updated_at" timestamp(3) with time zone,
    "version_created_at" timestamp(3) with time zone,
    "version__status" "public"."enum__articles_v_version_status" DEFAULT 'draft'::"public"."enum__articles_v_version_status",
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "latest" boolean
);


ALTER TABLE "public"."_articles_v" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_articles_v_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."_articles_v_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_articles_v_id_seq" OWNED BY "public"."_articles_v"."id";



CREATE TABLE IF NOT EXISTS "public"."_articles_v_rels" (
    "id" integer NOT NULL,
    "order" integer,
    "parent_id" integer NOT NULL,
    "path" character varying NOT NULL,
    "categories_id" integer
);


ALTER TABLE "public"."_articles_v_rels" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_articles_v_rels_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."_articles_v_rels_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_articles_v_rels_id_seq" OWNED BY "public"."_articles_v_rels"."id";



CREATE TABLE IF NOT EXISTS "public"."_articles_v_version_english_tab_enhanced_key_insights" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "insight" character varying,
    "_uuid" character varying
);


ALTER TABLE "public"."_articles_v_version_english_tab_enhanced_key_insights" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_articles_v_version_english_tab_enhanced_key_insights_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."_articles_v_version_english_tab_enhanced_key_insights_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_articles_v_version_english_tab_enhanced_key_insights_id_seq" OWNED BY "public"."_articles_v_version_english_tab_enhanced_key_insights"."id";



CREATE TABLE IF NOT EXISTS "public"."_articles_v_version_english_tab_keywords" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "keyword" character varying,
    "_uuid" character varying
);


ALTER TABLE "public"."_articles_v_version_english_tab_keywords" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_articles_v_version_english_tab_keywords_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."_articles_v_version_english_tab_keywords_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_articles_v_version_english_tab_keywords_id_seq" OWNED BY "public"."_articles_v_version_english_tab_keywords"."id";



CREATE TABLE IF NOT EXISTS "public"."_articles_v_version_german_tab_german_key_insights" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "insight" character varying,
    "_uuid" character varying
);


ALTER TABLE "public"."_articles_v_version_german_tab_german_key_insights" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_articles_v_version_german_tab_german_key_insights_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."_articles_v_version_german_tab_german_key_insights_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_articles_v_version_german_tab_german_key_insights_id_seq" OWNED BY "public"."_articles_v_version_german_tab_german_key_insights"."id";



CREATE TABLE IF NOT EXISTS "public"."_articles_v_version_german_tab_german_keywords" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "keyword" character varying,
    "_uuid" character varying
);


ALTER TABLE "public"."_articles_v_version_german_tab_german_keywords" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_articles_v_version_german_tab_german_keywords_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."_articles_v_version_german_tab_german_keywords_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_articles_v_version_german_tab_german_keywords_id_seq" OWNED BY "public"."_articles_v_version_german_tab_german_keywords"."id";



CREATE TABLE IF NOT EXISTS "public"."_articles_v_version_related_companies" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "name" character varying,
    "ticker" character varying,
    "exchange" character varying,
    "relevance" "public"."enum__articles_v_version_related_companies_relevance" DEFAULT 'medium'::"public"."enum__articles_v_version_related_companies_relevance",
    "confidence" numeric DEFAULT 100,
    "featured" boolean DEFAULT false,
    "_uuid" character varying
);


ALTER TABLE "public"."_articles_v_version_related_companies" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_articles_v_version_related_companies_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."_articles_v_version_related_companies_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_articles_v_version_related_companies_id_seq" OWNED BY "public"."_articles_v_version_related_companies"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v" (
    "id" integer NOT NULL,
    "parent_id" integer,
    "version_title" character varying,
    "version_has_german_translation" boolean DEFAULT false,
    "version_english_tab_title" character varying,
    "version_english_tab_content" "jsonb",
    "version_german_tab_german_title" character varying,
    "version_german_tab_german_content" "jsonb",
    "version_meta_title" character varying,
    "version_meta_image_id" integer,
    "version_meta_description" character varying,
    "version_published_at" timestamp(3) with time zone,
    "version_slug" character varying,
    "version_slug_lock" boolean DEFAULT true,
    "version_updated_at" timestamp(3) with time zone,
    "version_created_at" timestamp(3) with time zone,
    "version__status" "public"."enum__pages_v_version_status" DEFAULT 'draft'::"public"."enum__pages_v_version_status",
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "latest" boolean,
    "version_featured_image_id" integer,
    "version_parent_id" integer,
    "version_enable_breadcrumbs" boolean DEFAULT true
);


ALTER TABLE "public"."_pages_v" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."_pages_v_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_id_seq" OWNED BY "public"."_pages_v"."id";



CREATE TABLE IF NOT EXISTS "public"."articles" (
    "id" integer NOT NULL,
    "title" character varying,
    "slug" character varying,
    "featured_image_id" integer,
    "article_type" "public"."enum_articles_article_type" DEFAULT 'curated'::"public"."enum_articles_article_type",
    "workflow_stage" "public"."enum_articles_workflow_stage" DEFAULT 'curated-draft'::"public"."enum_articles_workflow_stage",
    "placement" "public"."enum_articles_placement",
    "pinned" boolean DEFAULT false,
    "trending" boolean DEFAULT false,
    "read_time_minutes" numeric,
    "published_by_id" integer,
    "published_at" timestamp(3) with time zone,
    "has_been_enhanced" boolean DEFAULT false,
    "has_german_translation" boolean DEFAULT false,
    "has_original_source" boolean DEFAULT false,
    "english_tab_enhanced_title" character varying,
    "english_tab_enhanced_summary" character varying,
    "english_tab_enhanced_content" "jsonb",
    "sources_tab_source_feed_id" integer,
    "sources_tab_source_url" character varying,
    "sources_tab_original_published_at" timestamp(3) with time zone,
    "sources_tab_original_title" character varying,
    "sources_tab_original_summary" character varying,
    "sources_tab_original_content" "jsonb",
    "german_tab_german_title" character varying,
    "german_tab_german_summary" character varying,
    "german_tab_german_content" "jsonb",
    "meta_title" character varying,
    "meta_description" character varying,
    "meta_image_id" integer,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "_status" "public"."enum_articles_status" DEFAULT 'draft'::"public"."enum_articles_status"
);


ALTER TABLE "public"."articles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."articles_english_tab_enhanced_key_insights" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "insight" character varying
);


ALTER TABLE "public"."articles_english_tab_enhanced_key_insights" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."articles_english_tab_keywords" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "keyword" character varying
);


ALTER TABLE "public"."articles_english_tab_keywords" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."articles_german_tab_german_key_insights" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "insight" character varying
);


ALTER TABLE "public"."articles_german_tab_german_key_insights" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."articles_german_tab_german_keywords" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "keyword" character varying
);


ALTER TABLE "public"."articles_german_tab_german_keywords" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."articles_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."articles_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."articles_id_seq" OWNED BY "public"."articles"."id";



CREATE TABLE IF NOT EXISTS "public"."articles_related_companies" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "name" character varying,
    "ticker" character varying,
    "exchange" character varying,
    "relevance" "public"."enum_articles_related_companies_relevance" DEFAULT 'medium'::"public"."enum_articles_related_companies_relevance",
    "confidence" numeric DEFAULT 100,
    "featured" boolean DEFAULT false
);


ALTER TABLE "public"."articles_related_companies" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."articles_rels" (
    "id" integer NOT NULL,
    "order" integer,
    "parent_id" integer NOT NULL,
    "path" character varying NOT NULL,
    "categories_id" integer
);


ALTER TABLE "public"."articles_rels" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."articles_rels_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."articles_rels_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."articles_rels_id_seq" OWNED BY "public"."articles_rels"."id";



CREATE TABLE IF NOT EXISTS "public"."categories" (
    "id" integer NOT NULL,
    "title" character varying NOT NULL,
    "english" character varying NOT NULL,
    "slug" character varying,
    "slug_lock" boolean DEFAULT true,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."categories" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."categories_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."categories_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."categories_id_seq" OWNED BY "public"."categories"."id";



CREATE TABLE IF NOT EXISTS "public"."footer" (
    "id" integer NOT NULL,
    "logo_image_id" integer,
    "logo_title" character varying DEFAULT 'Börsen Blick'::character varying NOT NULL,
    "logo_url" character varying DEFAULT '/'::character varying,
    "description" character varying,
    "copyright_company_name" character varying DEFAULT 'börsenblick.de'::character varying NOT NULL,
    "copyright_custom_text" character varying,
    "updated_at" timestamp(3) with time zone,
    "created_at" timestamp(3) with time zone
);


ALTER TABLE "public"."footer" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."footer_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."footer_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."footer_id_seq" OWNED BY "public"."footer"."id";



CREATE TABLE IF NOT EXISTS "public"."footer_legal_links" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "link_type" "public"."enum_footer_legal_links_link_type" DEFAULT 'reference'::"public"."enum_footer_legal_links_link_type",
    "link_new_tab" boolean,
    "link_url" character varying,
    "link_label" character varying NOT NULL
);


ALTER TABLE "public"."footer_legal_links" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."footer_navigation_sections" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "title" character varying NOT NULL
);


ALTER TABLE "public"."footer_navigation_sections" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."footer_navigation_sections_links" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "link_type" "public"."enum_footer_navigation_sections_links_link_type" DEFAULT 'reference'::"public"."enum_footer_navigation_sections_links_link_type",
    "link_new_tab" boolean,
    "link_url" character varying,
    "link_label" character varying NOT NULL
);


ALTER TABLE "public"."footer_navigation_sections_links" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."footer_rels" (
    "id" integer NOT NULL,
    "order" integer,
    "parent_id" integer NOT NULL,
    "path" character varying NOT NULL,
    "articles_id" integer,
    "categories_id" integer,
    "pages_id" integer
);


ALTER TABLE "public"."footer_rels" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."footer_rels_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."footer_rels_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."footer_rels_id_seq" OWNED BY "public"."footer_rels"."id";



CREATE TABLE IF NOT EXISTS "public"."footer_social_links" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "platform" "public"."enum_footer_social_links_platform" NOT NULL,
    "url" character varying NOT NULL,
    "label" character varying NOT NULL
);


ALTER TABLE "public"."footer_social_links" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."header" (
    "id" integer NOT NULL,
    "updated_at" timestamp(3) with time zone,
    "created_at" timestamp(3) with time zone
);


ALTER TABLE "public"."header" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."header_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."header_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."header_id_seq" OWNED BY "public"."header"."id";



CREATE TABLE IF NOT EXISTS "public"."header_nav_items" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "link_type" "public"."enum_header_nav_items_link_type" DEFAULT 'reference'::"public"."enum_header_nav_items_link_type",
    "link_new_tab" boolean,
    "link_url" character varying,
    "link_label" character varying NOT NULL
);


ALTER TABLE "public"."header_nav_items" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."header_rels" (
    "id" integer NOT NULL,
    "order" integer,
    "parent_id" integer NOT NULL,
    "path" character varying NOT NULL,
    "articles_id" integer,
    "categories_id" integer,
    "pages_id" integer
);


ALTER TABLE "public"."header_rels" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."header_rels_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."header_rels_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."header_rels_id_seq" OWNED BY "public"."header_rels"."id";



CREATE TABLE IF NOT EXISTS "public"."keywords" (
    "id" integer NOT NULL,
    "keyword" character varying NOT NULL,
    "english_keyword" character varying NOT NULL,
    "is_active" boolean DEFAULT true,
    "usage_count" numeric DEFAULT 0,
    "description" character varying,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."keywords" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."keywords_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."keywords_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."keywords_id_seq" OWNED BY "public"."keywords"."id";



CREATE TABLE IF NOT EXISTS "public"."media" (
    "id" integer NOT NULL,
    "alt" character varying NOT NULL,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "url" character varying,
    "thumbnail_u_r_l" character varying,
    "filename" character varying,
    "mime_type" character varying,
    "filesize" numeric,
    "width" numeric,
    "height" numeric,
    "focal_x" numeric,
    "focal_y" numeric,
    "sizes_thumbnail_url" character varying,
    "sizes_thumbnail_width" numeric,
    "sizes_thumbnail_height" numeric,
    "sizes_thumbnail_mime_type" character varying,
    "sizes_thumbnail_filesize" numeric,
    "sizes_thumbnail_filename" character varying,
    "sizes_card_url" character varying,
    "sizes_card_width" numeric,
    "sizes_card_height" numeric,
    "sizes_card_mime_type" character varying,
    "sizes_card_filesize" numeric,
    "sizes_card_filename" character varying,
    "sizes_feature_url" character varying,
    "sizes_feature_width" numeric,
    "sizes_feature_height" numeric,
    "sizes_feature_mime_type" character varying,
    "sizes_feature_filesize" numeric,
    "sizes_feature_filename" character varying
);


ALTER TABLE "public"."media" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."media_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."media_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."media_id_seq" OWNED BY "public"."media"."id";



CREATE TABLE IF NOT EXISTS "public"."pages" (
    "id" integer NOT NULL,
    "title" character varying,
    "has_german_translation" boolean DEFAULT false,
    "english_tab_title" character varying,
    "english_tab_content" "jsonb",
    "german_tab_german_title" character varying,
    "german_tab_german_content" "jsonb",
    "meta_title" character varying,
    "meta_image_id" integer,
    "meta_description" character varying,
    "published_at" timestamp(3) with time zone,
    "slug" character varying,
    "slug_lock" boolean DEFAULT true,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "_status" "public"."enum_pages_status" DEFAULT 'draft'::"public"."enum_pages_status",
    "featured_image_id" integer,
    "parent_id" integer,
    "enable_breadcrumbs" boolean DEFAULT true
);


ALTER TABLE "public"."pages" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."pages_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."pages_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."pages_id_seq" OWNED BY "public"."pages"."id";



CREATE TABLE IF NOT EXISTS "public"."payload_locked_documents" (
    "id" integer NOT NULL,
    "global_slug" character varying,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."payload_locked_documents" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."payload_locked_documents_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."payload_locked_documents_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."payload_locked_documents_id_seq" OWNED BY "public"."payload_locked_documents"."id";



CREATE TABLE IF NOT EXISTS "public"."payload_locked_documents_rels" (
    "id" integer NOT NULL,
    "order" integer,
    "parent_id" integer NOT NULL,
    "path" character varying NOT NULL,
    "users_id" integer,
    "articles_id" integer,
    "categories_id" integer,
    "pages_id" integer,
    "keywords_id" integer,
    "processed_urls_id" integer,
    "rss_feeds_id" integer,
    "media_id" integer
);


ALTER TABLE "public"."payload_locked_documents_rels" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."payload_locked_documents_rels_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."payload_locked_documents_rels_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."payload_locked_documents_rels_id_seq" OWNED BY "public"."payload_locked_documents_rels"."id";



CREATE TABLE IF NOT EXISTS "public"."payload_migrations" (
    "id" integer NOT NULL,
    "name" character varying,
    "batch" numeric,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."payload_migrations" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."payload_migrations_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."payload_migrations_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."payload_migrations_id_seq" OWNED BY "public"."payload_migrations"."id";



CREATE TABLE IF NOT EXISTS "public"."payload_preferences" (
    "id" integer NOT NULL,
    "key" character varying,
    "value" "jsonb",
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."payload_preferences" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."payload_preferences_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."payload_preferences_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."payload_preferences_id_seq" OWNED BY "public"."payload_preferences"."id";



CREATE TABLE IF NOT EXISTS "public"."payload_preferences_rels" (
    "id" integer NOT NULL,
    "order" integer,
    "parent_id" integer NOT NULL,
    "path" character varying NOT NULL,
    "users_id" integer
);


ALTER TABLE "public"."payload_preferences_rels" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."payload_preferences_rels_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."payload_preferences_rels_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."payload_preferences_rels_id_seq" OWNED BY "public"."payload_preferences_rels"."id";



CREATE TABLE IF NOT EXISTS "public"."processed_urls" (
    "id" integer NOT NULL,
    "url" character varying NOT NULL,
    "status" "public"."enum_processed_urls_status" DEFAULT 'pending'::"public"."enum_processed_urls_status" NOT NULL,
    "title" character varying,
    "publication_date" timestamp(3) with time zone,
    "feed_id_id" integer,
    "processed_at" timestamp(3) with time zone,
    "reason" character varying,
    "article_id_id" integer,
    "attempt_count" numeric DEFAULT 0,
    "last_attempt_at" timestamp(3) with time zone,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."processed_urls" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."processed_urls_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."processed_urls_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."processed_urls_id_seq" OWNED BY "public"."processed_urls"."id";



CREATE TABLE IF NOT EXISTS "public"."rss_feeds" (
    "id" integer NOT NULL,
    "name" character varying NOT NULL,
    "url" character varying NOT NULL,
    "is_active" boolean DEFAULT true,
    "language" "public"."enum_rss_feeds_language" DEFAULT 'de'::"public"."enum_rss_feeds_language",
    "last_processed" timestamp(3) with time zone,
    "last_checked" timestamp(3) with time zone,
    "last_successful_check" timestamp(3) with time zone,
    "items_processed" numeric DEFAULT 0,
    "items_accepted" numeric DEFAULT 0,
    "articles_found_since_last_successful" numeric,
    "total_articles_accepted" numeric,
    "error_count" numeric DEFAULT 0,
    "last_error_message" character varying,
    "processing_frequency" numeric DEFAULT 60,
    "priority" "public"."enum_rss_feeds_priority" DEFAULT 'medium'::"public"."enum_rss_feeds_priority",
    "firecrawl_options_remove_base64_images" boolean DEFAULT true,
    "firecrawl_options_block_ads" boolean DEFAULT true,
    "keyword_filtering_strict_keyword_matching" boolean,
    "processing_options_max_firecrawl_scrape" numeric,
    "processing_options_max_articles_per_run" numeric,
    "processing_options_skip_translation" boolean,
    "processing_options_skip_enhancement" boolean,
    "processing_options_custom_timeout" numeric,
    "processing_options_enable_stealth" boolean,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."rss_feeds" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."rss_feeds_firecrawl_options_exclude_tags" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "tag" character varying NOT NULL
);


ALTER TABLE "public"."rss_feeds_firecrawl_options_exclude_tags" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."rss_feeds_firecrawl_options_include_tags" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "tag" character varying NOT NULL
);


ALTER TABLE "public"."rss_feeds_firecrawl_options_include_tags" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."rss_feeds_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."rss_feeds_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."rss_feeds_id_seq" OWNED BY "public"."rss_feeds"."id";



CREATE TABLE IF NOT EXISTS "public"."rss_feeds_keyword_filtering_custom_keywords" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "keyword" character varying NOT NULL,
    "english_keyword" character varying NOT NULL,
    "weight" numeric DEFAULT 5
);


ALTER TABLE "public"."rss_feeds_keyword_filtering_custom_keywords" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."users" (
    "id" integer NOT NULL,
    "name" character varying,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "email" character varying NOT NULL,
    "reset_password_token" character varying,
    "reset_password_expiration" timestamp(3) with time zone,
    "salt" character varying,
    "hash" character varying,
    "login_attempts" numeric DEFAULT 0,
    "lock_until" timestamp(3) with time zone
);


ALTER TABLE "public"."users" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."users_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."users_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."users_id_seq" OWNED BY "public"."users"."id";



ALTER TABLE ONLY "public"."_articles_v" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_articles_v_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_articles_v_rels" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_articles_v_rels_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_articles_v_version_english_tab_enhanced_key_insights" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_articles_v_version_english_tab_enhanced_key_insights_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_articles_v_version_english_tab_keywords" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_articles_v_version_english_tab_keywords_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_articles_v_version_german_tab_german_key_insights" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_articles_v_version_german_tab_german_key_insights_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_articles_v_version_german_tab_german_keywords" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_articles_v_version_german_tab_german_keywords_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_articles_v_version_related_companies" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_articles_v_version_related_companies_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."articles" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."articles_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."articles_rels" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."articles_rels_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."categories" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."categories_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."footer" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."footer_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."footer_rels" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."footer_rels_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."header" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."header_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."header_rels" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."header_rels_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."keywords" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."keywords_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."media" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."media_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."pages" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."pages_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."payload_locked_documents" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."payload_locked_documents_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."payload_locked_documents_rels" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."payload_locked_documents_rels_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."payload_migrations" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."payload_migrations_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."payload_preferences" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."payload_preferences_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."payload_preferences_rels" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."payload_preferences_rels_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."processed_urls" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."processed_urls_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."rss_feeds" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."rss_feeds_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."users" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."users_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_articles_v"
    ADD CONSTRAINT "_articles_v_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_articles_v_rels"
    ADD CONSTRAINT "_articles_v_rels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_articles_v_version_english_tab_enhanced_key_insights"
    ADD CONSTRAINT "_articles_v_version_english_tab_enhanced_key_insights_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_articles_v_version_english_tab_keywords"
    ADD CONSTRAINT "_articles_v_version_english_tab_keywords_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_articles_v_version_german_tab_german_key_insights"
    ADD CONSTRAINT "_articles_v_version_german_tab_german_key_insights_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_articles_v_version_german_tab_german_keywords"
    ADD CONSTRAINT "_articles_v_version_german_tab_german_keywords_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_articles_v_version_related_companies"
    ADD CONSTRAINT "_articles_v_version_related_companies_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v"
    ADD CONSTRAINT "_pages_v_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."articles_english_tab_enhanced_key_insights"
    ADD CONSTRAINT "articles_english_tab_enhanced_key_insights_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."articles_english_tab_keywords"
    ADD CONSTRAINT "articles_english_tab_keywords_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."articles_german_tab_german_key_insights"
    ADD CONSTRAINT "articles_german_tab_german_key_insights_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."articles_german_tab_german_keywords"
    ADD CONSTRAINT "articles_german_tab_german_keywords_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."articles"
    ADD CONSTRAINT "articles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."articles_related_companies"
    ADD CONSTRAINT "articles_related_companies_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."articles_rels"
    ADD CONSTRAINT "articles_rels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."categories"
    ADD CONSTRAINT "categories_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."footer_legal_links"
    ADD CONSTRAINT "footer_legal_links_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."footer_navigation_sections_links"
    ADD CONSTRAINT "footer_navigation_sections_links_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."footer_navigation_sections"
    ADD CONSTRAINT "footer_navigation_sections_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."footer"
    ADD CONSTRAINT "footer_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."footer_rels"
    ADD CONSTRAINT "footer_rels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."footer_social_links"
    ADD CONSTRAINT "footer_social_links_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."header_nav_items"
    ADD CONSTRAINT "header_nav_items_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."header"
    ADD CONSTRAINT "header_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."header_rels"
    ADD CONSTRAINT "header_rels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."keywords"
    ADD CONSTRAINT "keywords_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."media"
    ADD CONSTRAINT "media_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages"
    ADD CONSTRAINT "pages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payload_locked_documents"
    ADD CONSTRAINT "payload_locked_documents_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payload_migrations"
    ADD CONSTRAINT "payload_migrations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payload_preferences"
    ADD CONSTRAINT "payload_preferences_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payload_preferences_rels"
    ADD CONSTRAINT "payload_preferences_rels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."processed_urls"
    ADD CONSTRAINT "processed_urls_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."rss_feeds_firecrawl_options_exclude_tags"
    ADD CONSTRAINT "rss_feeds_firecrawl_options_exclude_tags_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."rss_feeds_firecrawl_options_include_tags"
    ADD CONSTRAINT "rss_feeds_firecrawl_options_include_tags_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."rss_feeds_keyword_filtering_custom_keywords"
    ADD CONSTRAINT "rss_feeds_keyword_filtering_custom_keywords_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."rss_feeds"
    ADD CONSTRAINT "rss_feeds_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");



CREATE INDEX "_articles_v_created_at_idx" ON "public"."_articles_v" USING "btree" ("created_at");



CREATE INDEX "_articles_v_latest_idx" ON "public"."_articles_v" USING "btree" ("latest");



CREATE INDEX "_articles_v_parent_idx" ON "public"."_articles_v" USING "btree" ("parent_id");



CREATE INDEX "_articles_v_rels_categories_id_idx" ON "public"."_articles_v_rels" USING "btree" ("categories_id");



CREATE INDEX "_articles_v_rels_order_idx" ON "public"."_articles_v_rels" USING "btree" ("order");



CREATE INDEX "_articles_v_rels_parent_idx" ON "public"."_articles_v_rels" USING "btree" ("parent_id");



CREATE INDEX "_articles_v_rels_path_idx" ON "public"."_articles_v_rels" USING "btree" ("path");



CREATE INDEX "_articles_v_updated_at_idx" ON "public"."_articles_v" USING "btree" ("updated_at");



CREATE INDEX "_articles_v_version_english_tab_enhanced_key_insights_order_idx" ON "public"."_articles_v_version_english_tab_enhanced_key_insights" USING "btree" ("_order");



CREATE INDEX "_articles_v_version_english_tab_enhanced_key_insights_parent_id" ON "public"."_articles_v_version_english_tab_enhanced_key_insights" USING "btree" ("_parent_id");



CREATE INDEX "_articles_v_version_english_tab_keywords_order_idx" ON "public"."_articles_v_version_english_tab_keywords" USING "btree" ("_order");



CREATE INDEX "_articles_v_version_english_tab_keywords_parent_id_idx" ON "public"."_articles_v_version_english_tab_keywords" USING "btree" ("_parent_id");



CREATE INDEX "_articles_v_version_german_tab_german_key_insights_order_idx" ON "public"."_articles_v_version_german_tab_german_key_insights" USING "btree" ("_order");



CREATE INDEX "_articles_v_version_german_tab_german_key_insights_parent_id_id" ON "public"."_articles_v_version_german_tab_german_key_insights" USING "btree" ("_parent_id");



CREATE INDEX "_articles_v_version_german_tab_german_keywords_order_idx" ON "public"."_articles_v_version_german_tab_german_keywords" USING "btree" ("_order");



CREATE INDEX "_articles_v_version_german_tab_german_keywords_parent_id_idx" ON "public"."_articles_v_version_german_tab_german_keywords" USING "btree" ("_parent_id");



CREATE INDEX "_articles_v_version_meta_version_meta_image_idx" ON "public"."_articles_v" USING "btree" ("version_meta_image_id");



CREATE INDEX "_articles_v_version_related_companies_order_idx" ON "public"."_articles_v_version_related_companies" USING "btree" ("_order");



CREATE INDEX "_articles_v_version_related_companies_parent_id_idx" ON "public"."_articles_v_version_related_companies" USING "btree" ("_parent_id");



CREATE INDEX "_articles_v_version_sources_tab_version_sources_tab_source_feed" ON "public"."_articles_v" USING "btree" ("version_sources_tab_source_feed_id");



CREATE INDEX "_articles_v_version_version__status_idx" ON "public"."_articles_v" USING "btree" ("version__status");



CREATE INDEX "_articles_v_version_version_created_at_idx" ON "public"."_articles_v" USING "btree" ("version_created_at");



CREATE INDEX "_articles_v_version_version_featured_image_idx" ON "public"."_articles_v" USING "btree" ("version_featured_image_id");



CREATE INDEX "_articles_v_version_version_published_by_idx" ON "public"."_articles_v" USING "btree" ("version_published_by_id");



CREATE INDEX "_articles_v_version_version_trending_idx" ON "public"."_articles_v" USING "btree" ("version_trending");



CREATE INDEX "_articles_v_version_version_updated_at_idx" ON "public"."_articles_v" USING "btree" ("version_updated_at");



CREATE INDEX "_pages_v_created_at_idx" ON "public"."_pages_v" USING "btree" ("created_at");



CREATE INDEX "_pages_v_latest_idx" ON "public"."_pages_v" USING "btree" ("latest");



CREATE INDEX "_pages_v_parent_idx" ON "public"."_pages_v" USING "btree" ("parent_id");



CREATE INDEX "_pages_v_updated_at_idx" ON "public"."_pages_v" USING "btree" ("updated_at");



CREATE INDEX "_pages_v_version_meta_version_meta_image_idx" ON "public"."_pages_v" USING "btree" ("version_meta_image_id");



CREATE INDEX "_pages_v_version_version__status_idx" ON "public"."_pages_v" USING "btree" ("version__status");



CREATE INDEX "_pages_v_version_version_created_at_idx" ON "public"."_pages_v" USING "btree" ("version_created_at");



CREATE INDEX "_pages_v_version_version_featured_image_idx" ON "public"."_pages_v" USING "btree" ("version_featured_image_id");



CREATE INDEX "_pages_v_version_version_parent_idx" ON "public"."_pages_v" USING "btree" ("version_parent_id");



CREATE INDEX "_pages_v_version_version_slug_idx" ON "public"."_pages_v" USING "btree" ("version_slug");



CREATE INDEX "_pages_v_version_version_updated_at_idx" ON "public"."_pages_v" USING "btree" ("version_updated_at");



CREATE INDEX "articles__status_idx" ON "public"."articles" USING "btree" ("_status");



CREATE INDEX "articles_created_at_idx" ON "public"."articles" USING "btree" ("created_at");



CREATE INDEX "articles_english_tab_enhanced_key_insights_order_idx" ON "public"."articles_english_tab_enhanced_key_insights" USING "btree" ("_order");



CREATE INDEX "articles_english_tab_enhanced_key_insights_parent_id_idx" ON "public"."articles_english_tab_enhanced_key_insights" USING "btree" ("_parent_id");



CREATE INDEX "articles_english_tab_keywords_order_idx" ON "public"."articles_english_tab_keywords" USING "btree" ("_order");



CREATE INDEX "articles_english_tab_keywords_parent_id_idx" ON "public"."articles_english_tab_keywords" USING "btree" ("_parent_id");



CREATE INDEX "articles_featured_image_idx" ON "public"."articles" USING "btree" ("featured_image_id");



CREATE INDEX "articles_german_tab_german_key_insights_order_idx" ON "public"."articles_german_tab_german_key_insights" USING "btree" ("_order");



CREATE INDEX "articles_german_tab_german_key_insights_parent_id_idx" ON "public"."articles_german_tab_german_key_insights" USING "btree" ("_parent_id");



CREATE INDEX "articles_german_tab_german_keywords_order_idx" ON "public"."articles_german_tab_german_keywords" USING "btree" ("_order");



CREATE INDEX "articles_german_tab_german_keywords_parent_id_idx" ON "public"."articles_german_tab_german_keywords" USING "btree" ("_parent_id");



CREATE INDEX "articles_meta_meta_image_idx" ON "public"."articles" USING "btree" ("meta_image_id");



CREATE INDEX "articles_published_by_idx" ON "public"."articles" USING "btree" ("published_by_id");



CREATE INDEX "articles_related_companies_order_idx" ON "public"."articles_related_companies" USING "btree" ("_order");



CREATE INDEX "articles_related_companies_parent_id_idx" ON "public"."articles_related_companies" USING "btree" ("_parent_id");



CREATE INDEX "articles_rels_categories_id_idx" ON "public"."articles_rels" USING "btree" ("categories_id");



CREATE INDEX "articles_rels_order_idx" ON "public"."articles_rels" USING "btree" ("order");



CREATE INDEX "articles_rels_parent_idx" ON "public"."articles_rels" USING "btree" ("parent_id");



CREATE INDEX "articles_rels_path_idx" ON "public"."articles_rels" USING "btree" ("path");



CREATE INDEX "articles_sources_tab_sources_tab_source_feed_idx" ON "public"."articles" USING "btree" ("sources_tab_source_feed_id");



CREATE INDEX "articles_trending_idx" ON "public"."articles" USING "btree" ("trending");



CREATE INDEX "articles_updated_at_idx" ON "public"."articles" USING "btree" ("updated_at");



CREATE INDEX "categories_created_at_idx" ON "public"."categories" USING "btree" ("created_at");



CREATE UNIQUE INDEX "categories_slug_idx" ON "public"."categories" USING "btree" ("slug");



CREATE INDEX "categories_updated_at_idx" ON "public"."categories" USING "btree" ("updated_at");



CREATE INDEX "footer_legal_links_order_idx" ON "public"."footer_legal_links" USING "btree" ("_order");



CREATE INDEX "footer_legal_links_parent_id_idx" ON "public"."footer_legal_links" USING "btree" ("_parent_id");



CREATE INDEX "footer_logo_logo_image_idx" ON "public"."footer" USING "btree" ("logo_image_id");



CREATE INDEX "footer_navigation_sections_links_order_idx" ON "public"."footer_navigation_sections_links" USING "btree" ("_order");



CREATE INDEX "footer_navigation_sections_links_parent_id_idx" ON "public"."footer_navigation_sections_links" USING "btree" ("_parent_id");



CREATE INDEX "footer_navigation_sections_order_idx" ON "public"."footer_navigation_sections" USING "btree" ("_order");



CREATE INDEX "footer_navigation_sections_parent_id_idx" ON "public"."footer_navigation_sections" USING "btree" ("_parent_id");



CREATE INDEX "footer_rels_articles_id_idx" ON "public"."footer_rels" USING "btree" ("articles_id");



CREATE INDEX "footer_rels_categories_id_idx" ON "public"."footer_rels" USING "btree" ("categories_id");



CREATE INDEX "footer_rels_order_idx" ON "public"."footer_rels" USING "btree" ("order");



CREATE INDEX "footer_rels_pages_id_idx" ON "public"."footer_rels" USING "btree" ("pages_id");



CREATE INDEX "footer_rels_parent_idx" ON "public"."footer_rels" USING "btree" ("parent_id");



CREATE INDEX "footer_rels_path_idx" ON "public"."footer_rels" USING "btree" ("path");



CREATE INDEX "footer_social_links_order_idx" ON "public"."footer_social_links" USING "btree" ("_order");



CREATE INDEX "footer_social_links_parent_id_idx" ON "public"."footer_social_links" USING "btree" ("_parent_id");



CREATE INDEX "header_nav_items_order_idx" ON "public"."header_nav_items" USING "btree" ("_order");



CREATE INDEX "header_nav_items_parent_id_idx" ON "public"."header_nav_items" USING "btree" ("_parent_id");



CREATE INDEX "header_rels_articles_id_idx" ON "public"."header_rels" USING "btree" ("articles_id");



CREATE INDEX "header_rels_categories_id_idx" ON "public"."header_rels" USING "btree" ("categories_id");



CREATE INDEX "header_rels_order_idx" ON "public"."header_rels" USING "btree" ("order");



CREATE INDEX "header_rels_pages_id_idx" ON "public"."header_rels" USING "btree" ("pages_id");



CREATE INDEX "header_rels_parent_idx" ON "public"."header_rels" USING "btree" ("parent_id");



CREATE INDEX "header_rels_path_idx" ON "public"."header_rels" USING "btree" ("path");



CREATE INDEX "keywords_created_at_idx" ON "public"."keywords" USING "btree" ("created_at");



CREATE UNIQUE INDEX "keywords_keyword_idx" ON "public"."keywords" USING "btree" ("keyword");



CREATE INDEX "keywords_updated_at_idx" ON "public"."keywords" USING "btree" ("updated_at");



CREATE INDEX "media_created_at_idx" ON "public"."media" USING "btree" ("created_at");



CREATE UNIQUE INDEX "media_filename_idx" ON "public"."media" USING "btree" ("filename");



CREATE INDEX "media_sizes_card_sizes_card_filename_idx" ON "public"."media" USING "btree" ("sizes_card_filename");



CREATE INDEX "media_sizes_feature_sizes_feature_filename_idx" ON "public"."media" USING "btree" ("sizes_feature_filename");



CREATE INDEX "media_sizes_thumbnail_sizes_thumbnail_filename_idx" ON "public"."media" USING "btree" ("sizes_thumbnail_filename");



CREATE INDEX "media_updated_at_idx" ON "public"."media" USING "btree" ("updated_at");



CREATE INDEX "pages__status_idx" ON "public"."pages" USING "btree" ("_status");



CREATE INDEX "pages_created_at_idx" ON "public"."pages" USING "btree" ("created_at");



CREATE INDEX "pages_featured_image_idx" ON "public"."pages" USING "btree" ("featured_image_id");



CREATE INDEX "pages_meta_meta_image_idx" ON "public"."pages" USING "btree" ("meta_image_id");



CREATE INDEX "pages_parent_idx" ON "public"."pages" USING "btree" ("parent_id");



CREATE UNIQUE INDEX "pages_slug_idx" ON "public"."pages" USING "btree" ("slug");



CREATE INDEX "pages_updated_at_idx" ON "public"."pages" USING "btree" ("updated_at");



CREATE INDEX "payload_locked_documents_created_at_idx" ON "public"."payload_locked_documents" USING "btree" ("created_at");



CREATE INDEX "payload_locked_documents_global_slug_idx" ON "public"."payload_locked_documents" USING "btree" ("global_slug");



CREATE INDEX "payload_locked_documents_rels_articles_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("articles_id");



CREATE INDEX "payload_locked_documents_rels_categories_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("categories_id");



CREATE INDEX "payload_locked_documents_rels_keywords_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("keywords_id");



CREATE INDEX "payload_locked_documents_rels_media_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("media_id");



CREATE INDEX "payload_locked_documents_rels_order_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("order");



CREATE INDEX "payload_locked_documents_rels_pages_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("pages_id");



CREATE INDEX "payload_locked_documents_rels_parent_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("parent_id");



CREATE INDEX "payload_locked_documents_rels_path_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("path");



CREATE INDEX "payload_locked_documents_rels_processed_urls_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("processed_urls_id");



CREATE INDEX "payload_locked_documents_rels_rss_feeds_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("rss_feeds_id");



CREATE INDEX "payload_locked_documents_rels_users_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("users_id");



CREATE INDEX "payload_locked_documents_updated_at_idx" ON "public"."payload_locked_documents" USING "btree" ("updated_at");



CREATE INDEX "payload_migrations_created_at_idx" ON "public"."payload_migrations" USING "btree" ("created_at");



CREATE INDEX "payload_migrations_updated_at_idx" ON "public"."payload_migrations" USING "btree" ("updated_at");



CREATE INDEX "payload_preferences_created_at_idx" ON "public"."payload_preferences" USING "btree" ("created_at");



CREATE INDEX "payload_preferences_key_idx" ON "public"."payload_preferences" USING "btree" ("key");



CREATE INDEX "payload_preferences_rels_order_idx" ON "public"."payload_preferences_rels" USING "btree" ("order");



CREATE INDEX "payload_preferences_rels_parent_idx" ON "public"."payload_preferences_rels" USING "btree" ("parent_id");



CREATE INDEX "payload_preferences_rels_path_idx" ON "public"."payload_preferences_rels" USING "btree" ("path");



CREATE INDEX "payload_preferences_rels_users_id_idx" ON "public"."payload_preferences_rels" USING "btree" ("users_id");



CREATE INDEX "payload_preferences_updated_at_idx" ON "public"."payload_preferences" USING "btree" ("updated_at");



CREATE INDEX "processed_urls_article_id_idx" ON "public"."processed_urls" USING "btree" ("article_id_id");



CREATE INDEX "processed_urls_created_at_idx" ON "public"."processed_urls" USING "btree" ("created_at");



CREATE INDEX "processed_urls_feed_id_idx" ON "public"."processed_urls" USING "btree" ("feed_id_id");



CREATE INDEX "processed_urls_updated_at_idx" ON "public"."processed_urls" USING "btree" ("updated_at");



CREATE UNIQUE INDEX "processed_urls_url_idx" ON "public"."processed_urls" USING "btree" ("url");



CREATE INDEX "rss_feeds_created_at_idx" ON "public"."rss_feeds" USING "btree" ("created_at");



CREATE INDEX "rss_feeds_firecrawl_options_exclude_tags_order_idx" ON "public"."rss_feeds_firecrawl_options_exclude_tags" USING "btree" ("_order");



CREATE INDEX "rss_feeds_firecrawl_options_exclude_tags_parent_id_idx" ON "public"."rss_feeds_firecrawl_options_exclude_tags" USING "btree" ("_parent_id");



CREATE INDEX "rss_feeds_firecrawl_options_include_tags_order_idx" ON "public"."rss_feeds_firecrawl_options_include_tags" USING "btree" ("_order");



CREATE INDEX "rss_feeds_firecrawl_options_include_tags_parent_id_idx" ON "public"."rss_feeds_firecrawl_options_include_tags" USING "btree" ("_parent_id");



CREATE INDEX "rss_feeds_keyword_filtering_custom_keywords_order_idx" ON "public"."rss_feeds_keyword_filtering_custom_keywords" USING "btree" ("_order");



CREATE INDEX "rss_feeds_keyword_filtering_custom_keywords_parent_id_idx" ON "public"."rss_feeds_keyword_filtering_custom_keywords" USING "btree" ("_parent_id");



CREATE INDEX "rss_feeds_updated_at_idx" ON "public"."rss_feeds" USING "btree" ("updated_at");



CREATE UNIQUE INDEX "rss_feeds_url_idx" ON "public"."rss_feeds" USING "btree" ("url");



CREATE INDEX "users_created_at_idx" ON "public"."users" USING "btree" ("created_at");



CREATE UNIQUE INDEX "users_email_idx" ON "public"."users" USING "btree" ("email");



CREATE INDEX "users_updated_at_idx" ON "public"."users" USING "btree" ("updated_at");



ALTER TABLE ONLY "public"."_articles_v"
    ADD CONSTRAINT "_articles_v_parent_id_articles_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."articles"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_articles_v_rels"
    ADD CONSTRAINT "_articles_v_rels_categories_fk" FOREIGN KEY ("categories_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_articles_v_rels"
    ADD CONSTRAINT "_articles_v_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."_articles_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_articles_v_version_english_tab_enhanced_key_insights"
    ADD CONSTRAINT "_articles_v_version_english_tab_enhanced_key_insights_parent_id" FOREIGN KEY ("_parent_id") REFERENCES "public"."_articles_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_articles_v_version_english_tab_keywords"
    ADD CONSTRAINT "_articles_v_version_english_tab_keywords_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_articles_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_articles_v"
    ADD CONSTRAINT "_articles_v_version_featured_image_id_media_id_fk" FOREIGN KEY ("version_featured_image_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_articles_v_version_german_tab_german_key_insights"
    ADD CONSTRAINT "_articles_v_version_german_tab_german_key_insights_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_articles_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_articles_v_version_german_tab_german_keywords"
    ADD CONSTRAINT "_articles_v_version_german_tab_german_keywords_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_articles_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_articles_v"
    ADD CONSTRAINT "_articles_v_version_meta_image_id_media_id_fk" FOREIGN KEY ("version_meta_image_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_articles_v"
    ADD CONSTRAINT "_articles_v_version_published_by_id_users_id_fk" FOREIGN KEY ("version_published_by_id") REFERENCES "public"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_articles_v_version_related_companies"
    ADD CONSTRAINT "_articles_v_version_related_companies_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_articles_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_articles_v"
    ADD CONSTRAINT "_articles_v_version_sources_tab_source_feed_id_rss_feeds_id_fk" FOREIGN KEY ("version_sources_tab_source_feed_id") REFERENCES "public"."rss_feeds"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v"
    ADD CONSTRAINT "_pages_v_parent_id_pages_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."pages"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v"
    ADD CONSTRAINT "_pages_v_version_featured_image_id_media_id_fk" FOREIGN KEY ("version_featured_image_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v"
    ADD CONSTRAINT "_pages_v_version_meta_image_id_media_id_fk" FOREIGN KEY ("version_meta_image_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v"
    ADD CONSTRAINT "_pages_v_version_parent_id_pages_id_fk" FOREIGN KEY ("version_parent_id") REFERENCES "public"."pages"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."articles_english_tab_enhanced_key_insights"
    ADD CONSTRAINT "articles_english_tab_enhanced_key_insights_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."articles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."articles_english_tab_keywords"
    ADD CONSTRAINT "articles_english_tab_keywords_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."articles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."articles"
    ADD CONSTRAINT "articles_featured_image_id_media_id_fk" FOREIGN KEY ("featured_image_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."articles_german_tab_german_key_insights"
    ADD CONSTRAINT "articles_german_tab_german_key_insights_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."articles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."articles_german_tab_german_keywords"
    ADD CONSTRAINT "articles_german_tab_german_keywords_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."articles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."articles"
    ADD CONSTRAINT "articles_meta_image_id_media_id_fk" FOREIGN KEY ("meta_image_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."articles"
    ADD CONSTRAINT "articles_published_by_id_users_id_fk" FOREIGN KEY ("published_by_id") REFERENCES "public"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."articles_related_companies"
    ADD CONSTRAINT "articles_related_companies_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."articles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."articles_rels"
    ADD CONSTRAINT "articles_rels_categories_fk" FOREIGN KEY ("categories_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."articles_rels"
    ADD CONSTRAINT "articles_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."articles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."articles"
    ADD CONSTRAINT "articles_sources_tab_source_feed_id_rss_feeds_id_fk" FOREIGN KEY ("sources_tab_source_feed_id") REFERENCES "public"."rss_feeds"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."footer_legal_links"
    ADD CONSTRAINT "footer_legal_links_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."footer"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."footer"
    ADD CONSTRAINT "footer_logo_image_id_media_id_fk" FOREIGN KEY ("logo_image_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."footer_navigation_sections_links"
    ADD CONSTRAINT "footer_navigation_sections_links_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."footer_navigation_sections"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."footer_navigation_sections"
    ADD CONSTRAINT "footer_navigation_sections_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."footer"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."footer_rels"
    ADD CONSTRAINT "footer_rels_articles_fk" FOREIGN KEY ("articles_id") REFERENCES "public"."articles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."footer_rels"
    ADD CONSTRAINT "footer_rels_categories_fk" FOREIGN KEY ("categories_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."footer_rels"
    ADD CONSTRAINT "footer_rels_pages_fk" FOREIGN KEY ("pages_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."footer_rels"
    ADD CONSTRAINT "footer_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."footer"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."footer_social_links"
    ADD CONSTRAINT "footer_social_links_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."footer"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."header_nav_items"
    ADD CONSTRAINT "header_nav_items_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."header"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."header_rels"
    ADD CONSTRAINT "header_rels_articles_fk" FOREIGN KEY ("articles_id") REFERENCES "public"."articles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."header_rels"
    ADD CONSTRAINT "header_rels_categories_fk" FOREIGN KEY ("categories_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."header_rels"
    ADD CONSTRAINT "header_rels_pages_fk" FOREIGN KEY ("pages_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."header_rels"
    ADD CONSTRAINT "header_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."header"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages"
    ADD CONSTRAINT "pages_featured_image_id_media_id_fk" FOREIGN KEY ("featured_image_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pages"
    ADD CONSTRAINT "pages_meta_image_id_media_id_fk" FOREIGN KEY ("meta_image_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pages"
    ADD CONSTRAINT "pages_parent_id_pages_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."pages"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_articles_fk" FOREIGN KEY ("articles_id") REFERENCES "public"."articles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_categories_fk" FOREIGN KEY ("categories_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_keywords_fk" FOREIGN KEY ("keywords_id") REFERENCES "public"."keywords"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_media_fk" FOREIGN KEY ("media_id") REFERENCES "public"."media"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_pages_fk" FOREIGN KEY ("pages_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."payload_locked_documents"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_processed_urls_fk" FOREIGN KEY ("processed_urls_id") REFERENCES "public"."processed_urls"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_rss_feeds_fk" FOREIGN KEY ("rss_feeds_id") REFERENCES "public"."rss_feeds"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_users_fk" FOREIGN KEY ("users_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_preferences_rels"
    ADD CONSTRAINT "payload_preferences_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."payload_preferences"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_preferences_rels"
    ADD CONSTRAINT "payload_preferences_rels_users_fk" FOREIGN KEY ("users_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."processed_urls"
    ADD CONSTRAINT "processed_urls_article_id_id_articles_id_fk" FOREIGN KEY ("article_id_id") REFERENCES "public"."articles"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."processed_urls"
    ADD CONSTRAINT "processed_urls_feed_id_id_rss_feeds_id_fk" FOREIGN KEY ("feed_id_id") REFERENCES "public"."rss_feeds"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."rss_feeds_firecrawl_options_exclude_tags"
    ADD CONSTRAINT "rss_feeds_firecrawl_options_exclude_tags_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."rss_feeds"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."rss_feeds_firecrawl_options_include_tags"
    ADD CONSTRAINT "rss_feeds_firecrawl_options_include_tags_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."rss_feeds"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."rss_feeds_keyword_filtering_custom_keywords"
    ADD CONSTRAINT "rss_feeds_keyword_filtering_custom_keywords_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."rss_feeds"("id") ON DELETE CASCADE;



GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";



GRANT ALL ON TABLE "public"."_articles_v" TO "anon";
GRANT ALL ON TABLE "public"."_articles_v" TO "authenticated";
GRANT ALL ON TABLE "public"."_articles_v" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_articles_v_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_articles_v_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_articles_v_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_articles_v_rels" TO "anon";
GRANT ALL ON TABLE "public"."_articles_v_rels" TO "authenticated";
GRANT ALL ON TABLE "public"."_articles_v_rels" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_articles_v_rels_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_articles_v_rels_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_articles_v_rels_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_articles_v_version_english_tab_enhanced_key_insights" TO "anon";
GRANT ALL ON TABLE "public"."_articles_v_version_english_tab_enhanced_key_insights" TO "authenticated";
GRANT ALL ON TABLE "public"."_articles_v_version_english_tab_enhanced_key_insights" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_articles_v_version_english_tab_enhanced_key_insights_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_articles_v_version_english_tab_enhanced_key_insights_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_articles_v_version_english_tab_enhanced_key_insights_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_articles_v_version_english_tab_keywords" TO "anon";
GRANT ALL ON TABLE "public"."_articles_v_version_english_tab_keywords" TO "authenticated";
GRANT ALL ON TABLE "public"."_articles_v_version_english_tab_keywords" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_articles_v_version_english_tab_keywords_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_articles_v_version_english_tab_keywords_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_articles_v_version_english_tab_keywords_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_articles_v_version_german_tab_german_key_insights" TO "anon";
GRANT ALL ON TABLE "public"."_articles_v_version_german_tab_german_key_insights" TO "authenticated";
GRANT ALL ON TABLE "public"."_articles_v_version_german_tab_german_key_insights" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_articles_v_version_german_tab_german_key_insights_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_articles_v_version_german_tab_german_key_insights_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_articles_v_version_german_tab_german_key_insights_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_articles_v_version_german_tab_german_keywords" TO "anon";
GRANT ALL ON TABLE "public"."_articles_v_version_german_tab_german_keywords" TO "authenticated";
GRANT ALL ON TABLE "public"."_articles_v_version_german_tab_german_keywords" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_articles_v_version_german_tab_german_keywords_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_articles_v_version_german_tab_german_keywords_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_articles_v_version_german_tab_german_keywords_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_articles_v_version_related_companies" TO "anon";
GRANT ALL ON TABLE "public"."_articles_v_version_related_companies" TO "authenticated";
GRANT ALL ON TABLE "public"."_articles_v_version_related_companies" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_articles_v_version_related_companies_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_articles_v_version_related_companies_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_articles_v_version_related_companies_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."articles" TO "anon";
GRANT ALL ON TABLE "public"."articles" TO "authenticated";
GRANT ALL ON TABLE "public"."articles" TO "service_role";



GRANT ALL ON TABLE "public"."articles_english_tab_enhanced_key_insights" TO "anon";
GRANT ALL ON TABLE "public"."articles_english_tab_enhanced_key_insights" TO "authenticated";
GRANT ALL ON TABLE "public"."articles_english_tab_enhanced_key_insights" TO "service_role";



GRANT ALL ON TABLE "public"."articles_english_tab_keywords" TO "anon";
GRANT ALL ON TABLE "public"."articles_english_tab_keywords" TO "authenticated";
GRANT ALL ON TABLE "public"."articles_english_tab_keywords" TO "service_role";



GRANT ALL ON TABLE "public"."articles_german_tab_german_key_insights" TO "anon";
GRANT ALL ON TABLE "public"."articles_german_tab_german_key_insights" TO "authenticated";
GRANT ALL ON TABLE "public"."articles_german_tab_german_key_insights" TO "service_role";



GRANT ALL ON TABLE "public"."articles_german_tab_german_keywords" TO "anon";
GRANT ALL ON TABLE "public"."articles_german_tab_german_keywords" TO "authenticated";
GRANT ALL ON TABLE "public"."articles_german_tab_german_keywords" TO "service_role";



GRANT ALL ON SEQUENCE "public"."articles_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."articles_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."articles_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."articles_related_companies" TO "anon";
GRANT ALL ON TABLE "public"."articles_related_companies" TO "authenticated";
GRANT ALL ON TABLE "public"."articles_related_companies" TO "service_role";



GRANT ALL ON TABLE "public"."articles_rels" TO "anon";
GRANT ALL ON TABLE "public"."articles_rels" TO "authenticated";
GRANT ALL ON TABLE "public"."articles_rels" TO "service_role";



GRANT ALL ON SEQUENCE "public"."articles_rels_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."articles_rels_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."articles_rels_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."categories" TO "anon";
GRANT ALL ON TABLE "public"."categories" TO "authenticated";
GRANT ALL ON TABLE "public"."categories" TO "service_role";



GRANT ALL ON SEQUENCE "public"."categories_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."categories_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."categories_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."footer" TO "anon";
GRANT ALL ON TABLE "public"."footer" TO "authenticated";
GRANT ALL ON TABLE "public"."footer" TO "service_role";



GRANT ALL ON SEQUENCE "public"."footer_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."footer_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."footer_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."footer_legal_links" TO "anon";
GRANT ALL ON TABLE "public"."footer_legal_links" TO "authenticated";
GRANT ALL ON TABLE "public"."footer_legal_links" TO "service_role";



GRANT ALL ON TABLE "public"."footer_navigation_sections" TO "anon";
GRANT ALL ON TABLE "public"."footer_navigation_sections" TO "authenticated";
GRANT ALL ON TABLE "public"."footer_navigation_sections" TO "service_role";



GRANT ALL ON TABLE "public"."footer_navigation_sections_links" TO "anon";
GRANT ALL ON TABLE "public"."footer_navigation_sections_links" TO "authenticated";
GRANT ALL ON TABLE "public"."footer_navigation_sections_links" TO "service_role";



GRANT ALL ON TABLE "public"."footer_rels" TO "anon";
GRANT ALL ON TABLE "public"."footer_rels" TO "authenticated";
GRANT ALL ON TABLE "public"."footer_rels" TO "service_role";



GRANT ALL ON SEQUENCE "public"."footer_rels_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."footer_rels_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."footer_rels_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."footer_social_links" TO "anon";
GRANT ALL ON TABLE "public"."footer_social_links" TO "authenticated";
GRANT ALL ON TABLE "public"."footer_social_links" TO "service_role";



GRANT ALL ON TABLE "public"."header" TO "anon";
GRANT ALL ON TABLE "public"."header" TO "authenticated";
GRANT ALL ON TABLE "public"."header" TO "service_role";



GRANT ALL ON SEQUENCE "public"."header_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."header_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."header_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."header_nav_items" TO "anon";
GRANT ALL ON TABLE "public"."header_nav_items" TO "authenticated";
GRANT ALL ON TABLE "public"."header_nav_items" TO "service_role";



GRANT ALL ON TABLE "public"."header_rels" TO "anon";
GRANT ALL ON TABLE "public"."header_rels" TO "authenticated";
GRANT ALL ON TABLE "public"."header_rels" TO "service_role";



GRANT ALL ON SEQUENCE "public"."header_rels_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."header_rels_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."header_rels_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."keywords" TO "anon";
GRANT ALL ON TABLE "public"."keywords" TO "authenticated";
GRANT ALL ON TABLE "public"."keywords" TO "service_role";



GRANT ALL ON SEQUENCE "public"."keywords_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."keywords_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."keywords_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."media" TO "anon";
GRANT ALL ON TABLE "public"."media" TO "authenticated";
GRANT ALL ON TABLE "public"."media" TO "service_role";



GRANT ALL ON SEQUENCE "public"."media_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."media_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."media_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."pages" TO "anon";
GRANT ALL ON TABLE "public"."pages" TO "authenticated";
GRANT ALL ON TABLE "public"."pages" TO "service_role";



GRANT ALL ON SEQUENCE "public"."pages_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."pages_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."pages_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."payload_locked_documents" TO "anon";
GRANT ALL ON TABLE "public"."payload_locked_documents" TO "authenticated";
GRANT ALL ON TABLE "public"."payload_locked_documents" TO "service_role";



GRANT ALL ON SEQUENCE "public"."payload_locked_documents_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."payload_locked_documents_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."payload_locked_documents_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."payload_locked_documents_rels" TO "anon";
GRANT ALL ON TABLE "public"."payload_locked_documents_rels" TO "authenticated";
GRANT ALL ON TABLE "public"."payload_locked_documents_rels" TO "service_role";



GRANT ALL ON SEQUENCE "public"."payload_locked_documents_rels_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."payload_locked_documents_rels_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."payload_locked_documents_rels_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."payload_migrations" TO "anon";
GRANT ALL ON TABLE "public"."payload_migrations" TO "authenticated";
GRANT ALL ON TABLE "public"."payload_migrations" TO "service_role";



GRANT ALL ON SEQUENCE "public"."payload_migrations_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."payload_migrations_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."payload_migrations_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."payload_preferences" TO "anon";
GRANT ALL ON TABLE "public"."payload_preferences" TO "authenticated";
GRANT ALL ON TABLE "public"."payload_preferences" TO "service_role";



GRANT ALL ON SEQUENCE "public"."payload_preferences_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."payload_preferences_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."payload_preferences_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."payload_preferences_rels" TO "anon";
GRANT ALL ON TABLE "public"."payload_preferences_rels" TO "authenticated";
GRANT ALL ON TABLE "public"."payload_preferences_rels" TO "service_role";



GRANT ALL ON SEQUENCE "public"."payload_preferences_rels_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."payload_preferences_rels_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."payload_preferences_rels_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."processed_urls" TO "anon";
GRANT ALL ON TABLE "public"."processed_urls" TO "authenticated";
GRANT ALL ON TABLE "public"."processed_urls" TO "service_role";



GRANT ALL ON SEQUENCE "public"."processed_urls_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."processed_urls_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."processed_urls_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."rss_feeds" TO "anon";
GRANT ALL ON TABLE "public"."rss_feeds" TO "authenticated";
GRANT ALL ON TABLE "public"."rss_feeds" TO "service_role";



GRANT ALL ON TABLE "public"."rss_feeds_firecrawl_options_exclude_tags" TO "anon";
GRANT ALL ON TABLE "public"."rss_feeds_firecrawl_options_exclude_tags" TO "authenticated";
GRANT ALL ON TABLE "public"."rss_feeds_firecrawl_options_exclude_tags" TO "service_role";



GRANT ALL ON TABLE "public"."rss_feeds_firecrawl_options_include_tags" TO "anon";
GRANT ALL ON TABLE "public"."rss_feeds_firecrawl_options_include_tags" TO "authenticated";
GRANT ALL ON TABLE "public"."rss_feeds_firecrawl_options_include_tags" TO "service_role";



GRANT ALL ON SEQUENCE "public"."rss_feeds_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."rss_feeds_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."rss_feeds_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."rss_feeds_keyword_filtering_custom_keywords" TO "anon";
GRANT ALL ON TABLE "public"."rss_feeds_keyword_filtering_custom_keywords" TO "authenticated";
GRANT ALL ON TABLE "public"."rss_feeds_keyword_filtering_custom_keywords" TO "service_role";



GRANT ALL ON TABLE "public"."users" TO "anon";
GRANT ALL ON TABLE "public"."users" TO "authenticated";
GRANT ALL ON TABLE "public"."users" TO "service_role";



GRANT ALL ON SEQUENCE "public"."users_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."users_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."users_id_seq" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "service_role";






RESET ALL;
